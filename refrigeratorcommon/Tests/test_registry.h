/*!
 * @file
 * @brief Test registry for automatic test discovery
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved.
 */

#ifndef TEST_REGISTRY_H
#define TEST_REGISTRY_H

#include <stddef.h>

// Test function type
typedef int (*TestGroupFunction)(void);

// Test group registration structure
typedef struct {
    const char *name;
    TestGroupFunction function;
} TestGroup_t;

// Maximum number of test groups
#define MAX_TEST_GROUPS 32

// Global test registry
extern TestGroup_t g_test_registry[];
extern size_t g_test_count;

// Macro to register a test group
#define REGISTER_TEST_GROUP(group_name, test_func) \
    static void __attribute__((constructor)) register_##group_name(void) { \
        if (g_test_count < MAX_TEST_GROUPS) { \
            g_test_registry[g_test_count].name = #group_name; \
            g_test_registry[g_test_count].function = test_func; \
            g_test_count++; \
        } \
    }

// Function to run all registered tests
int run_all_registered_tests(void);

#endif // TEST_REGISTRY_H
