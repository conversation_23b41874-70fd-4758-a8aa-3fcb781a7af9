/*!
 * @file
 * @brief
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved.
 */

#include "TinyTimeSource_TestDouble.h"
#include "utils.h"

static TinyTimeSourceTickCount_t GetTicks(I_TinyTimeSource_t *_instance)
{
   REINTERPRET(instance, _instance, TinyTimeSource_TestDouble_t *);
   return instance->_private.ticks;
}

static const I_TinyTimeSource_Api_t api =
   { GetTicks };

void TinyTimeSource_TestDouble_Init(TinyTimeSource_TestDouble_t *instance)
{
   instance->interface.api = &api;
   instance->_private.ticks = 0;
}

void TinyTimeSource_TestDouble_SetTicks(TinyTimeSource_TestDouble_t *instance, TinyTimeSourceTickCount_t ticks)
{
   instance->_private.ticks = ticks;
}

void TinyTimeSource_TestDouble_TickOnce(TinyTimeSource_TestDouble_t *instance)
{
   instance->_private.ticks++;
}

void TinyTimeSource_TestDouble_TickMany(TinyTimeSource_TestDouble_t *instance, TinyTimeSourceTickCount_t ticks)
{
   instance->_private.ticks += ticks;
}
