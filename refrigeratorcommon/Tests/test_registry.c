/*!
 * @file
 * @brief Test registry implementation
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved.
 */

#include "test_registry.h"
#include <stdio.h>

// Global test registry
TestGroup_t g_test_registry[MAX_TEST_GROUPS];
size_t g_test_count = 0;

int run_all_registered_tests(void)
{
    int total_result = 0;

    printf("=== Running All Test Groups ===\n\n");

    for(size_t i = 0; i < g_test_count; i++)
    {
        printf("--- %s Tests ---\n", g_test_registry[i].name);
        int result = g_test_registry[i].function();
        total_result += result;
        printf("\n");
    }

    printf("=== Test Summary ===\n");
    if(total_result == 0)
    {
        printf("All tests (total %zu groups) PASSED!\n", g_test_count);
    }
    else
    {
        printf("Total FAILED tests: %d\n", total_result);
    }

    return total_result;
}
