#!/bin/bash
rm -rf  /home/<USER>/coverity/
make clean
cov-build --dir /home/<USER>/coverity/result --tmpdir /home/<USER>/coverity/tmp --encoding UTF-8 make
cov-analyze --dir /home/<USER>/coverity/result --strip-path ~/my_share/2ndsoftwarearchitecture_new/refrigeratorcommon/ \
--coding-standard-config /home/<USER>/ext/Downloads/cov-analysis-linux64-2024.3.1/config/coding-standards/misrac2012/misrac2012-all.config \
--jobs max8
cov-commit-defects --host ************ --dataport 443 --on-new-cert trust --stream demotest --dir /home/<USER>/coverity/result  \
--exclude-files '(/cmocka/.*)'  \
--user xxx --password xxxx
