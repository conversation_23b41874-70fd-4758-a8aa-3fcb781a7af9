# Default platform (x86)
PLATFORM ?= x86

# CMocka paths
CMOCKA_DIR = ../Tools/cmocka
CMOCKA_LIB = $(CMOCKA_DIR)/lib/libcmocka.a
CMOCKA_INCLUDE = $(CMOCKA_DIR)/include

CORE_DIR = ../Core
# Compiler and flags based on platform
ifeq ($(PLATFORM),arm)
    CC = arm-none-eabi-gcc
    AR = arm-none-eabi-ar
    CFLAGS = -mcpu=cortex-m0plus -mthumb -Wall -g -O0
    TARGET_DIR = arm_build
		BUILD_TESTS = 0
else
    CC = gcc
    AR = ar
    CFLAGS = -Wall -g -O0 -fPIC
    TARGET_DIR = x86_build
		BUILD_TESTS = 1
endif

INCLUDES = -I. -I$(CORE_DIR)/ -IUtils -I$(CORE_DIR)/DataStructures/LinkedList -I$(CORE_DIR)/DataStructures/Queue -I$(CORE_DIR)/DataStructures/RingBuffer -I$(CORE_DIR)/Timer
# Add CMocka include path for x86 builds with tests
ifeq ($(BUILD_TESTS),1)
    INCLUDES += -I$(CMOCKA_INCLUDE)
    CFLAGS += -DUNIT_TESTING
endif

# Source files for all tests
UTILS_SRCS = Utils/UtilsMacros_Test_CMocka.c
LINKLIST_SRCS = LinkList/TinyLinkedList_Test_CMocka.c
QUEUE_SRCS = Queue/Queue_RingBuffer_Test_CMocka.c
TIMER_SRCS = Timer/TinyTimer_Test_CMocka.c \
             Timer/TinyTimeSource_TestDouble.c

# Core source files
CORE_SRCS = $(CORE_DIR)/DataStructures/LinkedList/TinyLinkedList.c \
            $(CORE_DIR)/DataStructures/Queue/Queue_RingBuffer.c \
            $(CORE_DIR)/DataStructures/RingBuffer/RingBuffer.c \
	    $(CORE_DIR)/Timer/TinyTimer.c


ALL_TEST_SRCS = AllTests.c test_registry.c $(UTILS_SRCS) $(LINKLIST_SRCS) $(QUEUE_SRCS) $(TIMER_SRCS)

ALL_TEST_OBJS = $(patsubst %.c,$(TARGET_DIR)/%.o,$(ALL_TEST_SRCS))
CORE_OBJS = $(patsubst %.c,$(TARGET_DIR)/core/%.o,$(notdir $(CORE_SRCS)))

ALL_OBJS = $(ALL_TEST_OBJS) $(CORE_OBJS)

# Test binary
TEST_BIN = $(TARGET_DIR)/all_tests

# Default target
all: $(TARGET_DIR) tests

# Create build directory with subdirs
$(TARGET_DIR):
	mkdir -p $(TARGET_DIR)/Utils
	mkdir -p $(TARGET_DIR)/LinkList
	mkdir -p $(TARGET_DIR)/Queue
	mkdir -p $(TARGET_DIR)/core
	mkdir -p $(TARGET_DIR)/Timer

# Compile rules for different directories
$(TARGET_DIR)/%.o: %.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/Utils/%.o: Utils/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/LinkList/%.o: LinkList/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/Queue/%.o: Queue/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/Timer/%.o: Timer/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/core/%.o: $(CORE_DIR)/Timer/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/core/%.o: $(CORE_DIR)/DataStructures/LinkedList/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/core/%.o: $(CORE_DIR)/DataStructures/Queue/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

$(TARGET_DIR)/core/%.o: $(CORE_DIR)/DataStructures/RingBuffer/%.c | $(TARGET_DIR)
	$(CC) $(CFLAGS) $(INCLUDES) -c $< -o $@

ifeq ($(BUILD_TESTS),1)
# Build tests (x86 only)
tests: $(CMOCKA_LIB) $(TEST_BIN)

$(TEST_BIN): $(ALL_OBJS) $(CMOCKA_LIB)
	$(CC) -o $@ $(ALL_OBJS) $(CMOCKA_LIB)

run-tests: $(TEST_BIN)
	./$(TEST_BIN)
else
tests:
	@echo "Tests are only built for x86 platform"
endif

# Clean
clean:
	rm -rf x86_build arm_build

# Clean everything including CMocka
clean-all: clean
	rm -rf $(CMOCKA_DIR)/build $(CMOCKA_DIR)/install

# Build for both platforms
all-platforms: clean
	$(MAKE) PLATFORM=x86
	$(MAKE) PLATFORM=arm

# Test compilation only
test-compile: $(SRCS)
	$(CC) $(CFLAGS) $(INCLUDES) -c $(SRCS) -o /dev/null

.PHONY: all clean clean-all test-compile all-platforms tests run-tests
