/*!
 * @file
 * @brief User-defined assert mechanism
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#ifndef UASSERT_H_
#define UASSERT_H_

#include <stdbool.h>
#include <stdio.h>

#ifdef UNIT_TESTING
// For unit testing, use CMocka's mock_assert
extern void mock_assert(const int result, const char *const expression, const char *const file, const int line);

#define uassert(condition)                                  \
    do                                                      \
    {                                                       \
        if(!(condition))                                    \
        {                                                   \
            mock_assert(0, #condition, __FILE__, __LINE__); \
        }                                                   \
    } while(0)
#else
// For production code, use the original implementation
#define uassert(condition)                                     \
    do                                                         \
    {                                                          \
        if(!(condition))                                       \
        {                                                      \
            printf("Assertion failed: %s, file %s, line %d\n", \
                #condition,                                    \
                __FILE__,                                      \
                __LINE__);                                     \
            while(1)                                           \
                ; /* Infinite loop to halt execution */        \
        }                                                      \
    } while(0)
#endif

#endif
