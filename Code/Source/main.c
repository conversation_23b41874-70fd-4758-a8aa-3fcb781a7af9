/*!
 * @file
 * @brief Main program.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */

#include <stdint.h>
#include "Init_Mcu.h"
#include "adpt_iwdg.h"
#include "TinyTimer.h"
#include "Adpt_Timebase.h"
#include "SEGGER_RTT.h"

#define log_printf SEGGER_RTT_printf
#define log_WriteString SEGGER_RTT_WriteString

static TinyTimer_t st_TinyTimer1;
static TinyTimer_t st_TinyTimer2;

static uint32_t u32_count;

static void MyTinyTimerCallback(void *context)
{
    log_printf(0, "MyTinyTimerCallback 1 in isr count:%d\n", u32_count++);
}

static void MyTinyTimerCallback2(void *context)
{
    log_printf(0, "MyTinyTimerCallback 2 in isr count:%d\n", u32_count++);
}

int main(void)
{
    Init_Mcu();

    // Initialize SysTick time source with 1ms ticks
    SysTickTimeSource_Init();

    // Initialize tiny timer module with SysTick time source
    TinyTimerModule_Init();

    TinyTimerModule_StartPeriodic(&st_TinyTimer1, 1000, MyTinyTimerCallback, NULL);
    TinyTimerModule_StartPeriodic(&st_TinyTimer2, 1000, MyTinyTimerCallback2, NULL);

    while(1)
    {
        IWDG_Refesh();
        TinyTimerModule_Run();
    }

    return 0;
}
