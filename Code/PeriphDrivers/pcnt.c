/**
 *******************************************************************************
 * @file  pcnt.c
 * @brief This file provides firmware functions to manage the PCNT.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MADS            First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

/*******************************************************************************
 * Include files
 ******************************************************************************/
#include "pcnt.h"

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @defgroup DDL_PCNT PCNT模块驱动库
 * @brief PCNT Driver Library PCNT模块驱动库
 * @{
 */

/*******************************************************************************
 * Local type definitions ('typedef')
 ******************************************************************************/

/*******************************************************************************
 * Local pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions (declared in header file with 'extern')
 ******************************************************************************/

/*******************************************************************************
 * Local function prototypes ('static')
 ******************************************************************************/

/*******************************************************************************
 * Local variable definitions ('static')
 ******************************************************************************/

/*******************************************************************************
 * Function implementation - global ('extern') and local ('static')
 ******************************************************************************/
/**
 * @defgroup PCNT_Global_Functions PCNT全局函数定义
 * @{
 */

/**
 * @brief  PCNT的启动和停止控制
 * @param  [in] NewState TRUE:启动, FALSE:停止 @ref boolean_t
 * @retval boolean_t
 */
boolean_t Pcnt_Cmd(boolean_t NewState)
{
    SetBit((uint32_t)(&(M0P_PCNT->RUN)), 0, NewState);
    return GetBit((uint32_t)(&(M0P_PCNT->RUN)), 0);
}

/**
 * @brief  将BUF中的值同步到TOP
 * @param  [in] value 要同步到TOP的数值
 * @retval en_result_t
 *         - Ok: No error
 *         - ErrorTimeout: Time Out error occurred
 */
en_result_t Pcnt_SetB2T(uint16_t value)
{
    uint16_t u16TimeOut;

    u16TimeOut          = 1000;
    M0P_PCNT->BUF       = value;
    M0P_PCNT->CMD_f.B2T = 1;

    while (u16TimeOut--)
    {
        if (M0P_PCNT->SR2_f.B2T == FALSE)
        {
            break;
        }
        if (u16TimeOut == 0)
        {
            return ErrorTimeout;
        }
    }
    return Ok;
}

/**
 * @brief  将BUF中的值同步到CNT
 * @param  [in] value 要同步到CNT的数值
 * @retval en_result_t
 *         - Ok: No error
 *         - ErrorTimeout: Time Out error occurred
 */
en_result_t Pcnt_SetB2C(uint16_t value)
{
    uint16_t u16TimeOut;
    u16TimeOut          = 1000;
    M0P_PCNT->BUF       = value;
    M0P_PCNT->CMD_f.B2C = 1;

    while (u16TimeOut--)
    {
        if (M0P_PCNT->SR2_f.B2C == FALSE)
        {
            break;
        }
        if (u16TimeOut == 0)
        {
            return ErrorTimeout;
        }
    }
    return Ok;
}

/**
 * @brief  将TOP中的值同步到CNT
 * @retval en_result_t
 *         - Ok: No error
 *         - ErrorTimeout: Time Out error occurred
 */
en_result_t Pcnt_SetT2C(void)
{
    uint16_t u16TimeOut;
    u16TimeOut          = 1000;
    M0P_PCNT->CMD_f.T2C = 1;
    while (u16TimeOut--)
    {
        if (M0P_PCNT->SR2_f.T2C == FALSE)
        {
            break;
        }
        if (u16TimeOut == 0)
        {
            return ErrorTimeout;
        }
    }
    return Ok;
}

/**
 * @brief  赋值BUF
 * @param  [in] value 要赋值给BUF的数值
 * @retval None
 */
void Pcnt_SetBuf(uint16_t value)
{
    M0P_PCNT->BUF_f.BUF = value;
}

/**
 * @brief  初始化
 * @param  [in] InitStruct 初始化结构体 @ref stc_pcnt_initstruct_t
 * @retval None
 */
void Pcnt_Init(stc_pcnt_initstruct_t *InitStruct)
{
    M0P_PCNT->CTRL_f.S1P    = InitStruct->Pcnt_S1Sel;
    M0P_PCNT->CTRL_f.S0P    = InitStruct->Pcnt_S0Sel;
    M0P_PCNT->CTRL_f.CLKSEL = InitStruct->Pcnt_Clk;
    M0P_PCNT->CTRL_f.MODE   = InitStruct->Pcnt_Mode;
    if (InitStruct->Pcnt_Mode != PcntDoubleMode) /* 如果不是双通道正交脉冲计数模式 */
    {
        M0P_PCNT->CTRL_f.DIR = InitStruct->Pcnt_Dir;
    }
    M0P_PCNT->FLT_f.EN     = InitStruct->Pcnt_FltEn;
    M0P_PCNT->FLT_f.DEBTOP = InitStruct->Pcnt_DebTop;
    M0P_PCNT->FLT_f.CLKDIV = InitStruct->Pcnt_ClkDiv;
    M0P_PCNT->TOCR_f.EN    = InitStruct->Pcnt_TocrEn;
    M0P_PCNT->TOCR_f.TH    = InitStruct->Pcnt_TocrTh;

    M0P_PCNT->DBG_f.DBG = InitStruct->Pcnt_Dbg;
}

/**
 * @brief  配置中断源的使能
 * @param  [in] IT_Src 中断源再PCNT_IEN内部的位位置 @ref en_pcnt_itfce_t
 * @param  [in] NewState TRUE:使能, FALSE:禁止 @ref boolean_t
 * @retval None
 */
void Pcnt_ItCfg(en_pcnt_itfce_t IT_Src, boolean_t NewState)
{
    if (NewState == TRUE)
    {
        M0P_PCNT->IEN |= (uint32_t)(1 << IT_Src);
    }
    else if (NewState == FALSE)
    {
        M0P_PCNT->IEN &= ~(uint32_t)(1 << IT_Src);
    }
    else
    {
        ;
    }
}

/**
 * @brief  获取中断源的标志位
 * @param  [in] IT_Src 中断源标志位 @ref en_pcnt_itfce_t
 * @retval boolean_t
 *         - TRUE: 发生中断
 *         - FALSE: 没有发生中断
 */
boolean_t Pcnt_GetItStatus(en_pcnt_itfce_t IT_Src)
{
    return ((M0P_PCNT->IFR >> IT_Src) & 1u) > 0 ? TRUE : FALSE;
}

/**
 * @brief  清除中断源的标志位
 * @param  [in] IT_Src 中断源标志位 @ref en_pcnt_itfce_t
 * @retval None
 */
void Pcnt_ClrItStatus(en_pcnt_itfce_t IT_Src)
{
    M0P_PCNT->ICR &= ~(uint32_t)(1 << (uint32_t)IT_Src);
}

/**
 * @brief  获取PCNT_CNT寄存器的数值
 * @retval PCNT_CNT数值
 */
uint16_t Pcnt_GetCnt(void)
{
    return (uint16_t)(M0P_PCNT->CNT);
}

/**
 * @brief  获取PCNT_TOP寄存器的数值
 * @retval PCNT_TOP数值
 */
uint16_t Pcnt_GetTop(void)
{
    return (uint16_t)(M0P_PCNT->TOP);
}

/**
 * @brief  获取PCNT_BUF寄存器的数值
 * @retval PCNT_BUF数值
 */
uint16_t Pcnt_GetBuf(void)
{
    return (uint16_t)(M0P_PCNT->BUF);
}
/**
 * @}
 */

/**
 * @}
 */

/**
 * @}
 */

/******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
