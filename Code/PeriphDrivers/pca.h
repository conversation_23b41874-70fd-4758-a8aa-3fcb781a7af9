/**
 ******************************************************************************
 * @file   pca.h
 *
 * @brief This file contains all the functions prototypes of the PCA driver
 *        library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-15       MADS            First version
 @endverbatim
 ******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 ******************************************************************************
 */

#ifndef __PCA_H__
#define __PCA_H__

/* C binding of definitions if building with C++ compiler */
#ifdef __cplusplus
extern "C" {
#endif

/*****************************************************************************
 * Include files
 *****************************************************************************/
#include "ddl.h"

/**
 * @addtogroup HC32L186_DDL 驱动库
 * @{
 */

/**
 * @addtogroup DDL_PCA PCA模块驱动库
 * @{
 */

/*******************************************************************************
 * Global type definitions ('typedef')
 */
/**
 * @defgroup PCA_Global_Types PCA全局类型定义
 * @{
 */

/**
 * @brief PCA  模块选择
 */
typedef enum
{
    PcaModule0 = 0,      /*!<  PCA_0 */
    PcaModule1 = 1,      /*!<  PCA_1 */
    PcaModule2 = 2,      /*!<  PCA_2 */
    PcaModule3 = 3,      /*!<  PCA_3 */
    PcaModule4 = 4       /*!<  PCA_4 */
} en_pca_module_t;

/**
 * @brief PCA  中断标志位  中断清除位
 * @note       PCA_CCON中的CCF0-CCF4与CF; PCA_ICLR中的CCF0-CCF4与CF
 */
typedef enum
{
    PcaCcf0 = 0,      /*!<  PCA_0 比较/捕获标志位*/
    PcaCcf1 = 1,      /*!<  PCA_1 比较/捕获标志位*/
    PcaCcf2 = 2,      /*!<  PCA_2 比较/捕获标志位*/
    PcaCcf3 = 3,      /*!<  PCA_3 比较/捕获标志位*/
    PcaCcf4 = 4,      /*!<  PCA_4 比较/捕获标志位*/
    PcaCf   = 7       /*!<  PCA 计数器溢出标志 */
} en_pca_ccficlr_t;

/**
 * @brief PCA  时钟分频选择及时钟源选择
 * @note       PCA_CMOD   CPS[2:0]
 */
typedef enum
{
    PcaPclkdiv32 = 0,      /*!< PCLK/32 */
    PcaPclkdiv16 = 1,      /*!< PCLK/16 */
    PcaPclkdiv8  = 2,      /*!< PCLK/8 */
    PcaPclkdiv4  = 3,      /*!< PCLK/4 */
    PcaPclkdiv2  = 4,      /*!< PCLK/2 */
    PcaTim0ovf   = 5,      /*!< timer0 overflow */
    PcaTim1ovf   = 6,      /*!< timer1 overflow */
    PcaEci       = 7       /*!< ECI外部时钟 */
} en_pca_clksrc_t;

/**
 * @brief PCA  允许比较器功能控制
 * @note       PCA_CCAPMx   ECOM
 */
typedef enum
{
    PcaEcomDisable = 0,      /*!< 禁止比较器功能 */
    PcaEcomEnable  = 1       /*!< 允许比较器功能 */
} en_pca_ecom_t;

/**
 * @brief PCA  正沿捕获控制位
 * @note       PCA_CCAPMx  CAPP
 */
typedef enum
{
    PcaCappDisable = 0,       /*!< 禁止上升沿捕获 */
    PcaCappEnable  = 1        /*!< 允许上升沿捕获 */
} en_pca_capp_t;

/**
 * @brief PCA  负沿捕获控制位
 * @note       PCA_CCAPMx  CAPN
 */
typedef enum
{
    PcaCapnDisable = 0,       /*!< 禁止下降沿捕获 */
    PcaCapnEnable  = 1        /*!< 允许下降沿捕获 */
} en_pca_capn_t;

/**
 * @brief PCA  匹配控制位
 * @note       PCA_CCAPMx  MAT
 */
typedef enum
{
    PcaMatDisable = 0,       /*!< 禁止匹配功能 */
    PcaMatEnable  = 1        /*!< 允许匹配功能 */
} en_pca_mat_t;

/**
 * @brief PCA  翻转控制位
 * @note       PCA_CCAPMx  TOG
 */
typedef enum
{
    PcaTogDisable = 0,       /*!< 禁止翻转功能 */
    PcaTogEnable  = 1        /*!< 允许翻转功能 */
} en_pca_tog_t;

/**
 * @brief PCA  PWM控制位
 * @note       PCA_CCAPMx  PWM
 */
typedef enum
{
    PcaPwm8bitDisable = 0,       /*!< 禁止PWM脉宽调制功能 */
    PcaPwm8bitEnable  = 1        /*!< 允许PWM脉宽调制功能 */
} en_pca_pwm8bit_t;

/**
 * @brief PCA  中断使能制位
 * @note       PCA_CCAPMx  CCIE
 */
typedef enum
{
    PcaCcieDisable = 0,       /*!< 禁止比较/捕获中断 */
    PcaCcieEnable  = 1        /*!< 使能比较/捕获中断 */
} en_pca_ccie_t;

/**
 * @brief PCA  EPWM控制位
 * @note       PCA_EPWM  EPWM
 */
typedef enum
{
    PcaEpwmDisable = 0,       /*!< 禁止16 bit PWM */
    PcaEpwmEnable  = 1        /*!< 使能16 bit PWM */
} en_pca_epwm_t;

/**
 * @brief PCA  初始化配置的结构体
 * @note
 */
typedef struct
{
    en_pca_clksrc_t     pca_clksrc;           /*!< PCA_CMOD CPS[2:0]  */
    boolean_t           pca_cidl;             /*!< PCA_CMOD CIDL */
    en_pca_ecom_t       pca_ecom;             /*!< PCA_CCAPMx ECOM */
    en_pca_capp_t       pca_capp;             /*!< PCA_CCAPMx CAPP */
    en_pca_capn_t       pca_capn;             /*!< PCA_CCAPMx CAPN */
    en_pca_mat_t        pca_mat;              /*!< PCA_CCAPMx MAT */
    en_pca_tog_t        pca_tog;              /*!< PCA_CCAPMx TOG */
    en_pca_pwm8bit_t    pca_pwm;              /*!< PCA_CCAPMx PWM */
    en_pca_epwm_t       pca_epwm;             /*!< PCA_EPWM */
    uint16_t            pca_ccap;             /*!< PCA_CCAP */
    uint8_t             pca_ccapl;            /*!< PCA_CCAPL  PCA_CCAP的低字节 */
    uint8_t             pca_ccaph;            /*!< PCA_CCAPH  PCA_CCAP的高字节 */
    uint16_t            pca_carr;             /*!< PCA_CARR */
} stc_pcacfg_t;

/**
 * @}
 */

/*******************************************************************************
 * Global pre-processor symbols/macros ('#define')
 ******************************************************************************/

/*******************************************************************************
 * Global variable definitions ('extern')
 ******************************************************************************/

/*******************************************************************************
  Global function prototypes (definition in C source)
 ******************************************************************************/
/**
 * @addtogroup PCA_Global_Functions PCA全局函数定义
 * @{
 */
/* 中断标志获取 */
boolean_t Pca_GetItStatus(en_pca_ccficlr_t It_Src);
/* 清除中断标志 */
void Pca_ClrItStatus(en_pca_ccficlr_t It_Src);
/* 计数器运行控制 */
void Pca_StartPca(boolean_t NewStatus);
/* 空闲模式IDLE下，PCA是否停止工作设置 */
void Pca_SetCidl(boolean_t NewStatus);
/* @brief 模块4的看门狗使能控制 */
void Pca_Set4Wdte(boolean_t NewStatus);
/* 计数器中断控制 */
void Pca_ConfPcaIt(boolean_t NewStatus);
/* 比较捕获中断使能控制 */
void Pca_ConfModulexIt(en_pca_module_t Modulex, boolean_t NewStatus);
/* 模块0的初始化 */
void Pca_M0Init(stc_pcacfg_t *InitStruct);
/* 模块1的初始化 */
void Pca_M1Init(stc_pcacfg_t *InitStruct);
/* 模块2的初始化 */
void Pca_M2Init(stc_pcacfg_t *InitStruct);
/* 模块3的初始化 */
void Pca_M3Init(stc_pcacfg_t *InitStruct);
/* 模块4的初始化 */
void Pca_M4Init(stc_pcacfg_t *InitStruct);
/* 读取CNT寄存器的数值 */
uint16_t Pca_GetCnt(void);
/* 向CNT寄存器写入数值 */
void Pca_SetCnt(uint16_t cnt);
/* 返回指定通道比较高速输出标志寄存器的值 */
boolean_t Pca_GetOut(en_pca_module_t Modulex);
/* 设置比较捕获16位寄存器CCAPx数值 */
void Pca_SetCcap(en_pca_module_t Modulex, uint16_t Value);
/* 读取比较捕获16位寄存器CCAPx数值 */
uint16_t Pca_GetCcap(en_pca_module_t Modulex);
/* 设置自动重装载寄存器数值 */
void Pca_SetCarr(uint16_t Value);
/* 获取自动重装载寄存器数值 */
uint16_t Pca_GetCarr(void);
/* 设置比较捕获寄存器的高8位和低8位 */
void Pca_SetCcapHL(en_pca_module_t Modulex, uint8_t ValueH, uint8_t ValueL);
/* 读取比较捕获寄存器的高8位和低8位 */
void Pca_GetCcapHL(en_pca_module_t Modulex, uint8_t *ValueH, uint8_t *ValueL);

/**
 * @}
 */

/**
* @}
*/

/**
 * @}
 */

#ifdef __cplusplus
#endif

#endif /* __PCA_H__ */
/*******************************************************************************
 * EOF (not truncated)
 ******************************************************************************/
