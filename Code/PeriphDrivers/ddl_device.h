/**
 *******************************************************************************
 * @file  ddl_device.h
 * @brief This file contains all the functions prototypes of the device define driver
 *        library.
 @verbatim
   Change Logs:
   Date             Author          Notes
   2023-03-24       MADS            First version
 @endverbatim
 *******************************************************************************
 * Copyright (C) 2023, Xiaohua Semiconductor Co., Ltd. All rights reserved.
 *
 * This software component is licensed by XHSC under BSD 3-Clause license
 * (the "License"); You may not use this file except in compliance with the
 * License. You may obtain a copy of the License at:
 *                    opensource.org/licenses/BSD-3-Clause
 *
 *******************************************************************************
 */

#ifndef  __DDL_DEVICE_H__
#define  __DDL_DEVICE_H__

/**
 *******************************************************************************
 ** \brief Global device series definition
 **
 ** \note 
 ******************************************************************************/
#define DDL_MCU_SERIES       DDL_DEVICE_SERIES_HC32L186


/**
 *******************************************************************************
 ** \brief Global package definition
 **
 ** \note This definition is used for device package settings
 ******************************************************************************/
#define DDL_MCU_PACKAGE      DDL_DEVICE_PACKAGE_HC_K

#endif /* __DDL_DEVICE_H__ */

/*******************************************************************************
 * EOF (not truncated)                                                        
 ******************************************************************************/
