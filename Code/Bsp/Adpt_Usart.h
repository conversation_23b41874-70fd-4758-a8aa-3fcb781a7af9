/*!
 * @file
 * @brief This file defines public constants, types and functions for the usart.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _Adpt_USART_H_
#define _Adpt_USART_H_

#include <stdint.h>
#include "lpuart.h"
#include "uart.h"

#define USART_WIFI          M0P_UART3
#define USART_WIFI_IRQHandler Uart3_IRQHandler

void Board_InitUsart(void);
void LpUart0_SendOneData(uint8_t u8_data);
void LpUart0_EnalbeTxInterrupts(void);
void LpUart0_DisalbeTxInterrupts(void);
void LpUart0_EnalbeRxInterrupts(void);
void LpUart0_DisalbeRxInterrupts(void);
void TestUart_SendOneData(uint8_t u8_data);
void TestUart_EnalbeTxInterrupts(void);
void TestUart_DisalbeTxInterrupts(void);
void TestUart_EnalbeRxInterrupts(void);
void TestUart_DisalbeRxInterrupts(void);
void InverterUart_SendOneData(uint8_t u8_data);
void InverterUart_EnalbeTxInterrupts(void);
void InverterUart_DisalbeTxInterrupts(void);
void InverterUart_EnalbeRxInterrupts(void);
void InverterUart_DisalbeRxInterrupts(void);

void TestLog_SendOneData(uint8_t u8_data);
void Board_InitTestUsart(void);

#endif
