/*!
 * @file
 * @brief This file defines public constants, types and functions for the systick.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#ifndef _Adpt_TIME_BASE_H_
#define _Adpt_TIME_BASE_H_

#include "TinyTimer.h"

/*!
 * SysTick time source instance for HC32L186.
 */
typedef struct
{
   I_TinyTimeSource_t interface;
   
   struct
   {
      volatile TinyTimeSourceTickCount_t tickCount;
   } _private;
} SysTickTimeSource_t;

/*!
 * Initialize the SysTick time source.
 * @param instance The time source instance.
 */
void SysTickTimeSource_Init();

/**
 * @brief Get the SysTick time source instance.
 *
 * @returns The SysTick time source instance.
 */

// SysTickTimeSource_t * GetTimeSource(void);
I_TinyTimeSource_t* GetTinyTimeSource(void);

void Board_InitSysTick(void);

#endif
