/*!
 * @file
 * @brief System time base.
 *
 * Copyright Xiaomi Corporation - Confidential - All rights reserved
 */
#include <stdbool.h>
#include "Timebase.h"

#include "SystemTimerModule.h"


static uint8_t u8_Millisec;
static uint8_t u8_OneMsecCount;

void ISR_Timer_1ms(void)
{
    u8_Millisec++;
    Add_MSecCount();
}

void ISR_Timer_500us(void)
{
    u8_OneMsecCount++;
}

uint8_t Get_SystemMillisec(void)
{
    return u8_Millisec;
}
