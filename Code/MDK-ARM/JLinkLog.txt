T4A00 000:004.041   SEGGER J-Link V7.96a Log File
T4A00 000:004.240   DLL Compiled: Mar 14 2024 17:11:32
T4A00 000:004.245   Logging started @ 2025-08-04 12:21
T4A00 000:004.249   Process: d:\Keil_v5\UV4\UV4.exe
T4A00 000:004.256 - 4.253ms
T4A00 000:004.264 JLINK_SetWarnOutHandler(...)
T4A00 000:004.268 - 0.005ms
T4A00 000:004.273 JLINK_OpenEx(...)
T4A00 000:016.613   Firmware: J-Link V11 compiled Mar 14 2024 13:16:08
T4A00 000:035.388   Firmware: J-Link V11 compiled Mar 14 2024 13:16:08
T4A00 000:073.622   Hardware: V11.00
T4A00 000:073.671   S/N: 601012352
T4A00 000:073.684   OEM: SEGGER
T4A00 000:073.699   Feature(s): R<PERSON>, <PERSON><PERSON><PERSON>, FlashD<PERSON>, <PERSON><PERSON><PERSON>, GDB
T4A00 000:098.646   Bootloader: (Could not read)
T4A00 000:117.219   USB speed mode: High speed (480 MBit/s)
T4A00 000:123.741   TELNET listener socket opened on port 19021
T4A00 000:123.834   WEBSRV WEBSRV_Init(): Starting webserver thread(s)
T4A00 000:123.896   WEBSRV Failed to put socket into listener state (port 19080)
T4A00 000:123.943   WEBSRV Webserver running on local port 19081
T4A00 000:124.027   Looking for J-Link GUI Server exe at: d:\Keil_v5\ARM\Segger\JLinkGUIServer.exe
T4A00 000:124.065   Looking for J-Link GUI Server exe at: d:\Program Files\SEGGER\JLink_V796a\JLinkGUIServer.exe
T4A00 000:124.077   Forking J-Link GUI Server: d:\Program Files\SEGGER\JLink_V796a\JLinkGUIServer.exe
T4A00 000:132.139   J-Link GUI Server info: "J-Link GUI server V7.96a "
T4A00 000:132.703 - 128.422ms returns "O.K."
T4A00 000:132.744 JLINK_GetEmuCaps()
T4A00 000:132.750 - 0.005ms returns 0xB9FF7BBF
T4A00 000:132.772 JLINK_TIF_GetAvailable(...)
T4A00 000:135.859 - 3.087ms
T4A00 000:135.895 JLINK_SetErrorOutHandler(...)
T4A00 000:135.903 - 0.007ms
T4A00 000:135.933 JLINK_ExecCommand("ProjectFile = "D:\project\codestandards_demo2\Code\MDK-ARM\JLinkSettings.ini"", ...). 
T4A00 000:147.372   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\XHSC-JLinkDevices.xml
T4A00 000:148.738   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\XHSC-JLinkDevices.xml evaluated successfully.
T4A00 000:148.755     Device entry created:  CYCLONE V
T4A00 000:148.767       ChipInfo:
T4A00 000:148.774         Vendor:          Altera
T4A00 000:148.780         Name:            CYCLONE V
T4A00 000:148.787         Core:            JLINK_CORE_CORTEX_A9
T4A00 000:148.827         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Altera\Cyclone_V\Altera_Cyclone_V.JLinkScript
T4A00 000:148.838     Device entry modified: AMA3B1KK-KBR
T4A00 000:148.863       ChipInfo:
T4A00 000:148.870         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AmbiqMicro\AmbiqMicro_Apollo3.pex
T4A00 000:148.876     Device entry modified: AMA3B1KK-KCR
T4A00 000:148.897       ChipInfo:
T4A00 000:148.903         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AmbiqMicro\AmbiqMicro_Apollo3.pex
T4A00 000:148.911     Device entry modified: AMAPH1KK-KBR
T4A00 000:148.930       ChipInfo:
T4A00 000:148.937         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AmbiqMicro\AmbiqMicro_Apollo2.pex
T4A00 000:148.944     Device entry modified: AMAPH1KK-KCR
T4A00 000:148.963       ChipInfo:
T4A00 000:148.970         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AmbiqMicro\AmbiqMicro_Apollo2.pex
T4A00 000:148.978     Device entry modified: MSP3_AN524_M33
T4A00 000:148.999       ChipInfo:
T4A00 000:149.005         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ARM\SSE200-MPS3\ARM_SSE-200-MPS3_Core0.pex
T4A00 000:149.013     Device entry modified: MSP3_AN524_M33_0
T4A00 000:149.033       ChipInfo:
T4A00 000:149.039         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ARM\SSE200-MPS3\ARM_SSE-200-MPS3_Core0.pex
T4A00 000:149.046     Device entry modified: MSP3_AN524_M33_1
T4A00 000:149.065       ChipInfo:
T4A00 000:149.072         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ARM\SSE200-MPS3\ARM_SSE-200-MPS3_Core1.pex
T4A00 000:149.109     Device entry modified: ATSAMA5D21C
T4A00 000:149.133       ChipInfo:
T4A00 000:149.139         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:149.146     Device entry modified: ATSAMA5D22C
T4A00 000:149.166       ChipInfo:
T4A00 000:149.173         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:149.180     Device entry modified: ATSAMA5D23C
T4A00 000:149.199       ChipInfo:
T4A00 000:149.206         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:149.213     Device entry modified: ATSAMA5D24C
T4A00 000:149.232       ChipInfo:
T4A00 000:149.238         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:149.245     Device entry modified: ATSAMA5D25C
T4A00 000:149.265       ChipInfo:
T4A00 000:149.271         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:149.279     Device entry modified: ATSAMA5D26C
T4A00 000:149.298       ChipInfo:
T4A00 000:149.305         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:149.311     Device entry modified: ATSAMA5D27C
T4A00 000:149.336       ChipInfo:
T4A00 000:149.342         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:149.349       FlashBankInfo:
T4A00 000:149.354         Name:            QSPI Flash
T4A00 000:149.361         BaseAddr:        0xD0000000
T4A00 000:149.409     Device entry modified: ATSAMA5D28C
T4A00 000:149.431       ChipInfo:
T4A00 000:149.437         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:149.444     Device entry modified: ATSAMB11G18A
T4A00 000:149.449       FlashBankInfo:
T4A00 000:149.454         Name:            QSPI Flash
T4A00 000:149.460         BaseAddr:        0x60000000
T4A00 000:149.495     Device entry modified: ATSAMD51G18A
T4A00 000:149.520       ChipInfo:
T4A00 000:149.526         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.534     Device entry modified: ATSAMD51G19A
T4A00 000:149.553       ChipInfo:
T4A00 000:149.559         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.566     Device entry modified: ATSAMD51J18A
T4A00 000:149.585       ChipInfo:
T4A00 000:149.593         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.600     Device entry modified: ATSAMD51J19A
T4A00 000:149.619       ChipInfo:
T4A00 000:149.625         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.632     Device entry modified: ATSAMD51J20A
T4A00 000:149.651       ChipInfo:
T4A00 000:149.658         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.665     Device entry modified: ATSAMD51N19A
T4A00 000:149.683       ChipInfo:
T4A00 000:149.689         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.696     Device entry modified: ATSAMD51N20A
T4A00 000:149.714       ChipInfo:
T4A00 000:149.720         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.728     Device entry modified: ATSAMD51P19A
T4A00 000:149.747       ChipInfo:
T4A00 000:149.753         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.766     Device entry modified: ATSAMD51P20A
T4A00 000:149.785       ChipInfo:
T4A00 000:149.791         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.798     Device entry modified: ATSAME51J18A
T4A00 000:149.817       ChipInfo:
T4A00 000:149.823         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.829     Device entry modified: ATSAME51J19A
T4A00 000:149.848       ChipInfo:
T4A00 000:149.855         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.861     Device entry modified: ATSAME51N19A
T4A00 000:149.880       ChipInfo:
T4A00 000:149.886         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.893     Device entry modified: ATSAME51N20A
T4A00 000:149.912       ChipInfo:
T4A00 000:149.918         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.925     Device entry modified: ATSAME53J18A
T4A00 000:149.943       ChipInfo:
T4A00 000:149.950         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.957     Device entry modified: ATSAME53J19A
T4A00 000:149.977       ChipInfo:
T4A00 000:149.983         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:149.990     Device entry modified: ATSAME53J20A
T4A00 000:150.009       ChipInfo:
T4A00 000:150.015         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:150.022     Device entry modified: ATSAME53N19A
T4A00 000:150.041       ChipInfo:
T4A00 000:150.047         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:150.054     Device entry modified: ATSAME53N20A
T4A00 000:150.073       ChipInfo:
T4A00 000:150.079         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:150.086     Device entry modified: ATSAME54N19A
T4A00 000:150.105       ChipInfo:
T4A00 000:150.111         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:150.118     Device entry modified: ATSAME54N20A
T4A00 000:150.137       ChipInfo:
T4A00 000:150.143         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:150.150     Device entry modified: ATSAME54P19A
T4A00 000:150.169       ChipInfo:
T4A00 000:150.175         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:150.181     Device entry modified: ATSAME54P20A
T4A00 000:150.201       ChipInfo:
T4A00 000:150.207         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:150.214     Device entry modified: ATSAME70J19A
T4A00 000:150.233       ChipInfo:
T4A00 000:150.240         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:150.247     Device entry modified: ATSAME70J20A
T4A00 000:150.266       ChipInfo:
T4A00 000:150.272         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:150.279     Device entry modified: ATSAME70J21A
T4A00 000:150.297       ChipInfo:
T4A00 000:150.304         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:150.311     Device entry modified: ATSAME70N19A
T4A00 000:150.329       ChipInfo:
T4A00 000:150.335         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:150.343     Device entry modified: ATSAME70N20A
T4A00 000:150.362       ChipInfo:
T4A00 000:150.368         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:150.375     Device entry modified: ATSAME70N21A
T4A00 000:150.394       ChipInfo:
T4A00 000:150.401         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:150.407     Device entry modified: ATSAME70Q19A
T4A00 000:150.426       ChipInfo:
T4A00 000:150.432         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:150.439     Device entry modified: ATSAME70Q20A
T4A00 000:150.457       ChipInfo:
T4A00 000:150.463         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:150.470     Device entry modified: ATSAME70Q21A
T4A00 000:150.488       ChipInfo:
T4A00 000:150.495         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:150.502     Device entry modified: BCM43907
T4A00 000:150.522       ChipInfo:
T4A00 000:150.528         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Broadcom\BCM43907.JLinkScript
T4A00 000:150.536     Device entry modified: CR600
T4A00 000:150.557       ChipInfo:
T4A00 000:150.563         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ClouderSemi\CR600\CR600.JLinkScript
T4A00 000:150.569       FlashBankInfo:
T4A00 000:150.574         Name:            QSPI flash
T4A00 000:150.580         BaseAddr:        0x20000000
T4A00 000:150.614     Device entry modified: CYW43907
T4A00 000:150.639       ChipInfo:
T4A00 000:150.645         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Broadcom\BCM43907.JLinkScript
T4A00 000:150.653     Device entry modified: S6J328CK
T4A00 000:150.673       ChipInfo:
T4A00 000:150.679         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Cypress_S6J328.pex
T4A00 000:150.687     Device entry modified: S6J328CL
T4A00 000:150.706       ChipInfo:
T4A00 000:150.712         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Cypress_S6J328.pex
T4A00 000:150.719     Device entry modified: S6J324CKSM
T4A00 000:150.738       ChipInfo:
T4A00 000:150.744         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:150.751     Device entry modified: S6J331EKC
T4A00 000:150.777       ChipInfo:
T4A00 000:150.783         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:150.790     Device entry modified: S6J331EJA
T4A00 000:150.810       ChipInfo:
T4A00 000:150.817         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:150.823     Device entry modified: S6J331EKE
T4A00 000:150.843       ChipInfo:
T4A00 000:150.849         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:150.856     Device entry modified: S6J332CJB
T4A00 000:150.875       ChipInfo:
T4A00 000:150.881         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:150.887     Device entry modified: S6J332CJT
T4A00 000:150.907       ChipInfo:
T4A00 000:150.913         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:150.920     Device entry modified: S6J332CKS
T4A00 000:150.940       ChipInfo:
T4A00 000:150.946         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:150.967     Device entry modified: S6J332EJB
T4A00 000:150.989       ChipInfo:
T4A00 000:150.996         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.002     Device entry modified: S6J334BJD
T4A00 000:151.022       ChipInfo:
T4A00 000:151.028         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.035     Device entry modified: S6J334CHB
T4A00 000:151.054       ChipInfo:
T4A00 000:151.060         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.067     Device entry modified: S6J334CJE
T4A00 000:151.086       ChipInfo:
T4A00 000:151.093         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.100     Device entry modified: S6J334CJT
T4A00 000:151.120       ChipInfo:
T4A00 000:151.126         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.133     Device entry modified: S6J334CKS
T4A00 000:151.153       ChipInfo:
T4A00 000:151.159         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.166     Device entry modified: S6J334DJE
T4A00 000:151.185       ChipInfo:
T4A00 000:151.192         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.199     Device entry modified: S6J334DJT
T4A00 000:151.218       ChipInfo:
T4A00 000:151.224         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.231     Device entry modified: S6J334EJA
T4A00 000:151.251       ChipInfo:
T4A00 000:151.257         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.263     Device entry modified: S6J334EJE
T4A00 000:151.282       ChipInfo:
T4A00 000:151.289         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.295     Device entry modified: S6J334EJT
T4A00 000:151.315       ChipInfo:
T4A00 000:151.321         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:151.328     Device entry modified: CEC1702
T4A00 000:151.338       FlashBankInfo:
T4A00 000:151.344         Name:            SPI Flash
T4A00 000:151.350         BaseAddr:        0x60000000
T4A00 000:151.386     Device entry modified: nRF52832_xxAA
T4A00 000:151.501       ChipInfo:
T4A00 000:151.516         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NordicSemi\nRF52.pex
T4A00 000:151.528     Device entry modified: nRF52832_xxAB
T4A00 000:151.578       ChipInfo:
T4A00 000:151.587         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NordicSemi\nRF52.pex
T4A00 000:151.596     Device entry modified: nRF52840_xxAA
T4A00 000:151.641       ChipInfo:
T4A00 000:151.653         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NordicSemi\nRF52.pex
T4A00 000:151.663     Device entry modified: MCIMX6X1_A9
T4A00 000:151.702       ChipInfo:
T4A00 000:151.709         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6SX\iMX6SX_CortexA9.JLinkScript
T4A00 000:151.715       FlashBankInfo:
T4A00 000:151.721         Name:            QSPI Flash
T4A00 000:151.727         BaseAddr:        0x70000000
T4A00 000:151.774     Device entry modified: MCIMX6X1_M4
T4A00 000:151.798       ChipInfo:
T4A00 000:151.807         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6SX\iMX6SX_CortexM4.JLinkScript
T4A00 000:151.818     Device entry modified: MCIMX6X2_A9
T4A00 000:151.847       ChipInfo:
T4A00 000:151.853         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6SX\iMX6SX_CortexA9.JLinkScript
T4A00 000:151.859       FlashBankInfo:
T4A00 000:151.865         Name:            QSPI Flash
T4A00 000:151.871         BaseAddr:        0x70000000
T4A00 000:151.910     Device entry modified: MCIMX6X2_M4
T4A00 000:151.931       ChipInfo:
T4A00 000:151.937         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6SX\iMX6SX_CortexM4.JLinkScript
T4A00 000:151.945     Device entry modified: MCIMX6X3_A9
T4A00 000:151.964       ChipInfo:
T4A00 000:151.971         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6SX\iMX6SX_CortexA9.JLinkScript
T4A00 000:151.976       FlashBankInfo:
T4A00 000:151.982         Name:            QSPI Flash
T4A00 000:151.987         BaseAddr:        0x70000000
T4A00 000:152.020     Device entry modified: MCIMX6X3_M4
T4A00 000:152.042       ChipInfo:
T4A00 000:152.049         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6SX\iMX6SX_CortexM4.JLinkScript
T4A00 000:152.056     Device entry modified: MCIMX6X4_A9
T4A00 000:152.076       ChipInfo:
T4A00 000:152.082         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6SX\iMX6SX_CortexA9.JLinkScript
T4A00 000:152.088       FlashBankInfo:
T4A00 000:152.094         Name:            QSPI Flash
T4A00 000:152.099         BaseAddr:        0x70000000
T4A00 000:152.130     Device entry modified: MCIMX6X4_M4
T4A00 000:152.150       ChipInfo:
T4A00 000:152.157         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6SX\iMX6SX_CortexM4.JLinkScript
T4A00 000:152.164     Device entry modified: MCIMX6G0
T4A00 000:152.183       ChipInfo:
T4A00 000:152.190         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:152.195       FlashBankInfo:
T4A00 000:152.201         Name:            QSPI flash
T4A00 000:152.206         BaseAddr:        0x60000000
T4A00 000:152.239     Device entry modified: MCIMX6G1
T4A00 000:152.259       ChipInfo:
T4A00 000:152.265         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:152.271       FlashBankInfo:
T4A00 000:152.276         Name:            QSPI flash
T4A00 000:152.282         BaseAddr:        0x60000000
T4A00 000:152.313     Device entry modified: MCIMX6G2
T4A00 000:152.332       ChipInfo:
T4A00 000:152.338         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:152.344       FlashBankInfo:
T4A00 000:152.350         Name:            QSPI flash
T4A00 000:152.355         BaseAddr:        0x60000000
T4A00 000:152.385     Device entry modified: MCIMX6G3
T4A00 000:152.405       ChipInfo:
T4A00 000:152.411         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:152.417       FlashBankInfo:
T4A00 000:152.422         Name:            QSPI flash
T4A00 000:152.428         BaseAddr:        0x60000000
T4A00 000:152.458     Device entry modified: MCIMX6Y0
T4A00 000:152.478       ChipInfo:
T4A00 000:152.484         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:152.490       FlashBankInfo:
T4A00 000:152.495         Name:            QSPI flash
T4A00 000:152.501         BaseAddr:        0x60000000
T4A00 000:152.531     Device entry modified: MCIMX6Y1
T4A00 000:152.550       ChipInfo:
T4A00 000:152.556         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:152.564       FlashBankInfo:
T4A00 000:152.569         Name:            QSPI flash
T4A00 000:152.575         BaseAddr:        0x60000000
T4A00 000:152.606     Device entry modified: MCIMX6Y2
T4A00 000:152.625       ChipInfo:
T4A00 000:152.631         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:152.637       FlashBankInfo:
T4A00 000:152.642         Name:            QSPI flash
T4A00 000:152.648         BaseAddr:        0x60000000
T4A00 000:152.678     Device entry modified: MCIMX6Y7
T4A00 000:152.697       ChipInfo:
T4A00 000:152.704         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:152.709       FlashBankInfo:
T4A00 000:152.715         Name:            QSPI flash
T4A00 000:152.721         BaseAddr:        0x60000000
T4A00 000:152.752     Device entry modified: MCIMX7D3_A7_0
T4A00 000:152.777       ChipInfo:
T4A00 000:152.785         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_0.JLinkScript
T4A00 000:152.790       FlashBankInfo:
T4A00 000:152.796         Name:            QSPI flash
T4A00 000:152.801         BaseAddr:        0x60000000
T4A00 000:152.834     Device entry modified: MCIMX7D3_A7_1
T4A00 000:152.856       ChipInfo:
T4A00 000:152.862         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_1.JLinkScript
T4A00 000:152.868       FlashBankInfo:
T4A00 000:152.873         Name:            QSPI flash
T4A00 000:152.879         BaseAddr:        0x60000000
T4A00 000:152.911     Device entry modified: MCIMX7D3_M4
T4A00 000:152.915       ChipInfo:
T4A00 000:152.921         WorkRAMAddr:     0x00900000
T4A00 000:152.927         WorkRAMSize:     0x00020000
T4A00 000:152.949         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexM4.JLinkScript
T4A00 000:152.955       FlashBankInfo:
T4A00 000:152.961         Name:            QSPI flash
T4A00 000:152.966         BaseAddr:        0x60000000
T4A00 000:152.999     Device entry modified: MCIMX7D5_A7_0
T4A00 000:153.019       ChipInfo:
T4A00 000:153.025         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_0.JLinkScript
T4A00 000:153.031       FlashBankInfo:
T4A00 000:153.036         Name:            QSPI flash
T4A00 000:153.042         BaseAddr:        0x60000000
T4A00 000:153.073     Device entry modified: MCIMX7D5_A7_1
T4A00 000:153.093       ChipInfo:
T4A00 000:153.099         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_1.JLinkScript
T4A00 000:153.105       FlashBankInfo:
T4A00 000:153.110         Name:            QSPI flash
T4A00 000:153.116         BaseAddr:        0x60000000
T4A00 000:153.147     Device entry modified: MCIMX7D5_M4
T4A00 000:153.151       ChipInfo:
T4A00 000:153.157         WorkRAMAddr:     0x00900000
T4A00 000:153.162         WorkRAMSize:     0x00020000
T4A00 000:153.184         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexM4.JLinkScript
T4A00 000:153.190       FlashBankInfo:
T4A00 000:153.196         Name:            QSPI flash
T4A00 000:153.201         BaseAddr:        0x60000000
T4A00 000:153.233     Device entry modified: MCIMX7D7_A7_0
T4A00 000:153.253       ChipInfo:
T4A00 000:153.259         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_0.JLinkScript
T4A00 000:153.265       FlashBankInfo:
T4A00 000:153.270         Name:            QSPI flash
T4A00 000:153.276         BaseAddr:        0x60000000
T4A00 000:153.307     Device entry modified: MCIMX7D7_A7_1
T4A00 000:153.327       ChipInfo:
T4A00 000:153.333         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_1.JLinkScript
T4A00 000:153.340       FlashBankInfo:
T4A00 000:153.346         Name:            QSPI flash
T4A00 000:153.351         BaseAddr:        0x60000000
T4A00 000:153.382     Device entry modified: MCIMX7D7_M4
T4A00 000:153.386       ChipInfo:
T4A00 000:153.392         WorkRAMAddr:     0x00900000
T4A00 000:153.398         WorkRAMSize:     0x00020000
T4A00 000:153.421         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexM4.JLinkScript
T4A00 000:153.427       FlashBankInfo:
T4A00 000:153.433         Name:            QSPI flash
T4A00 000:153.439         BaseAddr:        0x60000000
T4A00 000:153.469     Device entry modified: MCIMX7U3_M4
T4A00 000:153.489       ChipInfo:
T4A00 000:153.495         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7ULP\NXP_iMX7ULP_CortexM4.JLinkScript
T4A00 000:153.501       FlashBankInfo:
T4A00 000:153.506         Name:            QSPI Flash
T4A00 000:153.512         BaseAddr:        0x04000000
T4A00 000:153.544     Device entry modified: MCIMX7U3_A7
T4A00 000:153.564       ChipInfo:
T4A00 000:153.570         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7ULP\NXP_iMX7ULP_CortexA7.JLinkScript
T4A00 000:153.576       FlashBankInfo:
T4A00 000:153.581         Name:            QSPI Flash
T4A00 000:153.587         BaseAddr:        0xC0000000
T4A00 000:153.622     Device entry modified: MCIMX7U5_M4
T4A00 000:153.643       ChipInfo:
T4A00 000:153.650         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7ULP\NXP_iMX7ULP_CortexM4.JLinkScript
T4A00 000:153.655       FlashBankInfo:
T4A00 000:153.661         Name:            QSPI Flash
T4A00 000:153.667         BaseAddr:        0x04000000
T4A00 000:153.698     Device entry modified: MCIMX7U5_A7
T4A00 000:153.718       ChipInfo:
T4A00 000:153.725         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\iMX7ULP\NXP_iMX7ULP_CortexA7.JLinkScript
T4A00 000:153.730       FlashBankInfo:
T4A00 000:153.736         Name:            QSPI Flash
T4A00 000:153.741         BaseAddr:        0xC0000000
T4A00 000:153.772     Device entry modified: MIMXRT1021xxx4A
T4A00 000:153.782       FlashBankInfo:
T4A00 000:153.787         Name:            QSPI Flash
T4A00 000:153.793         BaseAddr:        0x60000000
T4A00 000:153.826     Device entry modified: MIMXRT1021xxx4A
T4A00 000:153.831       FlashBankInfo:
T4A00 000:153.837         Name:            QSPI Flash
T4A00 000:153.842         BaseAddr:        0x60000000
T4A00 000:153.872     Device entry modified: MIMXRT1021xxx4A
T4A00 000:153.877       FlashBankInfo:
T4A00 000:153.883         Name:            QSPI Flash
T4A00 000:153.888         BaseAddr:        0x60000000
T4A00 000:153.918     Device entry modified: MIMXRT1021xxx5A
T4A00 000:153.923       FlashBankInfo:
T4A00 000:153.929         Name:            QSPI Flash
T4A00 000:153.934         BaseAddr:        0x60000000
T4A00 000:153.966     Device entry modified: MIMXRT1021xxx5A
T4A00 000:153.971       FlashBankInfo:
T4A00 000:153.977         Name:            QSPI Flash
T4A00 000:153.982         BaseAddr:        0x60000000
T4A00 000:154.013     Device entry modified: MIMXRT1021xxx5A
T4A00 000:154.019       FlashBankInfo:
T4A00 000:154.024         Name:            QSPI Flash
T4A00 000:154.030         BaseAddr:        0x60000000
T4A00 000:154.061     Device entry modified: MIMXRT1051xxxxA
T4A00 000:154.065       ChipInfo:
T4A00 000:154.071         WorkRAMSize:     0x00080000
T4A00 000:154.077       FlashBankInfo:
T4A00 000:154.082         Name:            HyperFlash
T4A00 000:154.088         BaseAddr:        0x60000000
T4A00 000:154.120     Device entry modified: MIMXRT1051xxxxA
T4A00 000:154.126       FlashBankInfo:
T4A00 000:154.131         Name:            HyperFlash
T4A00 000:154.165         BaseAddr:        0x60000000
T4A00 000:154.200     Alias entry created:
T4A00 000:154.205       ChipInfo:
T4A00 000:154.211         Alias of:        MIMXRT1051xxxxA
T4A00 000:154.217         Name:            MIMXRT1051xxx5A
T4A00 000:154.262     Alias entry created:
T4A00 000:154.266       ChipInfo:
T4A00 000:154.272         Alias of:        MIMXRT1051xxxxA
T4A00 000:154.277         Name:            MIMXRT1051CVL5A
T4A00 000:154.297     Alias entry created:
T4A00 000:154.301       ChipInfo:
T4A00 000:154.307         Alias of:        MIMXRT1051xxxxA
T4A00 000:154.312         Name:            MIMXRT1051xxx6A
T4A00 000:154.321     Alias entry created:
T4A00 000:154.325       ChipInfo:
T4A00 000:154.330         Alias of:        MIMXRT1051xxxxA
T4A00 000:154.336         Name:            MIMXRT1051DVL6A
T4A00 000:154.355     Device entry modified: MIMXRT1051xxxxB
T4A00 000:154.359       ChipInfo:
T4A00 000:154.365         WorkRAMSize:     0x00080000
T4A00 000:154.371       FlashBankInfo:
T4A00 000:154.377         Name:            HyperFlash
T4A00 000:154.383         BaseAddr:        0x60000000
T4A00 000:154.419     Alias entry created:
T4A00 000:154.424       ChipInfo:
T4A00 000:154.430         Alias of:        MIMXRT1051xxxxB
T4A00 000:154.435         Name:            MIMXRT1051xxx5B
T4A00 000:154.443     Alias entry created:
T4A00 000:154.447       ChipInfo:
T4A00 000:154.452         Alias of:        MIMXRT1051xxxxB
T4A00 000:154.458         Name:            MIMXRT1051CVL5B
T4A00 000:154.475     Alias entry created:
T4A00 000:154.479       ChipInfo:
T4A00 000:154.485         Alias of:        MIMXRT1051xxxxB
T4A00 000:154.490         Name:            MIMXRT1051xxx6B
T4A00 000:154.498     Alias entry created:
T4A00 000:154.502       ChipInfo:
T4A00 000:154.507         Alias of:        MIMXRT1051xxxxB
T4A00 000:154.513         Name:            MIMXRT1051DVL6B
T4A00 000:154.531     Device entry modified: MIMXRT1052xxxxA
T4A00 000:154.535       ChipInfo:
T4A00 000:154.541         WorkRAMSize:     0x00080000
T4A00 000:154.547       FlashBankInfo:
T4A00 000:154.553         Name:            HyperFlash
T4A00 000:154.558         BaseAddr:        0x60000000
T4A00 000:154.590     Device entry modified: MIMXRT1052xxxxA
T4A00 000:154.596       FlashBankInfo:
T4A00 000:154.601         Name:            HyperFlash
T4A00 000:154.607         BaseAddr:        0x60000000
T4A00 000:154.639     Alias entry created:
T4A00 000:154.644       ChipInfo:
T4A00 000:154.649         Alias of:        MIMXRT1052xxxxA
T4A00 000:154.655         Name:            MIMXRT1052xxx5A
T4A00 000:154.663     Alias entry created:
T4A00 000:154.667       ChipInfo:
T4A00 000:154.672         Alias of:        MIMXRT1052xxxxA
T4A00 000:154.678         Name:            MIMXRT1052CVL5A
T4A00 000:154.697     Alias entry created:
T4A00 000:154.701       ChipInfo:
T4A00 000:154.706         Alias of:        MIMXRT1052xxxxA
T4A00 000:154.712         Name:            MIMXRT1052xxx6A
T4A00 000:154.719     Alias entry created:
T4A00 000:154.723       ChipInfo:
T4A00 000:154.728         Alias of:        MIMXRT1052xxxxA
T4A00 000:154.734         Name:            MIMXRT1052DVL6A
T4A00 000:154.753     Device entry modified: MIMXRT1052xxxxB
T4A00 000:154.757       ChipInfo:
T4A00 000:154.762         WorkRAMSize:     0x00080000
T4A00 000:154.769       FlashBankInfo:
T4A00 000:154.774         Name:            HyperFlash
T4A00 000:154.780         BaseAddr:        0x60000000
T4A00 000:154.811     Alias entry created:
T4A00 000:154.816       ChipInfo:
T4A00 000:154.821         Alias of:        MIMXRT1052xxxxB
T4A00 000:154.827         Name:            MIMXRT1052xxx5B
T4A00 000:154.834     Alias entry created:
T4A00 000:154.838       ChipInfo:
T4A00 000:154.844         Alias of:        MIMXRT1052xxxxB
T4A00 000:154.849         Name:            MIMXRT1052CVL5B
T4A00 000:154.868     Alias entry created:
T4A00 000:154.872       ChipInfo:
T4A00 000:154.877         Alias of:        MIMXRT1052xxxxB
T4A00 000:154.883         Name:            MIMXRT1052xxx6B
T4A00 000:154.892     Alias entry created:
T4A00 000:154.896       ChipInfo:
T4A00 000:154.901         Alias of:        MIMXRT1052xxxxB
T4A00 000:154.906         Name:            MIMXRT1052DVL6B
T4A00 000:154.926     Device entry modified: MIMXRT1061xxx5A
T4A00 000:154.930       ChipInfo:
T4A00 000:154.935         WorkRAMSize:     0x00080000
T4A00 000:154.942       FlashBankInfo:
T4A00 000:154.947         Name:            HyperFlash
T4A00 000:154.953         BaseAddr:        0x60000000
T4A00 000:154.986     Alias entry created:
T4A00 000:154.991       ChipInfo:
T4A00 000:154.996         Alias of:        MIMXRT1061xxx5A
T4A00 000:155.002         Name:            MIMXRT1061CVL5A
T4A00 000:155.010     Alias entry created:
T4A00 000:155.013       ChipInfo:
T4A00 000:155.019         Alias of:        MIMXRT1061xxx5A
T4A00 000:155.024         Name:            MIMXRT1061CVJ5A
T4A00 000:155.044     Device entry modified: MIMXRT1061xxx6A
T4A00 000:155.048       ChipInfo:
T4A00 000:155.053         WorkRAMSize:     0x00080000
T4A00 000:155.060       FlashBankInfo:
T4A00 000:155.066         Name:            HyperFlash
T4A00 000:155.071         BaseAddr:        0x60000000
T4A00 000:155.103     Alias entry created:
T4A00 000:155.108       ChipInfo:
T4A00 000:155.113         Alias of:        MIMXRT1061xxx6A
T4A00 000:155.119         Name:            MIMXRT1061DVL6A
T4A00 000:155.127     Device entry modified: MIMXRT1062xxx5A
T4A00 000:155.131       ChipInfo:
T4A00 000:155.137         WorkRAMSize:     0x00080000
T4A00 000:155.143       FlashBankInfo:
T4A00 000:155.149         Name:            HyperFlash
T4A00 000:155.154         BaseAddr:        0x60000000
T4A00 000:155.184     Alias entry created:
T4A00 000:155.188       ChipInfo:
T4A00 000:155.194         Alias of:        MIMXRT1062xxx5A
T4A00 000:155.200         Name:            MIMXRT1062CVL5A
T4A00 000:155.208     Alias entry created:
T4A00 000:155.211       ChipInfo:
T4A00 000:155.217         Alias of:        MIMXRT1062xxx5A
T4A00 000:155.222         Name:            MIMXRT1062CVJ5A
T4A00 000:155.241     Device entry modified: MIMXRT1062xxx6A
T4A00 000:155.245       ChipInfo:
T4A00 000:155.251         WorkRAMSize:     0x00080000
T4A00 000:155.257       FlashBankInfo:
T4A00 000:155.263         Name:            HyperFlash
T4A00 000:155.268         BaseAddr:        0x60000000
T4A00 000:155.298     Alias entry created:
T4A00 000:155.303       ChipInfo:
T4A00 000:155.308         Alias of:        MIMXRT1062xxx6A
T4A00 000:155.314         Name:            MIMXRT1062DVL6A
T4A00 000:155.322     Device entry modified: LPC51U68
T4A00 000:155.342       ChipInfo:
T4A00 000:155.349         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC51U68\NXP_LPC51U68.pex
T4A00 000:155.355     Device entry modified: LPC54005
T4A00 000:155.375       ChipInfo:
T4A00 000:155.381         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:155.388     Device entry modified: LPC54016
T4A00 000:155.408       ChipInfo:
T4A00 000:155.414         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:155.421     Device entry modified: LPC54018
T4A00 000:155.440       ChipInfo:
T4A00 000:155.446         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:155.453     Device entry modified: LPC54S005
T4A00 000:155.473       ChipInfo:
T4A00 000:155.479         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:155.485     Device entry modified: LPC54S016
T4A00 000:155.505       ChipInfo:
T4A00 000:155.511         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:155.518     Device entry modified: LPC54S018
T4A00 000:155.538       ChipInfo:
T4A00 000:155.546         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:155.553     Device entry modified: LPC54101J256_M0
T4A00 000:155.572       ChipInfo:
T4A00 000:155.579         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:155.586     Device entry modified: LPC54101J512_M0
T4A00 000:155.605       ChipInfo:
T4A00 000:155.611         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:155.618     Device entry modified: LPC54102J256_M0
T4A00 000:155.637       ChipInfo:
T4A00 000:155.643         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:155.651     Device entry modified: LPC54102J512_M0
T4A00 000:155.669       ChipInfo:
T4A00 000:155.676         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:155.683     Device entry modified: LPC54111J128_M0
T4A00 000:155.701       ChipInfo:
T4A00 000:155.708         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:155.715     Device entry modified: LPC54111J256_M0
T4A00 000:155.734       ChipInfo:
T4A00 000:155.740         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:155.748     Device entry modified: LPC54112J256_M0
T4A00 000:155.766       ChipInfo:
T4A00 000:155.772         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:155.779     Device entry modified: LPC54113J128_M0
T4A00 000:155.798       ChipInfo:
T4A00 000:155.804         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:155.811     Device entry modified: LPC54113J256_M0
T4A00 000:155.830       ChipInfo:
T4A00 000:155.836         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:155.843     Device entry modified: LPC54114J256_M0
T4A00 000:155.862       ChipInfo:
T4A00 000:155.868         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:155.875     Device entry modified: LPC54608J512
T4A00 000:155.903       ChipInfo:
T4A00 000:155.909         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5460x\NXP_LPC5460x.pex
T4A00 000:155.917     Device entry modified: LPC54608J512 (allow ECRP)
T4A00 000:155.937       ChipInfo:
T4A00 000:155.944         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\LPC5460x\NXP_LPC5460x.pex
T4A00 000:155.951     Device entry modified: S32V232_A53_A0
T4A00 000:155.972       ChipInfo:
T4A00 000:155.978         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\S32V\NXP_S32V234_A53_0.pex
T4A00 000:155.985     Device entry modified: S32V232_A53_B0
T4A00 000:156.005       ChipInfo:
T4A00 000:156.012         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\S32V\NXP_S32V234_A53_2.pex
T4A00 000:156.018     Device entry modified: S32V234_A53_A0
T4A00 000:156.037       ChipInfo:
T4A00 000:156.044         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\S32V\NXP_S32V234_A53_0.pex
T4A00 000:156.051     Device entry modified: S32V234_A53_A1
T4A00 000:156.070       ChipInfo:
T4A00 000:156.076         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\S32V\NXP_S32V234_A53_1.pex
T4A00 000:156.084     Device entry modified: S32V234_A53_B0
T4A00 000:156.102       ChipInfo:
T4A00 000:156.109         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\S32V\NXP_S32V234_A53_2.pex
T4A00 000:156.117     Device entry modified: S32V234_A53_B1
T4A00 000:156.136       ChipInfo:
T4A00 000:156.142         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\NXP\S32V\NXP_S32V234_A53_3.pex
T4A00 000:156.150     Device entry modified: R9A06G032
T4A00 000:156.174       ChipInfo:
T4A00 000:156.180         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-A7_CPU0.pex
T4A00 000:156.188     Device entry modified: R9A06G032_A7CPU0
T4A00 000:156.207       ChipInfo:
T4A00 000:156.213         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-A7_CPU0.pex
T4A00 000:156.221     Device entry modified: R9A06G032_A7CPU1
T4A00 000:156.240       ChipInfo:
T4A00 000:156.246         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-A7_CPU1.pex
T4A00 000:156.253     Device entry modified: R9A06G032_M3
T4A00 000:156.271       ChipInfo:
T4A00 000:156.278         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-M3.pex
T4A00 000:156.285     Device entry modified: R9A06G033
T4A00 000:156.304       ChipInfo:
T4A00 000:156.310         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-A7_CPU0.pex
T4A00 000:156.317     Device entry modified: R9A06G033_A7
T4A00 000:156.336       ChipInfo:
T4A00 000:156.342         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-A7_CPU0.pex
T4A00 000:156.349     Device entry modified: R9A06G033_M3
T4A00 000:156.367       ChipInfo:
T4A00 000:156.374         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-M3.pex
T4A00 000:156.381     Device entry modified: R9A06G034
T4A00 000:156.399       ChipInfo:
T4A00 000:156.405         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-M3.pex
T4A00 000:156.413     Device entry modified: R7S921040VCBG
T4A00 000:156.424       FlashBankInfo:
T4A00 000:156.430         Name:            HyperFlash
T4A00 000:156.435         BaseAddr:        0x30000000
T4A00 000:156.470     Device entry modified: R7S921041VCBG
T4A00 000:156.475       FlashBankInfo:
T4A00 000:156.480         Name:            HyperFlash
T4A00 000:156.486         BaseAddr:        0x30000000
T4A00 000:156.517     Device entry modified: R7S921042VCBG
T4A00 000:156.522       FlashBankInfo:
T4A00 000:156.528         Name:            HyperFlash
T4A00 000:156.533         BaseAddr:        0x30000000
T4A00 000:156.565     Device entry modified: R7S921043VCBG
T4A00 000:156.570       FlashBankInfo:
T4A00 000:156.575         Name:            HyperFlash
T4A00 000:156.581         BaseAddr:        0x30000000
T4A00 000:156.610     Device entry modified: R7S921051VCBG
T4A00 000:156.615       FlashBankInfo:
T4A00 000:156.621         Name:            HyperFlash
T4A00 000:156.627         BaseAddr:        0x30000000
T4A00 000:156.656     Device entry modified: R7S921052VCBG
T4A00 000:156.661       FlashBankInfo:
T4A00 000:156.667         Name:            HyperFlash
T4A00 000:156.672         BaseAddr:        0x30000000
T4A00 000:156.703     Device entry modified: R7S921053VCBG
T4A00 000:156.708       FlashBankInfo:
T4A00 000:156.714         Name:            HyperFlash
T4A00 000:156.720         BaseAddr:        0x30000000
T4A00 000:156.750     Device entry modified: CC3200
T4A00 000:156.771       ChipInfo:
T4A00 000:156.778         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\TI\TI_CC3200.JLinkScript
T4A00 000:156.785     Device entry modified: CC3220R
T4A00 000:156.805       ChipInfo:
T4A00 000:156.813         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\TI\TI_CC322x.pex
T4A00 000:156.820         WorkRAMSize:     0x00010000
T4A00 000:156.826     Device entry modified: CC3220S
T4A00 000:156.846       ChipInfo:
T4A00 000:156.852         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\TI\TI_CC322x.pex
T4A00 000:156.858         WorkRAMSize:     0x00010000
T4A00 000:156.865     Device entry modified: CC3220SF
T4A00 000:156.885       ChipInfo:
T4A00 000:156.891         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\TI\TI_CC322x.pex
T4A00 000:156.897         WorkRAMSize:     0x00010000
T4A00 000:156.904     Device entry modified: DM3730
T4A00 000:156.922       ChipInfo:
T4A00 000:156.929         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\TI\DM3730\TI_DM3730.pex
T4A00 000:156.936     Device entry modified: TLE9842
T4A00 000:156.941       FlashBankInfo:
T4A00 000:156.947         Name:            NACNAD
T4A00 000:156.953         BaseAddr:        0x10FFFFFC
T4A00 000:156.984         AlwaysPresent:   1
T4A00 000:156.990       FlashBankInfo:
T4A00 000:156.996         Name:            Code Flash
T4A00 000:157.001         BaseAddr:        0x11000000
T4A00 000:157.032         AlwaysPresent:   1
T4A00 000:157.038       FlashBankInfo:
T4A00 000:157.043         Name:            Data Flash
T4A00 000:157.049         BaseAddr:        0x11008000
T4A00 000:157.079         AlwaysPresent:   1
T4A00 000:157.086     Device entry modified: TLE9842-2
T4A00 000:157.091       FlashBankInfo:
T4A00 000:157.096         Name:            NACNAD
T4A00 000:157.102         BaseAddr:        0x10FFFFFC
T4A00 000:157.130         AlwaysPresent:   1
T4A00 000:157.136       FlashBankInfo:
T4A00 000:157.142         Name:            Code Flash
T4A00 000:157.147         BaseAddr:        0x11000000
T4A00 000:157.176         AlwaysPresent:   1
T4A00 000:157.183       FlashBankInfo:
T4A00 000:157.188         Name:            Data Flash
T4A00 000:157.194         BaseAddr:        0x11009000
T4A00 000:157.222         AlwaysPresent:   1
T4A00 000:157.229     Device entry modified: TLE9843
T4A00 000:157.234       FlashBankInfo:
T4A00 000:157.239         Name:            NACNAD
T4A00 000:157.245         BaseAddr:        0x10FFFFFC
T4A00 000:157.273         AlwaysPresent:   1
T4A00 000:157.279       FlashBankInfo:
T4A00 000:157.285         Name:            Code Flash
T4A00 000:157.290         BaseAddr:        0x11000000
T4A00 000:157.319         AlwaysPresent:   1
T4A00 000:157.325       FlashBankInfo:
T4A00 000:157.331         Name:            Data Flash
T4A00 000:157.337         BaseAddr:        0x1100B000
T4A00 000:157.365         AlwaysPresent:   1
T4A00 000:157.373     Device entry modified: TLE9843-2
T4A00 000:157.377       FlashBankInfo:
T4A00 000:157.383         Name:            NACNAD
T4A00 000:157.388         BaseAddr:        0x10FFFFFC
T4A00 000:157.417         AlwaysPresent:   1
T4A00 000:157.423       FlashBankInfo:
T4A00 000:157.429         Name:            Code Flash
T4A00 000:157.434         BaseAddr:        0x11000000
T4A00 000:157.463         AlwaysPresent:   1
T4A00 000:157.469       FlashBankInfo:
T4A00 000:157.474         Name:            Data Flash
T4A00 000:157.480         BaseAddr:        0x1100C000
T4A00 000:157.509         AlwaysPresent:   1
T4A00 000:157.516     Device entry modified: TLE9844
T4A00 000:157.520       FlashBankInfo:
T4A00 000:157.526         Name:            NACNAD
T4A00 000:157.531         BaseAddr:        0x10FFFFFC
T4A00 000:157.561         AlwaysPresent:   1
T4A00 000:157.567       FlashBankInfo:
T4A00 000:157.573         Name:            Code Flash
T4A00 000:157.578         BaseAddr:        0x11000000
T4A00 000:157.608         AlwaysPresent:   1
T4A00 000:157.614       FlashBankInfo:
T4A00 000:157.620         Name:            Data Flash
T4A00 000:157.625         BaseAddr:        0x1100F000
T4A00 000:157.654         AlwaysPresent:   1
T4A00 000:157.660     Device entry modified: TLE9844
T4A00 000:157.666       FlashBankInfo:
T4A00 000:157.671         Name:            NACNAD
T4A00 000:157.677         BaseAddr:        0x10FFFFFC
T4A00 000:157.706         AlwaysPresent:   1
T4A00 000:157.712       FlashBankInfo:
T4A00 000:157.717         Name:            Code Flash
T4A00 000:157.723         BaseAddr:        0x11000000
T4A00 000:157.751         AlwaysPresent:   1
T4A00 000:157.757       FlashBankInfo:
T4A00 000:157.763         Name:            Data Flash
T4A00 000:157.768         BaseAddr:        0x1100F000
T4A00 000:157.797         AlwaysPresent:   1
T4A00 000:157.804     Device entry modified: TLE9845
T4A00 000:157.809       FlashBankInfo:
T4A00 000:157.814         Name:            NACNAD
T4A00 000:157.820         BaseAddr:        0x10FFFFFC
T4A00 000:157.848         AlwaysPresent:   1
T4A00 000:157.854       FlashBankInfo:
T4A00 000:157.860         Name:            Code Flash
T4A00 000:157.865         BaseAddr:        0x11000000
T4A00 000:157.894         AlwaysPresent:   1
T4A00 000:157.900       FlashBankInfo:
T4A00 000:157.906         Name:            Data Flash
T4A00 000:157.911         BaseAddr:        0x1100B000
T4A00 000:157.940         AlwaysPresent:   1
T4A00 000:157.948     Device entry modified: TLE9861
T4A00 000:157.956       ChipInfo:
T4A00 000:157.961         WorkRAMAddr:     0x18000000
T4A00 000:157.967         WorkRAMSize:     0x00000C00
T4A00 000:157.973         Core:            JLINK_CORE_CORTEX_M3_R2P0
T4A00 000:157.979       FlashBankInfo:
T4A00 000:157.984         Name:            Code Flash
T4A00 000:157.990         BaseAddr:        0x11000000
T4A00 000:158.021         AlwaysPresent:   1
T4A00 000:158.030       FlashBankInfo:
T4A00 000:158.036         Name:            Data Flash
T4A00 000:158.041         BaseAddr:        0x11008000
T4A00 000:158.071         AlwaysPresent:   1
T4A00 000:158.078     Device entry modified: TLE9867
T4A00 000:158.082       ChipInfo:
T4A00 000:158.088         WorkRAMAddr:     0x18000000
T4A00 000:158.094         WorkRAMSize:     0x00001800
T4A00 000:158.099       FlashBankInfo:
T4A00 000:158.105         Name:            Code Flash
T4A00 000:158.110         BaseAddr:        0x11000000
T4A00 000:158.139         AlwaysPresent:   1
T4A00 000:158.145       FlashBankInfo:
T4A00 000:158.151         Name:            Data Flash
T4A00 000:158.157         BaseAddr:        0x1100F000
T4A00 000:158.185         AlwaysPresent:   1
T4A00 000:158.192     Device entry modified: TLE9867
T4A00 000:158.196       FlashBankInfo:
T4A00 000:158.202         Name:            Code Flash
T4A00 000:158.207         BaseAddr:        0x11000000
T4A00 000:158.236         AlwaysPresent:   1
T4A00 000:158.242       FlashBankInfo:
T4A00 000:158.247         Name:            Data Flash
T4A00 000:158.253         BaseAddr:        0x1100F000
T4A00 000:158.281         AlwaysPresent:   1
T4A00 000:158.288     Device entry modified: TLE9867
T4A00 000:158.292       FlashBankInfo:
T4A00 000:158.298         Name:            Code Flash
T4A00 000:158.303         BaseAddr:        0x11000000
T4A00 000:158.331         AlwaysPresent:   1
T4A00 000:158.337       FlashBankInfo:
T4A00 000:158.343         Name:            Data Flash
T4A00 000:158.348         BaseAddr:        0x1100F000
T4A00 000:158.377         AlwaysPresent:   1
T4A00 000:158.384     Device entry modified: TLE9868
T4A00 000:158.388       ChipInfo:
T4A00 000:158.393         WorkRAMAddr:     0x18000000
T4A00 000:158.399         WorkRAMSize:     0x00001000
T4A00 000:158.405       FlashBankInfo:
T4A00 000:158.410         Name:            Code Flash
T4A00 000:158.416         BaseAddr:        0x11000000
T4A00 000:158.444         AlwaysPresent:   1
T4A00 000:158.450       FlashBankInfo:
T4A00 000:158.456         Name:            Data Flash
T4A00 000:158.461         BaseAddr:        0x1101F000
T4A00 000:158.490         AlwaysPresent:   1
T4A00 000:158.497     Device entry modified: TLE9869
T4A00 000:158.501       ChipInfo:
T4A00 000:158.506         WorkRAMAddr:     0x18000000
T4A00 000:158.513         WorkRAMSize:     0x00001800
T4A00 000:158.519       FlashBankInfo:
T4A00 000:158.524         Name:            Code Flash
T4A00 000:158.530         BaseAddr:        0x11000000
T4A00 000:158.559         AlwaysPresent:   1
T4A00 000:158.565       FlashBankInfo:
T4A00 000:158.570         Name:            Data Flash
T4A00 000:158.576         BaseAddr:        0x1101F000
T4A00 000:158.604         AlwaysPresent:   1
T4A00 000:158.612     Device entry modified: TLE9869
T4A00 000:158.616       FlashBankInfo:
T4A00 000:158.621         Name:            Code Flash
T4A00 000:158.627         BaseAddr:        0x11000000
T4A00 000:158.655         AlwaysPresent:   1
T4A00 000:158.661       FlashBankInfo:
T4A00 000:158.667         Name:            Data Flash
T4A00 000:158.672         BaseAddr:        0x1101F000
T4A00 000:158.701         AlwaysPresent:   1
T4A00 000:158.708     Device entry modified: TLE9871
T4A00 000:158.712       ChipInfo:
T4A00 000:158.717         WorkRAMAddr:     0x18000000
T4A00 000:158.723         WorkRAMSize:     0x00000C00
T4A00 000:158.729         Core:            JLINK_CORE_CORTEX_M3_R2P0
T4A00 000:158.734       FlashBankInfo:
T4A00 000:158.740         Name:            Code Flash
T4A00 000:158.745         BaseAddr:        0x11000000
T4A00 000:158.775         AlwaysPresent:   1
T4A00 000:158.781       FlashBankInfo:
T4A00 000:158.786         Name:            Data Flash
T4A00 000:158.792         BaseAddr:        0x11008000
T4A00 000:158.821         AlwaysPresent:   1
T4A00 000:158.828     Device entry modified: TLE9873
T4A00 000:158.832       ChipInfo:
T4A00 000:158.838         WorkRAMAddr:     0x18000000
T4A00 000:158.843         WorkRAMSize:     0x00000C00
T4A00 000:158.849       FlashBankInfo:
T4A00 000:158.854         Name:            Code Flash
T4A00 000:158.860         BaseAddr:        0x11000000
T4A00 000:158.889         AlwaysPresent:   1
T4A00 000:158.895       FlashBankInfo:
T4A00 000:158.900         Name:            Data Flash
T4A00 000:158.906         BaseAddr:        0x1100B000
T4A00 000:158.934         AlwaysPresent:   1
T4A00 000:158.941     Device entry modified: TLE9873
T4A00 000:158.946       FlashBankInfo:
T4A00 000:158.951         Name:            Code Flash
T4A00 000:158.957         BaseAddr:        0x11000000
T4A00 000:158.985         AlwaysPresent:   1
T4A00 000:158.992       FlashBankInfo:
T4A00 000:158.997         Name:            Data Flash
T4A00 000:159.003         BaseAddr:        0x1100B000
T4A00 000:159.031         AlwaysPresent:   1
T4A00 000:159.038     Device entry modified: TLE9877
T4A00 000:159.042       ChipInfo:
T4A00 000:159.047         WorkRAMAddr:     0x18000000
T4A00 000:159.053         WorkRAMSize:     0x00001800
T4A00 000:159.059       FlashBankInfo:
T4A00 000:159.064         Name:            Code Flash
T4A00 000:159.070         BaseAddr:        0x11000000
T4A00 000:159.098         AlwaysPresent:   1
T4A00 000:159.104       FlashBankInfo:
T4A00 000:159.110         Name:            Data Flash
T4A00 000:159.116         BaseAddr:        0x1100F000
T4A00 000:159.145         AlwaysPresent:   1
T4A00 000:159.151     Device entry modified: TLE9877
T4A00 000:159.155       FlashBankInfo:
T4A00 000:159.161         Name:            Code Flash
T4A00 000:159.166         BaseAddr:        0x11000000
T4A00 000:159.195         AlwaysPresent:   1
T4A00 000:159.201       FlashBankInfo:
T4A00 000:159.207         Name:            Data Flash
T4A00 000:159.212         BaseAddr:        0x1100F000
T4A00 000:159.241         AlwaysPresent:   1
T4A00 000:159.248     Device entry modified: TLE9877
T4A00 000:159.252       FlashBankInfo:
T4A00 000:159.258         Name:            Code Flash
T4A00 000:159.263         BaseAddr:        0x11000000
T4A00 000:159.292         AlwaysPresent:   1
T4A00 000:159.298       FlashBankInfo:
T4A00 000:159.303         Name:            Data Flash
T4A00 000:159.309         BaseAddr:        0x1100F000
T4A00 000:159.337         AlwaysPresent:   1
T4A00 000:159.345     Device entry modified: TLE9877
T4A00 000:159.349       FlashBankInfo:
T4A00 000:159.355         Name:            Code Flash
T4A00 000:159.360         BaseAddr:        0x11000000
T4A00 000:159.390         AlwaysPresent:   1
T4A00 000:159.396       FlashBankInfo:
T4A00 000:159.402         Name:            Data Flash
T4A00 000:159.407         BaseAddr:        0x1100F000
T4A00 000:159.436         AlwaysPresent:   1
T4A00 000:159.443     Device entry modified: TLE9879
T4A00 000:159.447       ChipInfo:
T4A00 000:159.453         WorkRAMAddr:     0x18000000
T4A00 000:159.458         WorkRAMSize:     0x00001800
T4A00 000:159.464       FlashBankInfo:
T4A00 000:159.470         Name:            Code Flash
T4A00 000:159.475         BaseAddr:        0x11000000
T4A00 000:159.504         AlwaysPresent:   1
T4A00 000:159.510       FlashBankInfo:
T4A00 000:159.516         Name:            Data Flash
T4A00 000:159.521         BaseAddr:        0x1101F000
T4A00 000:159.550         AlwaysPresent:   1
T4A00 000:159.557     Device entry modified: TLE9879
T4A00 000:159.561       FlashBankInfo:
T4A00 000:159.567         Name:            Code Flash
T4A00 000:159.572         BaseAddr:        0x11000000
T4A00 000:159.601         AlwaysPresent:   1
T4A00 000:159.607       FlashBankInfo:
T4A00 000:159.612         Name:            Data Flash
T4A00 000:159.618         BaseAddr:        0x1101F000
T4A00 000:159.647         AlwaysPresent:   1
T4A00 000:159.654     Device entry modified: TLE9879
T4A00 000:159.658       FlashBankInfo:
T4A00 000:159.664         Name:            Code Flash
T4A00 000:159.670         BaseAddr:        0x11000000
T4A00 000:159.698         AlwaysPresent:   1
T4A00 000:159.704       FlashBankInfo:
T4A00 000:159.709         Name:            Data Flash
T4A00 000:159.715         BaseAddr:        0x1101F000
T4A00 000:159.745         AlwaysPresent:   1
T4A00 000:159.752     Device entry modified: TLE9879
T4A00 000:159.756       FlashBankInfo:
T4A00 000:159.761         Name:            Code Flash
T4A00 000:159.767         BaseAddr:        0x11000000
T4A00 000:159.795         AlwaysPresent:   1
T4A00 000:159.801       FlashBankInfo:
T4A00 000:159.807         Name:            Data Flash
T4A00 000:159.813         BaseAddr:        0x1101F000
T4A00 000:159.841         AlwaysPresent:   1
T4A00 000:159.848     Device entry modified: TLE9879
T4A00 000:159.852       FlashBankInfo:
T4A00 000:159.857         Name:            Code Flash
T4A00 000:159.863         BaseAddr:        0x11000000
T4A00 000:159.892         AlwaysPresent:   1
T4A00 000:159.898       FlashBankInfo:
T4A00 000:159.903         Name:            Data Flash
T4A00 000:159.909         BaseAddr:        0x1101F000
T4A00 000:159.937         AlwaysPresent:   1
T4A00 000:159.945     Device entry modified: TLE9851QXW
T4A00 000:159.964       ChipInfo:
T4A00 000:159.970         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Infineon\TLE985x\Infineon_TLExxx.pex
T4A00 000:159.976       FlashBankInfo:
T4A00 000:159.981         Name:            NACNAD
T4A00 000:159.987         BaseAddr:        0x10FFFFFC
T4A00 000:160.017         AlwaysPresent:   1
T4A00 000:160.023       FlashBankInfo:
T4A00 000:160.029         Name:            Code Flash
T4A00 000:160.035         BaseAddr:        0x11000000
T4A00 000:160.063         AlwaysPresent:   1
T4A00 000:160.069       FlashBankInfo:
T4A00 000:160.075         Name:            Data Flash
T4A00 000:160.080         BaseAddr:        0x1100F000
T4A00 000:160.109         AlwaysPresent:   1
T4A00 000:160.116     Device entry modified: TLE9853QX
T4A00 000:160.136       ChipInfo:
T4A00 000:160.142         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Infineon\TLE985x\Infineon_TLExxx.pex
T4A00 000:160.148       FlashBankInfo:
T4A00 000:160.153         Name:            NACNAD
T4A00 000:160.159         BaseAddr:        0x10FFFFFC
T4A00 000:160.188         AlwaysPresent:   1
T4A00 000:160.194       FlashBankInfo:
T4A00 000:160.220         Name:            Code Flash
T4A00 000:160.225         BaseAddr:        0x11000000
T4A00 000:160.258         AlwaysPresent:   1
T4A00 000:160.264       FlashBankInfo:
T4A00 000:160.270         Name:            Data Flash
T4A00 000:160.275         BaseAddr:        0x1100B000
T4A00 000:160.305         AlwaysPresent:   1
T4A00 000:160.312     Device entry modified: TLE9854QX
T4A00 000:160.332       ChipInfo:
T4A00 000:160.338         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Infineon\TLE985x\Infineon_TLExxx.pex
T4A00 000:160.344       FlashBankInfo:
T4A00 000:160.349         Name:            NACNAD
T4A00 000:160.355         BaseAddr:        0x10FFFFFC
T4A00 000:160.383         AlwaysPresent:   1
T4A00 000:160.389       FlashBankInfo:
T4A00 000:160.395         Name:            Code Flash
T4A00 000:160.400         BaseAddr:        0x11000000
T4A00 000:160.429         AlwaysPresent:   1
T4A00 000:160.435       FlashBankInfo:
T4A00 000:160.441         Name:            Data Flash
T4A00 000:160.446         BaseAddr:        0x1100F000
T4A00 000:160.475         AlwaysPresent:   1
T4A00 000:160.483     Device entry modified: TLE9855QX
T4A00 000:160.502       ChipInfo:
T4A00 000:160.508         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Infineon\TLE985x\Infineon_TLExxx.pex
T4A00 000:160.514       FlashBankInfo:
T4A00 000:160.519         Name:            NACNAD
T4A00 000:160.525         BaseAddr:        0x10FFFFFC
T4A00 000:160.553         AlwaysPresent:   1
T4A00 000:160.560       FlashBankInfo:
T4A00 000:160.565         Name:            Code Flash
T4A00 000:160.571         BaseAddr:        0x11000000
T4A00 000:160.600         AlwaysPresent:   1
T4A00 000:160.606       FlashBankInfo:
T4A00 000:160.611         Name:            Data Flash
T4A00 000:160.617         BaseAddr:        0x11017000
T4A00 000:160.646         AlwaysPresent:   1
T4A00 000:160.654     Device entry modified: GP570NKDx
T4A00 000:160.674       ChipInfo:
T4A00 000:160.681         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:160.688     Device entry modified: GP570NMDx
T4A00 000:160.707       ChipInfo:
T4A00 000:160.713         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:160.720     Device entry modified: GP570NMEx
T4A00 000:160.739       ChipInfo:
T4A00 000:160.745         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:160.753     Device entry modified: GP870NKCG
T4A00 000:160.773       ChipInfo:
T4A00 000:160.779         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:160.786     Device entry modified: GP870NMDG
T4A00 000:160.806       ChipInfo:
T4A00 000:160.813         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:160.820     Device entry modified: GP870NMEG
T4A00 000:160.839       ChipInfo:
T4A00 000:160.846         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:160.853     Device entry modified: UE878NHCG
T4A00 000:160.872       ChipInfo:
T4A00 000:160.878         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:160.886     Device entry modified: UE878NKDx
T4A00 000:160.905       ChipInfo:
T4A00 000:160.911         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:160.917     Device entry modified: UE878NKEx
T4A00 000:160.937       ChipInfo:
T4A00 000:160.943         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:160.950     Device entry modified: UE878NMDx
T4A00 000:160.969       ChipInfo:
T4A00 000:160.975         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:160.993     Device entry modified: UE878NMEx
T4A00 000:161.014       ChipInfo:
T4A00 000:161.020         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:161.027     Device entry modified: QPG6095x
T4A00 000:161.047       ChipInfo:
T4A00 000:161.053         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:161.061     Device entry modified: CY8C6xx6_CM0p
T4A00 000:161.080       ChipInfo:
T4A00 000:161.086         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM0p.JLinkScript
T4A00 000:161.092         WorkRAMSize:     0x00040000
T4A00 000:161.098       FlashBankInfo:
T4A00 000:161.104         Name:            FLASH  (Main)
T4A00 000:161.109         BaseAddr:        0x10000000
T4A00 000:161.141         AlwaysPresent:   1
T4A00 000:161.152       FlashBankInfo:
T4A00 000:161.157         Name:            WFLASH (Work)
T4A00 000:161.163         BaseAddr:        0x14000000
T4A00 000:161.193         AlwaysPresent:   1
T4A00 000:161.199       FlashBankInfo:
T4A00 000:161.205         Name:            SFLASH: User Data
T4A00 000:161.211         BaseAddr:        0x16000800
T4A00 000:161.241         AlwaysPresent:   1
T4A00 000:161.247       FlashBankInfo:
T4A00 000:161.253         Name:            SFLASH: NAR
T4A00 000:161.259         BaseAddr:        0x16001A00
T4A00 000:161.288         AlwaysPresent:   1
T4A00 000:161.294       FlashBankInfo:
T4A00 000:161.299         Name:            SFLASH: Public Key
T4A00 000:161.305         BaseAddr:        0x16005A00
T4A00 000:161.334         AlwaysPresent:   1
T4A00 000:161.340       FlashBankInfo:
T4A00 000:161.345         Name:            SFLASH: TOC2
T4A00 000:161.351         BaseAddr:        0x16007C00
T4A00 000:161.380         AlwaysPresent:   1
T4A00 000:161.386       FlashBankInfo:
T4A00 000:161.392         Name:            SMIF
T4A00 000:161.398         BaseAddr:        0x18000000
T4A00 000:161.428     Device entry modified: CY8C6xx6_CM0p_sect256KB
T4A00 000:161.448       ChipInfo:
T4A00 000:161.454         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM0p.JLinkScript
T4A00 000:161.460         WorkRAMSize:     0x00040000
T4A00 000:161.465       FlashBankInfo:
T4A00 000:161.471         Name:            FLASH  (Main)
T4A00 000:161.477         BaseAddr:        0x10000000
T4A00 000:161.506         AlwaysPresent:   1
T4A00 000:161.512       FlashBankInfo:
T4A00 000:161.517         Name:            WFLASH (Work)
T4A00 000:161.523         BaseAddr:        0x14000000
T4A00 000:161.552         AlwaysPresent:   1
T4A00 000:161.558       FlashBankInfo:
T4A00 000:161.563         Name:            SFLASH: User Data
T4A00 000:161.569         BaseAddr:        0x16000800
T4A00 000:161.598         AlwaysPresent:   1
T4A00 000:161.605       FlashBankInfo:
T4A00 000:161.610         Name:            SFLASH: NAR
T4A00 000:161.616         BaseAddr:        0x16001A00
T4A00 000:161.644         AlwaysPresent:   1
T4A00 000:161.650       FlashBankInfo:
T4A00 000:161.656         Name:            SFLASH: Public Key
T4A00 000:161.661         BaseAddr:        0x16005A00
T4A00 000:161.690         AlwaysPresent:   1
T4A00 000:161.696       FlashBankInfo:
T4A00 000:161.701         Name:            SFLASH: TOC2
T4A00 000:161.707         BaseAddr:        0x16007C00
T4A00 000:161.735         AlwaysPresent:   1
T4A00 000:161.741       FlashBankInfo:
T4A00 000:161.747         Name:            SMIF
T4A00 000:161.752         BaseAddr:        0x18000000
T4A00 000:161.783     Device entry modified: CY8C6xx6_CM0p_tm
T4A00 000:161.802       ChipInfo:
T4A00 000:161.809         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM0p_tm_xA.JLinkScript
T4A00 000:161.815         WorkRAMSize:     0x00040000
T4A00 000:161.835       FlashBankInfo:
T4A00 000:161.840         Name:            FLASH  (Main)
T4A00 000:161.846         BaseAddr:        0x10000000
T4A00 000:161.877         AlwaysPresent:   1
T4A00 000:161.883       FlashBankInfo:
T4A00 000:161.889         Name:            WFLASH (Work)
T4A00 000:161.895         BaseAddr:        0x14000000
T4A00 000:161.923         AlwaysPresent:   1
T4A00 000:161.929       FlashBankInfo:
T4A00 000:161.935         Name:            SFLASH: User Data
T4A00 000:161.941         BaseAddr:        0x16000800
T4A00 000:161.969         AlwaysPresent:   1
T4A00 000:161.975       FlashBankInfo:
T4A00 000:161.981         Name:            SFLASH: NAR
T4A00 000:161.987         BaseAddr:        0x16001A00
T4A00 000:162.015         AlwaysPresent:   1
T4A00 000:162.021       FlashBankInfo:
T4A00 000:162.027         Name:            SFLASH: Public Key
T4A00 000:162.032         BaseAddr:        0x16005A00
T4A00 000:162.061         AlwaysPresent:   1
T4A00 000:162.067       FlashBankInfo:
T4A00 000:162.073         Name:            SFLASH: TOC2
T4A00 000:162.078         BaseAddr:        0x16007C00
T4A00 000:162.107         AlwaysPresent:   1
T4A00 000:162.113       FlashBankInfo:
T4A00 000:162.118         Name:            SMIF
T4A00 000:162.124         BaseAddr:        0x18000000
T4A00 000:162.154     Device entry modified: CY8C6xx6_CM0p_sect256KB_tm
T4A00 000:162.173       ChipInfo:
T4A00 000:162.180         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM0p_tm_xA.JLinkScript
T4A00 000:162.186         WorkRAMSize:     0x00040000
T4A00 000:162.191       FlashBankInfo:
T4A00 000:162.197         Name:            FLASH  (Main)
T4A00 000:162.203         BaseAddr:        0x10000000
T4A00 000:162.231         AlwaysPresent:   1
T4A00 000:162.237       FlashBankInfo:
T4A00 000:162.243         Name:            WFLASH (Work)
T4A00 000:162.248         BaseAddr:        0x14000000
T4A00 000:162.277         AlwaysPresent:   1
T4A00 000:162.283       FlashBankInfo:
T4A00 000:162.289         Name:            SFLASH: User Data
T4A00 000:162.295         BaseAddr:        0x16000800
T4A00 000:162.323         AlwaysPresent:   1
T4A00 000:162.329       FlashBankInfo:
T4A00 000:162.335         Name:            SFLASH: NAR
T4A00 000:162.340         BaseAddr:        0x16001A00
T4A00 000:162.370         AlwaysPresent:   1
T4A00 000:162.376       FlashBankInfo:
T4A00 000:162.381         Name:            SFLASH: Public Key
T4A00 000:162.387         BaseAddr:        0x16005A00
T4A00 000:162.416         AlwaysPresent:   1
T4A00 000:162.422       FlashBankInfo:
T4A00 000:162.427         Name:            SFLASH: TOC2
T4A00 000:162.433         BaseAddr:        0x16007C00
T4A00 000:162.461         AlwaysPresent:   1
T4A00 000:162.467       FlashBankInfo:
T4A00 000:162.473         Name:            SMIF
T4A00 000:162.478         BaseAddr:        0x18000000
T4A00 000:162.508     Device entry modified: CY8C6xx6_CM4
T4A00 000:162.527       ChipInfo:
T4A00 000:162.534         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM4.JLinkScript
T4A00 000:162.540         WorkRAMSize:     0x00040000
T4A00 000:162.546       FlashBankInfo:
T4A00 000:162.551         Name:            FLASH  (Main)
T4A00 000:162.557         BaseAddr:        0x10000000
T4A00 000:162.586         AlwaysPresent:   1
T4A00 000:162.592       FlashBankInfo:
T4A00 000:162.597         Name:            WFLASH (Work)
T4A00 000:162.603         BaseAddr:        0x14000000
T4A00 000:162.631         AlwaysPresent:   1
T4A00 000:162.637       FlashBankInfo:
T4A00 000:162.643         Name:            SFLASH: User Data
T4A00 000:162.648         BaseAddr:        0x16000800
T4A00 000:162.677         AlwaysPresent:   1
T4A00 000:162.683       FlashBankInfo:
T4A00 000:162.688         Name:            SFLASH: NAR
T4A00 000:162.694         BaseAddr:        0x16001A00
T4A00 000:162.724         AlwaysPresent:   1
T4A00 000:162.730       FlashBankInfo:
T4A00 000:162.736         Name:            SFLASH: Public Key
T4A00 000:162.741         BaseAddr:        0x16005A00
T4A00 000:162.770         AlwaysPresent:   1
T4A00 000:162.776       FlashBankInfo:
T4A00 000:162.781         Name:            SFLASH: TOC2
T4A00 000:162.787         BaseAddr:        0x16007C00
T4A00 000:162.815         AlwaysPresent:   1
T4A00 000:162.821       FlashBankInfo:
T4A00 000:162.827         Name:            SMIF
T4A00 000:162.832         BaseAddr:        0x18000000
T4A00 000:162.863     Device entry modified: CY8C6xx6_CM4_sect256KB
T4A00 000:162.882       ChipInfo:
T4A00 000:162.889         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM4.JLinkScript
T4A00 000:162.894         WorkRAMSize:     0x00040000
T4A00 000:162.901       FlashBankInfo:
T4A00 000:162.907         Name:            FLASH  (Main)
T4A00 000:162.912         BaseAddr:        0x10000000
T4A00 000:162.941         AlwaysPresent:   1
T4A00 000:162.947       FlashBankInfo:
T4A00 000:162.952         Name:            WFLASH (Work)
T4A00 000:162.958         BaseAddr:        0x14000000
T4A00 000:162.987         AlwaysPresent:   1
T4A00 000:162.993       FlashBankInfo:
T4A00 000:162.998         Name:            SFLASH: User Data
T4A00 000:163.004         BaseAddr:        0x16000800
T4A00 000:163.032         AlwaysPresent:   1
T4A00 000:163.038       FlashBankInfo:
T4A00 000:163.044         Name:            SFLASH: NAR
T4A00 000:163.050         BaseAddr:        0x16001A00
T4A00 000:163.079         AlwaysPresent:   1
T4A00 000:163.085       FlashBankInfo:
T4A00 000:163.090         Name:            SFLASH: Public Key
T4A00 000:163.096         BaseAddr:        0x16005A00
T4A00 000:163.125         AlwaysPresent:   1
T4A00 000:163.131       FlashBankInfo:
T4A00 000:163.136         Name:            SFLASH: TOC2
T4A00 000:163.142         BaseAddr:        0x16007C00
T4A00 000:163.170         AlwaysPresent:   1
T4A00 000:163.176       FlashBankInfo:
T4A00 000:163.182         Name:            SMIF
T4A00 000:163.187         BaseAddr:        0x18000000
T4A00 000:163.217     Device entry modified: CY8C6xx7_CM0p
T4A00 000:163.236       ChipInfo:
T4A00 000:163.242         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM0p.JLinkScript
T4A00 000:163.248         WorkRAMSize:     0x00040000
T4A00 000:163.254       FlashBankInfo:
T4A00 000:163.259         Name:            FLASH  (Main)
T4A00 000:163.265         BaseAddr:        0x10000000
T4A00 000:163.294         AlwaysPresent:   1
T4A00 000:163.300       FlashBankInfo:
T4A00 000:163.306         Name:            WFLASH (Work)
T4A00 000:163.312         BaseAddr:        0x14000000
T4A00 000:163.340         AlwaysPresent:   1
T4A00 000:163.346       FlashBankInfo:
T4A00 000:163.352         Name:            SFLASH: User Data
T4A00 000:163.357         BaseAddr:        0x16000800
T4A00 000:163.386         AlwaysPresent:   1
T4A00 000:163.392       FlashBankInfo:
T4A00 000:163.397         Name:            SFLASH: NAR
T4A00 000:163.403         BaseAddr:        0x16001A00
T4A00 000:163.431         AlwaysPresent:   1
T4A00 000:163.437       FlashBankInfo:
T4A00 000:163.443         Name:            SFLASH: Public Key
T4A00 000:163.449         BaseAddr:        0x16005A00
T4A00 000:163.477         AlwaysPresent:   1
T4A00 000:163.483       FlashBankInfo:
T4A00 000:163.489         Name:            SFLASH: TOC2
T4A00 000:163.494         BaseAddr:        0x16007C00
T4A00 000:163.523         AlwaysPresent:   1
T4A00 000:163.529       FlashBankInfo:
T4A00 000:163.534         Name:            SMIF
T4A00 000:163.540         BaseAddr:        0x18000000
T4A00 000:163.570     Device entry modified: CY8C6xx7_CM0p_sect256KB
T4A00 000:163.589       ChipInfo:
T4A00 000:163.596         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM0p.JLinkScript
T4A00 000:163.603         WorkRAMSize:     0x00040000
T4A00 000:163.608       FlashBankInfo:
T4A00 000:163.614         Name:            FLASH  (Main)
T4A00 000:163.619         BaseAddr:        0x10000000
T4A00 000:163.649         AlwaysPresent:   1
T4A00 000:163.655       FlashBankInfo:
T4A00 000:163.660         Name:            WFLASH (Work)
T4A00 000:163.666         BaseAddr:        0x14000000
T4A00 000:163.694         AlwaysPresent:   1
T4A00 000:163.700       FlashBankInfo:
T4A00 000:163.706         Name:            SFLASH: User Data
T4A00 000:163.712         BaseAddr:        0x16000800
T4A00 000:163.740         AlwaysPresent:   1
T4A00 000:163.746       FlashBankInfo:
T4A00 000:163.751         Name:            SFLASH: NAR
T4A00 000:163.757         BaseAddr:        0x16001A00
T4A00 000:163.785         AlwaysPresent:   1
T4A00 000:163.792       FlashBankInfo:
T4A00 000:163.797         Name:            SFLASH: Public Key
T4A00 000:163.803         BaseAddr:        0x16005A00
T4A00 000:163.832         AlwaysPresent:   1
T4A00 000:163.838       FlashBankInfo:
T4A00 000:163.843         Name:            SFLASH: TOC2
T4A00 000:163.849         BaseAddr:        0x16007C00
T4A00 000:163.878         AlwaysPresent:   1
T4A00 000:163.884       FlashBankInfo:
T4A00 000:163.889         Name:            SMIF
T4A00 000:163.895         BaseAddr:        0x18000000
T4A00 000:163.925     Device entry modified: CY8C6xx7_CM0p_tm
T4A00 000:163.944       ChipInfo:
T4A00 000:163.950         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM0p_tm_xA.JLinkScript
T4A00 000:163.956         WorkRAMSize:     0x00040000
T4A00 000:163.962       FlashBankInfo:
T4A00 000:163.967         Name:            FLASH  (Main)
T4A00 000:163.973         BaseAddr:        0x10000000
T4A00 000:164.001         AlwaysPresent:   1
T4A00 000:164.008       FlashBankInfo:
T4A00 000:164.013         Name:            WFLASH (Work)
T4A00 000:164.019         BaseAddr:        0x14000000
T4A00 000:164.047         AlwaysPresent:   1
T4A00 000:164.053       FlashBankInfo:
T4A00 000:164.059         Name:            SFLASH: User Data
T4A00 000:164.064         BaseAddr:        0x16000800
T4A00 000:164.093         AlwaysPresent:   1
T4A00 000:164.099       FlashBankInfo:
T4A00 000:164.105         Name:            SFLASH: NAR
T4A00 000:164.111         BaseAddr:        0x16001A00
T4A00 000:164.140         AlwaysPresent:   1
T4A00 000:164.147       FlashBankInfo:
T4A00 000:164.152         Name:            SFLASH: Public Key
T4A00 000:164.158         BaseAddr:        0x16005A00
T4A00 000:164.187         AlwaysPresent:   1
T4A00 000:164.193       FlashBankInfo:
T4A00 000:164.199         Name:            SFLASH: TOC2
T4A00 000:164.205         BaseAddr:        0x16007C00
T4A00 000:164.233         AlwaysPresent:   1
T4A00 000:164.239       FlashBankInfo:
T4A00 000:164.245         Name:            SMIF
T4A00 000:164.251         BaseAddr:        0x18000000
T4A00 000:164.281     Device entry modified: CY8C6xx7_CM0p_sect256KB_tm
T4A00 000:164.301       ChipInfo:
T4A00 000:164.307         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM0p_tm_xA.JLinkScript
T4A00 000:164.313         WorkRAMSize:     0x00040000
T4A00 000:164.319       FlashBankInfo:
T4A00 000:164.324         Name:            FLASH  (Main)
T4A00 000:164.330         BaseAddr:        0x10000000
T4A00 000:164.359         AlwaysPresent:   1
T4A00 000:164.365       FlashBankInfo:
T4A00 000:164.371         Name:            WFLASH (Work)
T4A00 000:164.377         BaseAddr:        0x14000000
T4A00 000:164.407         AlwaysPresent:   1
T4A00 000:164.413       FlashBankInfo:
T4A00 000:164.419         Name:            SFLASH: User Data
T4A00 000:164.424         BaseAddr:        0x16000800
T4A00 000:164.454         AlwaysPresent:   1
T4A00 000:164.460       FlashBankInfo:
T4A00 000:164.466         Name:            SFLASH: NAR
T4A00 000:164.473         BaseAddr:        0x16001A00
T4A00 000:164.504         AlwaysPresent:   1
T4A00 000:164.510       FlashBankInfo:
T4A00 000:164.515         Name:            SFLASH: Public Key
T4A00 000:164.521         BaseAddr:        0x16005A00
T4A00 000:164.550         AlwaysPresent:   1
T4A00 000:164.556       FlashBankInfo:
T4A00 000:164.562         Name:            SFLASH: TOC2
T4A00 000:164.567         BaseAddr:        0x16007C00
T4A00 000:164.602         AlwaysPresent:   1
T4A00 000:164.609       FlashBankInfo:
T4A00 000:164.614         Name:            SMIF
T4A00 000:164.620         BaseAddr:        0x18000000
T4A00 000:164.652     Device entry modified: CY8C6xx7_CM4
T4A00 000:164.672       ChipInfo:
T4A00 000:164.678         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM4.JLinkScript
T4A00 000:164.684         WorkRAMSize:     0x00040000
T4A00 000:164.690       FlashBankInfo:
T4A00 000:164.696         Name:            FLASH  (Main)
T4A00 000:164.701         BaseAddr:        0x10000000
T4A00 000:164.730         AlwaysPresent:   1
T4A00 000:164.737       FlashBankInfo:
T4A00 000:164.742         Name:            WFLASH (Work)
T4A00 000:164.748         BaseAddr:        0x14000000
T4A00 000:164.779         AlwaysPresent:   1
T4A00 000:164.786       FlashBankInfo:
T4A00 000:164.791         Name:            SFLASH: User Data
T4A00 000:164.797         BaseAddr:        0x16000800
T4A00 000:164.826         AlwaysPresent:   1
T4A00 000:164.833       FlashBankInfo:
T4A00 000:164.838         Name:            SFLASH: NAR
T4A00 000:164.844         BaseAddr:        0x16001A00
T4A00 000:164.874         AlwaysPresent:   1
T4A00 000:164.881       FlashBankInfo:
T4A00 000:164.886         Name:            SFLASH: Public Key
T4A00 000:164.892         BaseAddr:        0x16005A00
T4A00 000:164.922         AlwaysPresent:   1
T4A00 000:164.928       FlashBankInfo:
T4A00 000:164.934         Name:            SFLASH: TOC2
T4A00 000:164.939         BaseAddr:        0x16007C00
T4A00 000:164.968         AlwaysPresent:   1
T4A00 000:164.974       FlashBankInfo:
T4A00 000:164.980         Name:            SMIF
T4A00 000:164.985         BaseAddr:        0x18000000
T4A00 000:165.016     Device entry modified: CY8C6xx7_CM4_sect256KB
T4A00 000:165.036       ChipInfo:
T4A00 000:165.043         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Cypress\PSoC6\CY8C6xxx_CM4.JLinkScript
T4A00 000:165.048         WorkRAMSize:     0x00040000
T4A00 000:165.055       FlashBankInfo:
T4A00 000:165.060         Name:            FLASH  (Main)
T4A00 000:165.066         BaseAddr:        0x10000000
T4A00 000:165.095         AlwaysPresent:   1
T4A00 000:165.101       FlashBankInfo:
T4A00 000:165.107         Name:            WFLASH (Work)
T4A00 000:165.112         BaseAddr:        0x14000000
T4A00 000:165.142         AlwaysPresent:   1
T4A00 000:165.148       FlashBankInfo:
T4A00 000:165.153         Name:            SFLASH: User Data
T4A00 000:165.159         BaseAddr:        0x16000800
T4A00 000:165.188         AlwaysPresent:   1
T4A00 000:165.194       FlashBankInfo:
T4A00 000:165.199         Name:            SFLASH: NAR
T4A00 000:165.205         BaseAddr:        0x16001A00
T4A00 000:165.234         AlwaysPresent:   1
T4A00 000:165.240       FlashBankInfo:
T4A00 000:165.246         Name:            SFLASH: Public Key
T4A00 000:165.251         BaseAddr:        0x16005A00
T4A00 000:165.280         AlwaysPresent:   1
T4A00 000:165.286       FlashBankInfo:
T4A00 000:165.292         Name:            SFLASH: TOC2
T4A00 000:165.297         BaseAddr:        0x16007C00
T4A00 000:165.327         AlwaysPresent:   1
T4A00 000:165.334       FlashBankInfo:
T4A00 000:165.339         Name:            SMIF
T4A00 000:165.345         BaseAddr:        0x18000000
T4A00 000:165.376     Device entry modified: RSL10
T4A00 000:165.386       ChipInfo:
T4A00 000:165.392         Core:            JLINK_CORE_CORTEX_M3_R2P1
T4A00 000:165.418         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ONSemiconductor\RSL10\ONSemiconductor_RSL10.JLinkScript
T4A00 000:165.424       FlashBankInfo:
T4A00 000:165.429         Name:            Main Flash
T4A00 000:165.435         BaseAddr:        0x00100000
T4A00 000:165.469         AlwaysPresent:   1
T4A00 000:165.476       FlashBankInfo:
T4A00 000:165.481         Name:            NVR Flash
T4A00 000:165.487         BaseAddr:        0x00080000
T4A00 000:165.521     Device entry modified: E31ARTY
T4A00 000:165.528       FlashBankInfo:
T4A00 000:165.534         Name:            QSPI Flash
T4A00 000:165.539         BaseAddr:        0x40000000
T4A00 000:165.574     Device entry modified: STM32F412CE
T4A00 000:165.588       FlashBankInfo:
T4A00 000:165.594         Name:            QSPI Flash
T4A00 000:165.599         BaseAddr:        0x90000000
T4A00 000:165.634     Device entry modified: STM32F412RE
T4A00 000:165.639       FlashBankInfo:
T4A00 000:165.645         Name:            QSPI Flash
T4A00 000:165.651         BaseAddr:        0x90000000
T4A00 000:165.681     Device entry modified: STM32F412ZG
T4A00 000:165.686       FlashBankInfo:
T4A00 000:165.692         Name:            QSPI Flash
T4A00 000:165.698         BaseAddr:        0x90000000
T4A00 000:165.728     Device entry modified: STM32F412RG
T4A00 000:165.733       FlashBankInfo:
T4A00 000:165.739         Name:            QSPI Flash
T4A00 000:165.744         BaseAddr:        0x90000000
T4A00 000:165.775     Device entry modified: STM32F412VE
T4A00 000:165.780       FlashBankInfo:
T4A00 000:165.786         Name:            QSPI Flash
T4A00 000:165.791         BaseAddr:        0x90000000
T4A00 000:165.822     Device entry modified: STM32F412VG
T4A00 000:165.827       FlashBankInfo:
T4A00 000:165.832         Name:            QSPI Flash
T4A00 000:165.838         BaseAddr:        0x90000000
T4A00 000:165.868     Device entry modified: STM32F412CG
T4A00 000:165.874       FlashBankInfo:
T4A00 000:165.879         Name:            QSPI Flash
T4A00 000:165.885         BaseAddr:        0x90000000
T4A00 000:165.915     Device entry modified: STM32F412ZE
T4A00 000:165.920       FlashBankInfo:
T4A00 000:165.925         Name:            QSPI Flash
T4A00 000:165.931         BaseAddr:        0x90000000
T4A00 000:165.962     Device entry modified: STM32F469AE
T4A00 000:165.972       FlashBankInfo:
T4A00 000:165.977         Name:            QSPI Flash
T4A00 000:165.983         BaseAddr:        0x90000000
T4A00 000:166.017     Device entry modified: STM32F469AG
T4A00 000:166.022       FlashBankInfo:
T4A00 000:166.028         Name:            QSPI Flash
T4A00 000:166.033         BaseAddr:        0x90000000
T4A00 000:166.064     Device entry modified: STM32F469AI
T4A00 000:166.070       FlashBankInfo:
T4A00 000:166.075         Name:            QSPI Flash
T4A00 000:166.081         BaseAddr:        0x90000000
T4A00 000:166.112     Device entry modified: STM32F469BE
T4A00 000:166.118       FlashBankInfo:
T4A00 000:166.123         Name:            QSPI Flash
T4A00 000:166.129         BaseAddr:        0x90000000
T4A00 000:166.159     Device entry modified: STM32F469BG
T4A00 000:166.165       FlashBankInfo:
T4A00 000:166.170         Name:            QSPI Flash
T4A00 000:166.176         BaseAddr:        0x90000000
T4A00 000:166.207     Device entry modified: STM32F469BI
T4A00 000:166.212       FlashBankInfo:
T4A00 000:166.217         Name:            QSPI Flash
T4A00 000:166.223         BaseAddr:        0x90000000
T4A00 000:166.253     Device entry modified: STM32F469IE
T4A00 000:166.258       FlashBankInfo:
T4A00 000:166.264         Name:            QSPI Flash
T4A00 000:166.269         BaseAddr:        0x90000000
T4A00 000:166.300     Device entry modified: STM32F469IG
T4A00 000:166.306       FlashBankInfo:
T4A00 000:166.311         Name:            QSPI Flash
T4A00 000:166.317         BaseAddr:        0x90000000
T4A00 000:166.348     Device entry modified: STM32F469II
T4A00 000:166.353       FlashBankInfo:
T4A00 000:166.360         Name:            QSPI Flash
T4A00 000:166.366         BaseAddr:        0x90000000
T4A00 000:166.397     Device entry modified: STM32F469NE
T4A00 000:166.402       FlashBankInfo:
T4A00 000:166.407         Name:            QSPI Flash
T4A00 000:166.413         BaseAddr:        0x90000000
T4A00 000:166.444     Device entry modified: STM32F469NG
T4A00 000:166.449       FlashBankInfo:
T4A00 000:166.455         Name:            QSPI Flash
T4A00 000:166.460         BaseAddr:        0x90000000
T4A00 000:166.490     Device entry modified: STM32F469NI
T4A00 000:166.495       FlashBankInfo:
T4A00 000:166.501         Name:            QSPI Flash
T4A00 000:166.506         BaseAddr:        0x90000000
T4A00 000:166.537     Device entry modified: STM32F469VE
T4A00 000:166.543       FlashBankInfo:
T4A00 000:166.548         Name:            QSPI Flash
T4A00 000:166.554         BaseAddr:        0x90000000
T4A00 000:166.584     Device entry modified: STM32F469VG
T4A00 000:166.589       FlashBankInfo:
T4A00 000:166.594         Name:            QSPI Flash
T4A00 000:166.600         BaseAddr:        0x90000000
T4A00 000:166.630     Device entry modified: STM32F469VI
T4A00 000:166.635       FlashBankInfo:
T4A00 000:166.641         Name:            QSPI Flash
T4A00 000:166.646         BaseAddr:        0x90000000
T4A00 000:166.677     Device entry modified: STM32F469ZE
T4A00 000:166.682       FlashBankInfo:
T4A00 000:166.687         Name:            QSPI Flash
T4A00 000:166.693         BaseAddr:        0x90000000
T4A00 000:166.723     Device entry modified: STM32F469ZG
T4A00 000:166.728       FlashBankInfo:
T4A00 000:166.734         Name:            QSPI Flash
T4A00 000:166.739         BaseAddr:        0x90000000
T4A00 000:166.770     Device entry modified: STM32F469ZI
T4A00 000:166.775       FlashBankInfo:
T4A00 000:166.780         Name:            QSPI Flash
T4A00 000:166.786         BaseAddr:        0x90000000
T4A00 000:166.816     Device entry modified: STM32F723IC
T4A00 000:166.825       FlashBankInfo:
T4A00 000:166.831         Name:            QSPI Flash
T4A00 000:166.836         BaseAddr:        0x90000000
T4A00 000:166.870     Device entry modified: STM32F723IE
T4A00 000:166.876       FlashBankInfo:
T4A00 000:166.881         Name:            QSPI Flash
T4A00 000:166.887         BaseAddr:        0x90000000
T4A00 000:166.918     Device entry modified: STM32F723VE
T4A00 000:166.923       FlashBankInfo:
T4A00 000:166.929         Name:            QSPI Flash
T4A00 000:166.934         BaseAddr:        0x90000000
T4A00 000:166.965     Device entry modified: STM32F723ZC
T4A00 000:166.970       FlashBankInfo:
T4A00 000:166.976         Name:            QSPI Flash
T4A00 000:166.981         BaseAddr:        0x90000000
T4A00 000:167.012     Device entry modified: STM32F723ZE
T4A00 000:167.017       FlashBankInfo:
T4A00 000:167.023         Name:            QSPI Flash
T4A00 000:167.029         BaseAddr:        0x90000000
T4A00 000:167.058     Device entry modified: STM32F745IE
T4A00 000:167.063       FlashBankInfo:
T4A00 000:167.069         Name:            QSPI Flash
T4A00 000:167.075         BaseAddr:        0x90000000
T4A00 000:167.106     Device entry modified: STM32F745IG
T4A00 000:167.111       FlashBankInfo:
T4A00 000:167.116         Name:            QSPI Flash
T4A00 000:167.122         BaseAddr:        0x90000000
T4A00 000:167.178     Device entry modified: STM32F745VE
T4A00 000:167.187       FlashBankInfo:
T4A00 000:167.194         Name:            QSPI Flash
T4A00 000:167.200         BaseAddr:        0x90000000
T4A00 000:167.269     Device entry modified: STM32F745VG
T4A00 000:167.281       FlashBankInfo:
T4A00 000:167.289         Name:            QSPI Flash
T4A00 000:167.296         BaseAddr:        0x90000000
T4A00 000:167.341     Device entry modified: STM32F745ZE
T4A00 000:167.349       FlashBankInfo:
T4A00 000:167.354         Name:            QSPI Flash
T4A00 000:167.360         BaseAddr:        0x90000000
T4A00 000:167.390     Device entry modified: STM32F745ZG
T4A00 000:167.401       FlashBankInfo:
T4A00 000:167.407         Name:            QSPI Flash
T4A00 000:167.413         BaseAddr:        0x90000000
T4A00 000:167.456     Device entry modified: STM32F746BE
T4A00 000:167.463       FlashBankInfo:
T4A00 000:167.468         Name:            QSPI Flash
T4A00 000:167.476         BaseAddr:        0x90000000
T4A00 000:167.519     Device entry modified: STM32F746BG
T4A00 000:167.528       FlashBankInfo:
T4A00 000:167.534         Name:            QSPI Flash
T4A00 000:167.540         BaseAddr:        0x90000000
T4A00 000:167.582     Device entry modified: STM32F746IE
T4A00 000:167.590       FlashBankInfo:
T4A00 000:167.598         Name:            QSPI Flash
T4A00 000:167.608         BaseAddr:        0x90000000
T4A00 000:167.654     Device entry modified: STM32F746IG
T4A00 000:167.662       FlashBankInfo:
T4A00 000:167.669         Name:            QSPI Flash
T4A00 000:167.677         BaseAddr:        0x90000000
T4A00 000:167.713     Device entry modified: STM32F746NE
T4A00 000:167.718       FlashBankInfo:
T4A00 000:167.724         Name:            QSPI Flash
T4A00 000:167.730         BaseAddr:        0x90000000
T4A00 000:167.761     Device entry modified: STM32F746NG
T4A00 000:167.766       FlashBankInfo:
T4A00 000:167.772         Name:            QSPI Flash
T4A00 000:167.778         BaseAddr:        0x90000000
T4A00 000:167.807     Device entry modified: STM32F746VE
T4A00 000:167.813       FlashBankInfo:
T4A00 000:167.818         Name:            QSPI Flash
T4A00 000:167.824         BaseAddr:        0x90000000
T4A00 000:167.855     Device entry modified: STM32F746VG
T4A00 000:167.861       FlashBankInfo:
T4A00 000:167.866         Name:            QSPI Flash
T4A00 000:167.872         BaseAddr:        0x90000000
T4A00 000:167.902     Device entry modified: STM32F746ZE
T4A00 000:167.908       FlashBankInfo:
T4A00 000:167.913         Name:            QSPI Flash
T4A00 000:167.919         BaseAddr:        0x90000000
T4A00 000:167.948     Device entry modified: STM32F746ZG
T4A00 000:167.954       FlashBankInfo:
T4A00 000:167.959         Name:            QSPI Flash
T4A00 000:167.965         BaseAddr:        0x90000000
T4A00 000:167.995     Device entry modified: STM32F756BE
T4A00 000:168.001       FlashBankInfo:
T4A00 000:168.006         Name:            QSPI Flash
T4A00 000:168.012         BaseAddr:        0x90000000
T4A00 000:168.042     Device entry modified: STM32F756BG
T4A00 000:168.047       FlashBankInfo:
T4A00 000:168.052         Name:            QSPI Flash
T4A00 000:168.058         BaseAddr:        0x90000000
T4A00 000:168.088     Device entry modified: STM32F756IE
T4A00 000:168.094       FlashBankInfo:
T4A00 000:168.099         Name:            QSPI Flash
T4A00 000:168.105         BaseAddr:        0x90000000
T4A00 000:168.135     Device entry modified: STM32F756IG
T4A00 000:168.140       FlashBankInfo:
T4A00 000:168.145         Name:            QSPI Flash
T4A00 000:168.151         BaseAddr:        0x90000000
T4A00 000:168.181     Device entry modified: STM32F756NE
T4A00 000:168.186       FlashBankInfo:
T4A00 000:168.192         Name:            QSPI Flash
T4A00 000:168.198         BaseAddr:        0x90000000
T4A00 000:168.228     Device entry modified: STM32F756NG
T4A00 000:168.234       FlashBankInfo:
T4A00 000:168.239         Name:            QSPI Flash
T4A00 000:168.245         BaseAddr:        0x90000000
T4A00 000:168.275     Device entry modified: STM32F756VE
T4A00 000:168.281       FlashBankInfo:
T4A00 000:168.286         Name:            QSPI Flash
T4A00 000:168.292         BaseAddr:        0x90000000
T4A00 000:168.322     Device entry modified: STM32F756VG
T4A00 000:168.327       FlashBankInfo:
T4A00 000:168.333         Name:            QSPI Flash
T4A00 000:168.338         BaseAddr:        0x90000000
T4A00 000:168.369     Device entry modified: STM32F756ZE
T4A00 000:168.374       FlashBankInfo:
T4A00 000:168.380         Name:            QSPI Flash
T4A00 000:168.385         BaseAddr:        0x90000000
T4A00 000:168.418     Device entry modified: STM32F756ZG
T4A00 000:168.423       FlashBankInfo:
T4A00 000:168.429         Name:            QSPI Flash
T4A00 000:168.435         BaseAddr:        0x90000000
T4A00 000:168.465     Device entry modified: STM32F765BG
T4A00 000:168.471       FlashBankInfo:
T4A00 000:168.476         Name:            QSPI Flash
T4A00 000:168.482         BaseAddr:        0x90000000
T4A00 000:168.512     Device entry modified: STM32F765BI
T4A00 000:168.524       FlashBankInfo:
T4A00 000:168.529         Name:            QSPI Flash
T4A00 000:168.535         BaseAddr:        0x90000000
T4A00 000:168.566     Device entry modified: STM32F765IG
T4A00 000:168.571       FlashBankInfo:
T4A00 000:168.577         Name:            QSPI Flash
T4A00 000:168.583         BaseAddr:        0x90000000
T4A00 000:168.613     Device entry modified: STM32F765II
T4A00 000:168.618       FlashBankInfo:
T4A00 000:168.624         Name:            QSPI Flash
T4A00 000:168.629         BaseAddr:        0x90000000
T4A00 000:168.662     Device entry modified: STM32F765NG
T4A00 000:168.669       FlashBankInfo:
T4A00 000:168.676         Name:            QSPI Flash
T4A00 000:168.683         BaseAddr:        0x90000000
T4A00 000:168.722     Device entry modified: STM32F765NI
T4A00 000:168.728       FlashBankInfo:
T4A00 000:168.733         Name:            QSPI Flash
T4A00 000:168.739         BaseAddr:        0x90000000
T4A00 000:168.770     Device entry modified: STM32F765VG
T4A00 000:168.776       FlashBankInfo:
T4A00 000:168.782         Name:            QSPI Flash
T4A00 000:168.788         BaseAddr:        0x90000000
T4A00 000:168.819     Device entry modified: STM32F765VI
T4A00 000:168.824       FlashBankInfo:
T4A00 000:168.830         Name:            QSPI Flash
T4A00 000:168.836         BaseAddr:        0x90000000
T4A00 000:168.867     Device entry modified: STM32F765ZG
T4A00 000:168.872       FlashBankInfo:
T4A00 000:168.878         Name:            QSPI Flash
T4A00 000:168.883         BaseAddr:        0x90000000
T4A00 000:168.914     Device entry modified: STM32F765ZI
T4A00 000:168.919       FlashBankInfo:
T4A00 000:168.925         Name:            QSPI Flash
T4A00 000:168.930         BaseAddr:        0x90000000
T4A00 000:168.960     Device entry modified: STM32F767BG
T4A00 000:168.966       FlashBankInfo:
T4A00 000:168.971         Name:            QSPI Flash
T4A00 000:168.977         BaseAddr:        0x90000000
T4A00 000:169.008     Device entry modified: STM32F767BI
T4A00 000:169.014       FlashBankInfo:
T4A00 000:169.019         Name:            QSPI Flash
T4A00 000:169.025         BaseAddr:        0x90000000
T4A00 000:169.057     Device entry modified: STM32F767IG
T4A00 000:169.062       FlashBankInfo:
T4A00 000:169.068         Name:            QSPI Flash
T4A00 000:169.074         BaseAddr:        0x90000000
T4A00 000:169.106     Device entry modified: STM32F767II
T4A00 000:169.112       FlashBankInfo:
T4A00 000:169.117         Name:            QSPI Flash
T4A00 000:169.123         BaseAddr:        0x90000000
T4A00 000:169.154     Device entry modified: STM32F767NG
T4A00 000:169.159       FlashBankInfo:
T4A00 000:169.165         Name:            QSPI Flash
T4A00 000:169.171         BaseAddr:        0x90000000
T4A00 000:169.202     Device entry modified: STM32F767NI
T4A00 000:169.208       FlashBankInfo:
T4A00 000:169.214         Name:            QSPI Flash
T4A00 000:169.219         BaseAddr:        0x90000000
T4A00 000:169.251     Device entry modified: STM32F767VG
T4A00 000:169.257       FlashBankInfo:
T4A00 000:169.263         Name:            QSPI Flash
T4A00 000:169.268         BaseAddr:        0x90000000
T4A00 000:169.301     Device entry modified: STM32F767VI
T4A00 000:169.307       FlashBankInfo:
T4A00 000:169.313         Name:            QSPI Flash
T4A00 000:169.319         BaseAddr:        0x90000000
T4A00 000:169.352     Device entry modified: STM32F767ZG
T4A00 000:169.357       FlashBankInfo:
T4A00 000:169.363         Name:            QSPI Flash
T4A00 000:169.370         BaseAddr:        0x90000000
T4A00 000:169.402     Device entry modified: STM32F767ZI
T4A00 000:169.407       FlashBankInfo:
T4A00 000:169.413         Name:            QSPI Flash
T4A00 000:169.419         BaseAddr:        0x90000000
T4A00 000:169.450     Device entry modified: STM32F768AI
T4A00 000:169.455       FlashBankInfo:
T4A00 000:169.461         Name:            QSPI Flash
T4A00 000:169.466         BaseAddr:        0x90000000
T4A00 000:169.498     Device entry modified: STM32F769AG
T4A00 000:169.503       FlashBankInfo:
T4A00 000:169.509         Name:            QSPI Flash
T4A00 000:169.514         BaseAddr:        0x90000000
T4A00 000:169.546     Device entry modified: STM32F769AI
T4A00 000:169.552       FlashBankInfo:
T4A00 000:169.557         Name:            QSPI Flash
T4A00 000:169.563         BaseAddr:        0x90000000
T4A00 000:169.595     Device entry modified: STM32F769BG
T4A00 000:169.600       FlashBankInfo:
T4A00 000:169.605         Name:            QSPI Flash
T4A00 000:169.611         BaseAddr:        0x90000000
T4A00 000:169.642     Device entry modified: STM32F769BI
T4A00 000:169.647       FlashBankInfo:
T4A00 000:169.653         Name:            QSPI Flash
T4A00 000:169.658         BaseAddr:        0x90000000
T4A00 000:169.690     Device entry modified: STM32F769IG
T4A00 000:169.696       FlashBankInfo:
T4A00 000:169.701         Name:            QSPI Flash
T4A00 000:169.707         BaseAddr:        0x90000000
T4A00 000:169.738     Device entry modified: STM32F769II
T4A00 000:169.743       FlashBankInfo:
T4A00 000:169.749         Name:            QSPI Flash
T4A00 000:169.755         BaseAddr:        0x90000000
T4A00 000:169.786     Device entry modified: STM32F769NG
T4A00 000:169.791       FlashBankInfo:
T4A00 000:169.797         Name:            QSPI Flash
T4A00 000:169.802         BaseAddr:        0x90000000
T4A00 000:169.835     Device entry modified: STM32F769NI
T4A00 000:169.840       FlashBankInfo:
T4A00 000:169.846         Name:            QSPI Flash
T4A00 000:169.851         BaseAddr:        0x90000000
T4A00 000:169.883     Device entry modified: STM32F777BI
T4A00 000:169.888       FlashBankInfo:
T4A00 000:169.894         Name:            QSPI Flash
T4A00 000:169.900         BaseAddr:        0x90000000
T4A00 000:169.933     Device entry modified: STM32F777II
T4A00 000:169.938       FlashBankInfo:
T4A00 000:169.944         Name:            QSPI Flash
T4A00 000:169.949         BaseAddr:        0x90000000
T4A00 000:169.982     Device entry modified: STM32F777NI
T4A00 000:169.987       FlashBankInfo:
T4A00 000:169.993         Name:            QSPI Flash
T4A00 000:169.998         BaseAddr:        0x90000000
T4A00 000:170.030     Device entry modified: STM32F777VI
T4A00 000:170.035       FlashBankInfo:
T4A00 000:170.041         Name:            QSPI Flash
T4A00 000:170.046         BaseAddr:        0x90000000
T4A00 000:170.078     Device entry modified: STM32F777ZI
T4A00 000:170.084       FlashBankInfo:
T4A00 000:170.089         Name:            QSPI Flash
T4A00 000:170.096         BaseAddr:        0x90000000
T4A00 000:170.127     Device entry modified: STM32F778AI
T4A00 000:170.132       FlashBankInfo:
T4A00 000:170.138         Name:            QSPI Flash
T4A00 000:170.143         BaseAddr:        0x90000000
T4A00 000:170.175     Device entry modified: STM32F779AI
T4A00 000:170.180       FlashBankInfo:
T4A00 000:170.186         Name:            QSPI Flash
T4A00 000:170.191         BaseAddr:        0x90000000
T4A00 000:170.223     Device entry modified: STM32F779BI
T4A00 000:170.228       FlashBankInfo:
T4A00 000:170.234         Name:            QSPI Flash
T4A00 000:170.239         BaseAddr:        0x90000000
T4A00 000:170.270     Device entry modified: STM32F779II
T4A00 000:170.275       FlashBankInfo:
T4A00 000:170.281         Name:            QSPI Flash
T4A00 000:170.286         BaseAddr:        0x90000000
T4A00 000:170.317     Device entry modified: STM32F779NI
T4A00 000:170.323       FlashBankInfo:
T4A00 000:170.329         Name:            QSPI Flash
T4A00 000:170.335         BaseAddr:        0x90000000
T4A00 000:170.368     Device entry modified: STM32H743BI
T4A00 000:170.396       ChipInfo:
T4A00 000:170.403         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:170.410     Device entry modified: STM32H743II
T4A00 000:170.430       ChipInfo:
T4A00 000:170.437         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:170.444     Device entry modified: STM32H743VI
T4A00 000:170.464       ChipInfo:
T4A00 000:170.470         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:170.477     Device entry modified: STM32H743XI
T4A00 000:170.497       ChipInfo:
T4A00 000:170.503         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:170.510     Device entry modified: STM32H743ZI
T4A00 000:170.530       ChipInfo:
T4A00 000:170.536         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:170.543     Device entry modified: STM32H753BI
T4A00 000:170.564       ChipInfo:
T4A00 000:170.571         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:170.577     Device entry modified: STM32H753II
T4A00 000:170.598       ChipInfo:
T4A00 000:170.604         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:170.611     Device entry modified: STM32H753VI
T4A00 000:170.631       ChipInfo:
T4A00 000:170.638         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:170.645     Device entry modified: STM32H753XI
T4A00 000:170.664       ChipInfo:
T4A00 000:170.671         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:170.678     Device entry modified: STM32H753ZI
T4A00 000:170.698       ChipInfo:
T4A00 000:170.705         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:170.712     Device entry modified: Z32F0642
T4A00 000:170.718       FlashBankInfo:
T4A00 000:170.724         Name:            Internal Flash
T4A00 000:170.758         AlwaysPresent:   1
T4A00 000:170.766     Device entry modified: CM40z_128_512
T4A00 000:170.792       ChipInfo:
T4A00 000:170.798         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM40\Analog_CM40x.pex
T4A00 000:170.805     Device entry modified: CM40z_128_256
T4A00 000:170.825       ChipInfo:
T4A00 000:170.831         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM40\Analog_CM40x.pex
T4A00 000:170.838     Device entry modified: CM40z_384_2048
T4A00 000:170.857       ChipInfo:
T4A00 000:170.864         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM40\Analog_CM40x.pex
T4A00 000:170.870     Device entry modified: CM40z_128_512
T4A00 000:170.890     Device entry modified: CM40z_128_256
T4A00 000:170.911     Device entry modified: CM40z_384_2048
T4A00 000:170.931     Device entry modified: CM40z_384_2048
T4A00 000:170.951     Device entry modified: CM40z_128_1024
T4A00 000:170.971       ChipInfo:
T4A00 000:170.978         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM40\Analog_CM40x.pex
T4A00 000:170.984     Device entry modified: CM40z_384_2048
T4A00 000:171.005     Device entry modified: CM40z_384_2048
T4A00 000:171.026     Device entry modified: CM40z_384_2048
T4A00 000:171.045     Device entry modified: CM40z_384_2048
T4A00 000:171.067     Device entry modified: ADSP-CM416CSWZ-BF_M0
T4A00 000:171.087       ChipInfo:
T4A00 000:171.095         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:171.102     Device entry modified: ADSP-CM416CSWZ-CF_M0
T4A00 000:171.121       ChipInfo:
T4A00 000:171.127         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:171.134     Device entry modified: ADSP-CM417CSWZ-CF_M0
T4A00 000:171.154       ChipInfo:
T4A00 000:171.160         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:171.167     Device entry modified: ADSP-CM417CSWZ-DF_M0
T4A00 000:171.186       ChipInfo:
T4A00 000:171.192         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:171.200     Device entry modified: ADSP-CM418CBCZ-BF_M0
T4A00 000:171.220       ChipInfo:
T4A00 000:171.226         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:171.233     Device entry modified: ADSP-CM418CBCZ-CF_M0
T4A00 000:171.252       ChipInfo:
T4A00 000:171.259         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:171.267     Device entry modified: ADSP-CM419CBCZ-CF_M0
T4A00 000:171.287       ChipInfo:
T4A00 000:171.293         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:171.300     Device entry modified: ADSP-CM419CBCZ-DF_M0
T4A00 000:171.319       ChipInfo:
T4A00 000:171.326         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:171.333     Device entry modified: ADSP-CM411CBCZ-AF_M4
T4A00 000:171.352       ChipInfo:
T4A00 000:171.358         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:171.365       FlashBankInfo:
T4A00 000:171.370         Name:            Flash Block A
T4A00 000:171.376         BaseAddr:        0x11000000
T4A00 000:171.410         AlwaysPresent:   1
T4A00 000:171.420       FlashBankInfo:
T4A00 000:171.425         Name:            Flash Block B
T4A00 000:171.431         BaseAddr:        0x11080000
T4A00 000:171.461         AlwaysPresent:   1
T4A00 000:171.469     Device entry modified: ADSP-CM411CBCZ-BF_M4
T4A00 000:171.488       ChipInfo:
T4A00 000:171.494         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:171.501       FlashBankInfo:
T4A00 000:171.506         Name:            Flash Block A
T4A00 000:171.512         BaseAddr:        0x11000000
T4A00 000:171.543         AlwaysPresent:   1
T4A00 000:171.549       FlashBankInfo:
T4A00 000:171.555         Name:            Flash Block B
T4A00 000:171.560         BaseAddr:        0x11080000
T4A00 000:171.589         AlwaysPresent:   1
T4A00 000:171.597     Device entry modified: ADSP-CM412CSWZ-AF_M4
T4A00 000:171.616       ChipInfo:
T4A00 000:171.622         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:171.629       FlashBankInfo:
T4A00 000:171.634         Name:            Flash Block A
T4A00 000:171.640         BaseAddr:        0x11000000
T4A00 000:171.669         AlwaysPresent:   1
T4A00 000:171.675       FlashBankInfo:
T4A00 000:171.681         Name:            Flash Block B
T4A00 000:171.686         BaseAddr:        0x11080000
T4A00 000:171.715         AlwaysPresent:   1
T4A00 000:171.723     Device entry modified: ADSP-CM412CBCZ-BF_M4
T4A00 000:171.742       ChipInfo:
T4A00 000:171.749         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:171.756       FlashBankInfo:
T4A00 000:171.762         Name:            Flash Block A
T4A00 000:171.768         BaseAddr:        0x11000000
T4A00 000:171.797         AlwaysPresent:   1
T4A00 000:171.803       FlashBankInfo:
T4A00 000:171.809         Name:            Flash Block B
T4A00 000:171.815         BaseAddr:        0x11080000
T4A00 000:171.845         AlwaysPresent:   1
T4A00 000:171.852     Device entry modified: ADSP-CM413CSWZ-BF_M4
T4A00 000:171.871       ChipInfo:
T4A00 000:171.877         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:171.884       FlashBankInfo:
T4A00 000:171.889         Name:            Flash Block A
T4A00 000:171.895         BaseAddr:        0x11000000
T4A00 000:171.924         AlwaysPresent:   1
T4A00 000:171.930       FlashBankInfo:
T4A00 000:171.935         Name:            Flash Block B
T4A00 000:171.941         BaseAddr:        0x11080000
T4A00 000:171.970         AlwaysPresent:   1
T4A00 000:171.977     Device entry modified: ADSP-CM413CSWZ-CF_M4
T4A00 000:171.996       ChipInfo:
T4A00 000:172.002         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:172.009       FlashBankInfo:
T4A00 000:172.014         Name:            Flash Block A
T4A00 000:172.020         BaseAddr:        0x11000000
T4A00 000:172.049         AlwaysPresent:   1
T4A00 000:172.055       FlashBankInfo:
T4A00 000:172.061         Name:            Flash Block B
T4A00 000:172.067         BaseAddr:        0x11080000
T4A00 000:172.096         AlwaysPresent:   1
T4A00 000:172.104     Device entry modified: ADSP-CM416CSWZ-BF_M4
T4A00 000:172.122       ChipInfo:
T4A00 000:172.129         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:172.135       FlashBankInfo:
T4A00 000:172.141         Name:            Flash Block A
T4A00 000:172.146         BaseAddr:        0x11000000
T4A00 000:172.175         AlwaysPresent:   1
T4A00 000:172.181       FlashBankInfo:
T4A00 000:172.186         Name:            Flash Block B
T4A00 000:172.192         BaseAddr:        0x11080000
T4A00 000:172.221         AlwaysPresent:   1
T4A00 000:172.228     Device entry modified: ADSP-CM416CSWZ-CF_M4
T4A00 000:172.248       ChipInfo:
T4A00 000:172.254         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:172.261       FlashBankInfo:
T4A00 000:172.266         Name:            Flash Block A
T4A00 000:172.272         BaseAddr:        0x11000000
T4A00 000:172.301         AlwaysPresent:   1
T4A00 000:172.307       FlashBankInfo:
T4A00 000:172.312         Name:            Flash Block B
T4A00 000:172.318         BaseAddr:        0x11080000
T4A00 000:172.346         AlwaysPresent:   1
T4A00 000:172.354     Device entry modified: ADSP-CM417CSWZ-CF_M4
T4A00 000:172.372       ChipInfo:
T4A00 000:172.379         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:172.386       FlashBankInfo:
T4A00 000:172.391         Name:            Flash Block A
T4A00 000:172.397         BaseAddr:        0x11000000
T4A00 000:172.425         AlwaysPresent:   1
T4A00 000:172.432       FlashBankInfo:
T4A00 000:172.437         Name:            Flash Block B
T4A00 000:172.443         BaseAddr:        0x11080000
T4A00 000:172.471         AlwaysPresent:   1
T4A00 000:172.479     Device entry modified: ADSP-CM417CSWZ-DF_M4
T4A00 000:172.498       ChipInfo:
T4A00 000:172.504         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:172.510       FlashBankInfo:
T4A00 000:172.516         Name:            Flash Block A
T4A00 000:172.522         BaseAddr:        0x11000000
T4A00 000:172.553         AlwaysPresent:   1
T4A00 000:172.559       FlashBankInfo:
T4A00 000:172.564         Name:            Flash Block B
T4A00 000:172.570         BaseAddr:        0x11080000
T4A00 000:172.599         AlwaysPresent:   1
T4A00 000:172.607     Device entry modified: ADSP-CM418CBCZ-BF_M4
T4A00 000:172.625       ChipInfo:
T4A00 000:172.632         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:172.638       FlashBankInfo:
T4A00 000:172.644         Name:            Flash Block A
T4A00 000:172.649         BaseAddr:        0x11000000
T4A00 000:172.678         AlwaysPresent:   1
T4A00 000:172.684       FlashBankInfo:
T4A00 000:172.690         Name:            Flash Block B
T4A00 000:172.696         BaseAddr:        0x11080000
T4A00 000:172.725         AlwaysPresent:   1
T4A00 000:172.733     Device entry modified: ADSP-CM418CBCZ-CF_M4
T4A00 000:172.752       ChipInfo:
T4A00 000:172.758         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:172.765       FlashBankInfo:
T4A00 000:172.770         Name:            Flash Block A
T4A00 000:172.776         BaseAddr:        0x11000000
T4A00 000:172.805         AlwaysPresent:   1
T4A00 000:172.811       FlashBankInfo:
T4A00 000:172.817         Name:            Flash Block B
T4A00 000:172.822         BaseAddr:        0x11080000
T4A00 000:172.851         AlwaysPresent:   1
T4A00 000:172.859     Device entry modified: ADSP-CM419CBCZ-CF_M4
T4A00 000:172.877       ChipInfo:
T4A00 000:172.884         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:172.890       FlashBankInfo:
T4A00 000:172.896         Name:            Flash Block A
T4A00 000:172.901         BaseAddr:        0x11000000
T4A00 000:172.930         AlwaysPresent:   1
T4A00 000:172.936       FlashBankInfo:
T4A00 000:172.942         Name:            Flash Block B
T4A00 000:172.947         BaseAddr:        0x11080000
T4A00 000:172.976         AlwaysPresent:   1
T4A00 000:172.983     Device entry modified: ADSP-CM419CBCZ-DF_M4
T4A00 000:173.002       ChipInfo:
T4A00 000:173.008         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:173.015       FlashBankInfo:
T4A00 000:173.020         Name:            Flash Block A
T4A00 000:173.026         BaseAddr:        0x11000000
T4A00 000:173.054         AlwaysPresent:   1
T4A00 000:173.060       FlashBankInfo:
T4A00 000:173.066         Name:            Flash Block B
T4A00 000:173.072         BaseAddr:        0x11080000
T4A00 000:173.100         AlwaysPresent:   1
T4A00 000:173.108     Device entry modified: ADuCM4050
T4A00 000:173.113       FlashBankInfo:
T4A00 000:173.119         Name:            Flash Block
T4A00 000:173.149         AlwaysPresent:   1
T4A00 000:173.157     Device entry modified: MAX32600
T4A00 000:173.165       FlashBankInfo:
T4A00 000:173.171         Name:            Internal Flash
T4A00 000:173.203         AlwaysPresent:   1
T4A00 000:173.211     Device entry modified: ATSAML10D14A
T4A00 000:173.235       ChipInfo:
T4A00 000:173.241         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.248     Device entry modified: ATSAML10D15A
T4A00 000:173.267       ChipInfo:
T4A00 000:173.274         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.280     Device entry modified: ATSAML10D16A
T4A00 000:173.299       ChipInfo:
T4A00 000:173.306         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.312     Device entry modified: ATSAML10E14A
T4A00 000:173.331       ChipInfo:
T4A00 000:173.338         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.345     Device entry modified: ATSAML10E15A
T4A00 000:173.365       ChipInfo:
T4A00 000:173.371         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.378     Device entry modified: ATSAML10E16A
T4A00 000:173.397       ChipInfo:
T4A00 000:173.403         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.410     Device entry modified: ATSAML11D14A
T4A00 000:173.429       ChipInfo:
T4A00 000:173.435         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.442     Device entry modified: ATSAML11D15A
T4A00 000:173.461       ChipInfo:
T4A00 000:173.467         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.474     Device entry modified: ATSAML11D16A
T4A00 000:173.493       ChipInfo:
T4A00 000:173.500         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.507     Device entry modified: ATSAML11E14A
T4A00 000:173.525       ChipInfo:
T4A00 000:173.532         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.538     Device entry modified: ATSAML11E15A
T4A00 000:173.558       ChipInfo:
T4A00 000:173.564         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.571     Device entry modified: ATSAML11E16A
T4A00 000:173.590       ChipInfo:
T4A00 000:173.596         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:173.603     Device entry modified: ARTIK05X
T4A00 000:173.624       ChipInfo:
T4A00 000:173.630         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\Samsung\ARTIK05X.JLinkScript
T4A00 000:173.637     Device entry created:  HC32L176
T4A00 000:173.641       ChipInfo:
T4A00 000:173.646         Vendor:          HDSC
T4A00 000:173.652         Name:            HC32L176
T4A00 000:173.657         WorkRAMAddr:     0x20000000
T4A00 000:173.663         WorkRAMSize:     0x00002000
T4A00 000:173.669         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:173.675       FlashBankInfo:
T4A00 000:173.680         Name:            Flash_128K
T4A00 000:173.724         AlwaysPresent:   1
T4A00 000:173.730         LoaderInfo:
T4A00 000:173.735           Name:            Flash_128K
T4A00 000:173.741           MaxSize:         0x00020000
T4A00 000:173.747           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32L17X_128K.FLM
T4A00 000:173.753           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:173.768     Device entry created:  HC32L136
T4A00 000:173.772       ChipInfo:
T4A00 000:173.777         Vendor:          HDSC
T4A00 000:173.783         Name:            HC32L136
T4A00 000:173.788         WorkRAMAddr:     0x20000000
T4A00 000:173.794         WorkRAMSize:     0x00002000
T4A00 000:173.800         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:173.806       FlashBankInfo:
T4A00 000:173.811         Name:            Flash_64K
T4A00 000:173.851         AlwaysPresent:   1
T4A00 000:173.857         LoaderInfo:
T4A00 000:173.862           Name:            Flash_64K
T4A00 000:173.868           MaxSize:         0x00010000
T4A00 000:173.874           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32L13X_64K.FLM
T4A00 000:173.880           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:173.889     Device entry created:  HC32L130
T4A00 000:173.893       ChipInfo:
T4A00 000:173.899         Vendor:          HDSC
T4A00 000:173.904         Name:            HC32L130
T4A00 000:173.911         WorkRAMAddr:     0x20000000
T4A00 000:173.916         WorkRAMSize:     0x00002000
T4A00 000:173.922         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:173.928       FlashBankInfo:
T4A00 000:173.933         Name:            Flash_64K
T4A00 000:173.980         AlwaysPresent:   1
T4A00 000:173.986         LoaderInfo:
T4A00 000:173.992           Name:            Flash_64K
T4A00 000:173.997           MaxSize:         0x00010000
T4A00 000:174.003           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32L13X_64K.FLM
T4A00 000:174.009           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:174.019     Device entry created:  HC32F030
T4A00 000:174.023       ChipInfo:
T4A00 000:174.028         Vendor:          HDSC
T4A00 000:174.034         Name:            HC32F030
T4A00 000:174.039         WorkRAMAddr:     0x20000000
T4A00 000:174.045         WorkRAMSize:     0x00002000
T4A00 000:174.051         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:174.056       FlashBankInfo:
T4A00 000:174.062         Name:            Flash_64K
T4A00 000:174.099         AlwaysPresent:   1
T4A00 000:174.105         LoaderInfo:
T4A00 000:174.111           Name:            Flash_64K
T4A00 000:174.116           MaxSize:         0x00010000
T4A00 000:174.122           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32F030_64K.FLM
T4A00 000:174.128           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:174.137     Device entry created:  HC32L110x4
T4A00 000:174.141       ChipInfo:
T4A00 000:174.146         Vendor:          HDSC
T4A00 000:174.152         Name:            HC32L110x4
T4A00 000:174.158         WorkRAMAddr:     0x20000000
T4A00 000:174.163         WorkRAMSize:     0x00000800
T4A00 000:174.169         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:174.175       FlashBankInfo:
T4A00 000:174.180         Name:            Flash_16K
T4A00 000:174.257         AlwaysPresent:   1
T4A00 000:174.264         LoaderInfo:
T4A00 000:174.269           Name:            Flash_16K
T4A00 000:174.275           MaxSize:         0x00004000
T4A00 000:174.281           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32L110_16K.FLM
T4A00 000:174.287           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:174.296     Device entry created:  HC32L110x6
T4A00 000:174.300       ChipInfo:
T4A00 000:174.306         Vendor:          HDSC
T4A00 000:174.311         Name:            HC32L110x6
T4A00 000:174.317         WorkRAMAddr:     0x20000000
T4A00 000:174.323         WorkRAMSize:     0x00001000
T4A00 000:174.333         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:174.339       FlashBankInfo:
T4A00 000:174.344         Name:            Flash_32K
T4A00 000:174.384         AlwaysPresent:   1
T4A00 000:174.390         LoaderInfo:
T4A00 000:174.395           Name:            Flash_32K
T4A00 000:174.401           MaxSize:         0x00008000
T4A00 000:174.407           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32L110_32K.FLM
T4A00 000:174.412           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:174.420     Device entry created:  HC32F003
T4A00 000:174.424       ChipInfo:
T4A00 000:174.429         Vendor:          HDSC
T4A00 000:174.435         Name:            HC32F003
T4A00 000:174.440         WorkRAMAddr:     0x20000000
T4A00 000:174.446         WorkRAMSize:     0x00000800
T4A00 000:174.452         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:174.457       FlashBankInfo:
T4A00 000:174.463         Name:            Flash_16K
T4A00 000:174.501         AlwaysPresent:   1
T4A00 000:174.507         LoaderInfo:
T4A00 000:174.512           Name:            Flash_16K
T4A00 000:174.518           MaxSize:         0x00004000
T4A00 000:174.524           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32F003_16K.FLM
T4A00 000:174.529           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:174.542     Device entry created:  HC32F005
T4A00 000:174.546       ChipInfo:
T4A00 000:174.551         Vendor:          HDSC
T4A00 000:174.557         Name:            HC32F005
T4A00 000:174.562         WorkRAMAddr:     0x20000000
T4A00 000:174.568         WorkRAMSize:     0x00001000
T4A00 000:174.574         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:174.579       FlashBankInfo:
T4A00 000:174.585         Name:            Flash_32K
T4A00 000:174.623         AlwaysPresent:   1
T4A00 000:174.629         LoaderInfo:
T4A00 000:174.635           Name:            Flash_32K
T4A00 000:174.640           MaxSize:         0x00008000
T4A00 000:174.646           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32F005_32K.FLM
T4A00 000:174.652           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:174.660     Device entry created:  HC32L15
T4A00 000:174.664       ChipInfo:
T4A00 000:174.670         Vendor:          HDSC
T4A00 000:174.675         Name:            HC32L15
T4A00 000:174.681         WorkRAMAddr:     0x20000000
T4A00 000:174.686         WorkRAMSize:     0x00001800
T4A00 000:174.692         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:174.698       FlashBankInfo:
T4A00 000:174.703         Name:            Flash_128K
T4A00 000:174.743         AlwaysPresent:   1
T4A00 000:174.749         LoaderInfo:
T4A00 000:174.755           Name:            Flash_128K
T4A00 000:174.760           MaxSize:         0x00020000
T4A00 000:174.766           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32L15.FLM
T4A00 000:174.772           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:174.780     Device entry created:  HC32F_M14
T4A00 000:174.784       ChipInfo:
T4A00 000:174.790         Vendor:          HDSC
T4A00 000:174.795         Name:            HC32F_M14
T4A00 000:174.801         WorkRAMAddr:     0x20000000
T4A00 000:174.806         WorkRAMSize:     0x00002000
T4A00 000:174.812         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:174.818       FlashBankInfo:
T4A00 000:174.823         Name:            Flash_128K
T4A00 000:174.861         AlwaysPresent:   1
T4A00 000:174.867         LoaderInfo:
T4A00 000:174.872           Name:            Flash_128K
T4A00 000:174.878           MaxSize:         0x00020000
T4A00 000:174.884           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F_M14.FLM
T4A00 000:174.889           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:174.899     Device entry created:  HC32L19x
T4A00 000:174.903       ChipInfo:
T4A00 000:174.909         Vendor:          HDSC
T4A00 000:174.914         Name:            HC32L19x
T4A00 000:174.920         WorkRAMAddr:     0x20000000
T4A00 000:174.925         WorkRAMSize:     0x00008000
T4A00 000:174.931         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:174.936       FlashBankInfo:
T4A00 000:174.942         Name:            Flash_256K
T4A00 000:174.980         AlwaysPresent:   1
T4A00 000:174.985         LoaderInfo:
T4A00 000:174.992           Name:            Flash_256K
T4A00 000:174.997           MaxSize:         0x00040000
T4A00 000:175.003           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32L19X_256K.FLM
T4A00 000:175.009           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:175.016     Device entry created:  HC32F19x
T4A00 000:175.020       ChipInfo:
T4A00 000:175.025         Vendor:          HDSC
T4A00 000:175.031         Name:            HC32F19x
T4A00 000:175.036         WorkRAMAddr:     0x20000000
T4A00 000:175.042         WorkRAMSize:     0x00008000
T4A00 000:175.048         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:175.053       FlashBankInfo:
T4A00 000:175.059         Name:            Flash_256K
T4A00 000:175.096         AlwaysPresent:   1
T4A00 000:175.102         LoaderInfo:
T4A00 000:175.107           Name:            Flash_256K
T4A00 000:175.113           MaxSize:         0x00040000
T4A00 000:175.120           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32F19X_256K.FLM
T4A00 000:175.126           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:175.135     Device entry created:  HC32F17x
T4A00 000:175.139       ChipInfo:
T4A00 000:175.145         Vendor:          HDSC
T4A00 000:175.150         Name:            HC32F17x
T4A00 000:175.156         WorkRAMAddr:     0x20000000
T4A00 000:175.162         WorkRAMSize:     0x00004000
T4A00 000:175.167         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:175.173       FlashBankInfo:
T4A00 000:175.178         Name:            Flash_128K
T4A00 000:175.216         AlwaysPresent:   1
T4A00 000:175.222         LoaderInfo:
T4A00 000:175.227           Name:            Flash_128K
T4A00 000:175.233           MaxSize:         0x00020000
T4A00 000:175.239           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32F17X_128K.FLM
T4A00 000:175.244           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:175.254     Device entry created:  HC32L17x
T4A00 000:175.258       ChipInfo:
T4A00 000:175.263         Vendor:          HDSC
T4A00 000:175.269         Name:            HC32L17x
T4A00 000:175.274         WorkRAMAddr:     0x20000000
T4A00 000:175.280         WorkRAMSize:     0x00004000
T4A00 000:175.286         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:175.291       FlashBankInfo:
T4A00 000:175.297         Name:            Flash_128K
T4A00 000:175.333         AlwaysPresent:   1
T4A00 000:175.339         LoaderInfo:
T4A00 000:175.344           Name:            Flash_128K
T4A00 000:175.350           MaxSize:         0x00020000
T4A00 000:175.356           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32L17X_128K.FLM
T4A00 000:175.361           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:175.373     Device entry created:  HC32F072
T4A00 000:175.377       ChipInfo:
T4A00 000:175.383         Vendor:          HDSC
T4A00 000:175.388         Name:            HC32F072
T4A00 000:175.394         WorkRAMAddr:     0x20000000
T4A00 000:175.399         WorkRAMSize:     0x00004000
T4A00 000:175.405         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:175.411       FlashBankInfo:
T4A00 000:175.416         Name:            Flash_128K
T4A00 000:175.453         AlwaysPresent:   1
T4A00 000:175.459         LoaderInfo:
T4A00 000:175.465           Name:            Flash_128K
T4A00 000:175.470           MaxSize:         0x00020000
T4A00 000:175.476           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32F072_128K.FLM
T4A00 000:175.482           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:175.491     Device entry created:  HC32L07X
T4A00 000:175.495       ChipInfo:
T4A00 000:175.500         Vendor:          HDSC
T4A00 000:175.506         Name:            HC32L07X
T4A00 000:175.512         WorkRAMAddr:     0x20000000
T4A00 000:175.517         WorkRAMSize:     0x00004000
T4A00 000:175.523         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:175.528       FlashBankInfo:
T4A00 000:175.534         Name:            Flash_128K
T4A00 000:175.571         AlwaysPresent:   1
T4A00 000:175.576         LoaderInfo:
T4A00 000:175.582           Name:            Flash_128K
T4A00 000:175.588           MaxSize:         0x00020000
T4A00 000:175.593           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\FlashHC32L07X_128K.FLM
T4A00 000:175.599           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:175.607     Device entry created:  HC32F120x6
T4A00 000:175.611       ChipInfo:
T4A00 000:175.616         Vendor:          HDSC
T4A00 000:175.622         Name:            HC32F120x6
T4A00 000:175.628         WorkRAMAddr:     0x20000000
T4A00 000:175.633         WorkRAMSize:     0x00001000
T4A00 000:175.639         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:175.645       FlashBankInfo:
T4A00 000:175.651         Name:            Flash_32K
T4A00 000:175.689         AlwaysPresent:   1
T4A00 000:175.695         LoaderInfo:
T4A00 000:175.700           Name:            Flash_32K
T4A00 000:175.706           MaxSize:         0x00008000
T4A00 000:175.712           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F120_32K.FLM
T4A00 000:175.718           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:175.727     Device entry created:  HC32F120x8
T4A00 000:175.731       ChipInfo:
T4A00 000:175.736         Vendor:          HDSC
T4A00 000:175.742         Name:            HC32F120x8
T4A00 000:175.747         WorkRAMAddr:     0x20000000
T4A00 000:175.753         WorkRAMSize:     0x00001000
T4A00 000:175.758         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:175.764       FlashBankInfo:
T4A00 000:175.769         Name:            Flash_64K
T4A00 000:175.806         AlwaysPresent:   1
T4A00 000:175.812         LoaderInfo:
T4A00 000:175.817           Name:            Flash_64K
T4A00 000:175.823           MaxSize:         0x00010000
T4A00 000:175.829           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F120_64K.FLM
T4A00 000:175.834           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:175.842     Device entry created:  HC32F160xA
T4A00 000:175.846       ChipInfo:
T4A00 000:175.851         Vendor:          HDSC
T4A00 000:175.857         Name:            HC32F160xA
T4A00 000:175.863         WorkRAMAddr:     0x20000000
T4A00 000:175.868         WorkRAMSize:     0x00008000
T4A00 000:175.874         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:175.879       FlashBankInfo:
T4A00 000:175.885         Name:            Flash_128K
T4A00 000:175.922         AlwaysPresent:   1
T4A00 000:175.928         LoaderInfo:
T4A00 000:175.933           Name:            Flash_128K
T4A00 000:175.939           MaxSize:         0x00020000
T4A00 000:175.945           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F160_128K.FLM
T4A00 000:175.950           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:175.956       FlashBankInfo:
T4A00 000:175.961         Name:            Flash_1K
T4A00 000:175.967         BaseAddr:        0x01000800
T4A00 000:176.003         AlwaysPresent:   1
T4A00 000:176.009         LoaderInfo:
T4A00 000:176.015           Name:            Flash_1K
T4A00 000:176.020           MaxSize:         0x00000400
T4A00 000:176.026           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F160_1K.FLM
T4A00 000:176.032           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:176.040     Device entry created:  HC32F160xC
T4A00 000:176.043       ChipInfo:
T4A00 000:176.049         Vendor:          HDSC
T4A00 000:176.054         Name:            HC32F160xC
T4A00 000:176.060         WorkRAMAddr:     0x20000000
T4A00 000:176.065         WorkRAMSize:     0x00008000
T4A00 000:176.071         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:176.077       FlashBankInfo:
T4A00 000:176.082         Name:            Flash_256K
T4A00 000:176.119         AlwaysPresent:   1
T4A00 000:176.125         LoaderInfo:
T4A00 000:176.131           Name:            Flash_256K
T4A00 000:176.136           MaxSize:         0x00040000
T4A00 000:176.142           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F160_256K.FLM
T4A00 000:176.148           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:176.154       FlashBankInfo:
T4A00 000:176.159         Name:            Flash_1K
T4A00 000:176.165         BaseAddr:        0x01000800
T4A00 000:176.200         AlwaysPresent:   1
T4A00 000:176.206         LoaderInfo:
T4A00 000:176.211           Name:            Flash_1K
T4A00 000:176.217           MaxSize:         0x00000400
T4A00 000:176.223           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F160_1K.FLM
T4A00 000:176.228           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:176.238     Device entry modified: HC32F460xC
T4A00 000:176.242       ChipInfo:
T4A00 000:176.248         WorkRAMAddr:     0x1FFF8000
T4A00 000:176.253         WorkRAMSize:     0x0002F000
T4A00 000:176.260       FlashBankInfo:
T4A00 000:176.265         Name:            Flash_256K
T4A00 000:176.302         AlwaysPresent:   1
T4A00 000:176.308         LoaderInfo:
T4A00 000:176.313           Name:            Flash_256K
T4A00 000:176.319           MaxSize:         0x00040000
T4A00 000:176.325           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F460_256K.FLM
T4A00 000:176.330           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:176.338     Device entry modified: HC32F460xE
T4A00 000:176.341       ChipInfo:
T4A00 000:176.347         WorkRAMAddr:     0x1FFF8000
T4A00 000:176.353         WorkRAMSize:     0x0002F000
T4A00 000:176.359       FlashBankInfo:
T4A00 000:176.365         Name:            Flash_512K
T4A00 000:176.402         AlwaysPresent:   1
T4A00 000:176.413         LoaderInfo:
T4A00 000:176.418           Name:            Flash_512K
T4A00 000:176.424           MaxSize:         0x00080000
T4A00 000:176.430           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F460_512K.FLM
T4A00 000:176.436           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:176.443     Device entry created:  HC32F4A0xG
T4A00 000:176.446       ChipInfo:
T4A00 000:176.452         Vendor:          HDSC
T4A00 000:176.457         Name:            HC32F4A0xG
T4A00 000:176.463         WorkRAMAddr:     0x1FFE0000
T4A00 000:176.469         WorkRAMSize:     0x00080000
T4A00 000:176.475         Core:            JLINK_CORE_CORTEX_M4
T4A00 000:176.481       FlashBankInfo:
T4A00 000:176.486         Name:            Flash_1M
T4A00 000:176.523         AlwaysPresent:   1
T4A00 000:176.529         LoaderInfo:
T4A00 000:176.535           Name:            Flash_1M
T4A00 000:176.540           MaxSize:         0x00100000
T4A00 000:176.546           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F4A0_1M.FLM
T4A00 000:176.552           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:176.559     Device entry created:  HC32F4A0xI
T4A00 000:176.562       ChipInfo:
T4A00 000:176.568         Vendor:          HDSC
T4A00 000:176.573         Name:            HC32F4A0xI
T4A00 000:176.579         WorkRAMAddr:     0x1FFE0000
T4A00 000:176.585         WorkRAMSize:     0x00080000
T4A00 000:176.591         Core:            JLINK_CORE_CORTEX_M4
T4A00 000:176.596       FlashBankInfo:
T4A00 000:176.602         Name:            Flash_2M
T4A00 000:176.638         AlwaysPresent:   1
T4A00 000:176.644         LoaderInfo:
T4A00 000:176.650           Name:            Flash_2M
T4A00 000:176.655           MaxSize:         0x00200000
T4A00 000:176.661           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F4A0_2M.FLM
T4A00 000:176.666           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:176.674     Device entry created:  HC32M120x6
T4A00 000:176.678       ChipInfo:
T4A00 000:176.684         Vendor:          HDSC
T4A00 000:176.689         Name:            HC32M120x6
T4A00 000:176.695         WorkRAMAddr:     0x20000000
T4A00 000:176.700         WorkRAMSize:     0x00001000
T4A00 000:176.706         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:176.712       FlashBankInfo:
T4A00 000:176.717         Name:            Flash_32K
T4A00 000:176.754         AlwaysPresent:   1
T4A00 000:176.760         LoaderInfo:
T4A00 000:176.765           Name:            Flash_32K
T4A00 000:176.771           MaxSize:         0x00008000
T4A00 000:176.777           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32M120_32K.FLM
T4A00 000:176.783           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:176.790     Device entry created:  HC32M423xA
T4A00 000:176.794       ChipInfo:
T4A00 000:176.801         Vendor:          HDSC
T4A00 000:176.806         Name:            HC32M423xA
T4A00 000:176.812         WorkRAMAddr:     0x1FFFE000
T4A00 000:176.817         WorkRAMSize:     0x00004000
T4A00 000:176.824         Core:            JLINK_CORE_CORTEX_M4
T4A00 000:176.829       FlashBankInfo:
T4A00 000:176.835         Name:            Flash_128K
T4A00 000:176.871         AlwaysPresent:   1
T4A00 000:176.877         LoaderInfo:
T4A00 000:176.883           Name:            Flash_128K
T4A00 000:176.888           MaxSize:         0x00020000
T4A00 000:176.894           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32M423_128K.FLM
T4A00 000:176.900           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:176.907     Device entry created:  HC32L16xC
T4A00 000:176.911       ChipInfo:
T4A00 000:176.916         Vendor:          HDSC
T4A00 000:176.922         Name:            HC32L16xC
T4A00 000:176.927         WorkRAMAddr:     0x20000000
T4A00 000:176.933         WorkRAMSize:     0x00008000
T4A00 000:176.939         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:176.944       FlashBankInfo:
T4A00 000:176.950         Name:            Flash_256K
T4A00 000:176.990         AlwaysPresent:   1
T4A00 000:176.996         LoaderInfo:
T4A00 000:177.001           Name:            Flash_256K
T4A00 000:177.007           MaxSize:         0x00040000
T4A00 000:177.013           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32L16x_256KB.FLM
T4A00 000:177.018           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:177.028     Device entry created:  HC32L16xA
T4A00 000:177.032       ChipInfo:
T4A00 000:177.037         Vendor:          HDSC
T4A00 000:177.043         Name:            HC32L16xA
T4A00 000:177.048         WorkRAMAddr:     0x20000000
T4A00 000:177.054         WorkRAMSize:     0x00004000
T4A00 000:177.060         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:177.065       FlashBankInfo:
T4A00 000:177.071         Name:            Flash_128K
T4A00 000:177.109         AlwaysPresent:   1
T4A00 000:177.115         LoaderInfo:
T4A00 000:177.120           Name:            Flash_128K
T4A00 000:177.126           MaxSize:         0x00020000
T4A00 000:177.131           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32L16x_128KB.FLM
T4A00 000:177.137           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:177.147     Device entry created:  HC32L18xC
T4A00 000:177.151       ChipInfo:
T4A00 000:177.156         Vendor:          HDSC
T4A00 000:177.162         Name:            HC32L18xC
T4A00 000:177.167         WorkRAMAddr:     0x20000000
T4A00 000:177.173         WorkRAMSize:     0x00008000
T4A00 000:177.178         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:177.184       FlashBankInfo:
T4A00 000:177.189         Name:            Flash_256K
T4A00 000:177.227         AlwaysPresent:   1
T4A00 000:177.232         LoaderInfo:
T4A00 000:177.238           Name:            Flash_256K
T4A00 000:177.243           MaxSize:         0x00040000
T4A00 000:177.249           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32L18x_256KB.FLM
T4A00 000:177.255           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:177.262     Device entry created:  HC32L18xA
T4A00 000:177.266       ChipInfo:
T4A00 000:177.271         Vendor:          HDSC
T4A00 000:177.277         Name:            HC32L18xA
T4A00 000:177.282         WorkRAMAddr:     0x20000000
T4A00 000:177.288         WorkRAMSize:     0x00008000
T4A00 000:177.294         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:177.299       FlashBankInfo:
T4A00 000:177.305         Name:            Flash_128K
T4A00 000:177.340         AlwaysPresent:   1
T4A00 000:177.346         LoaderInfo:
T4A00 000:177.352           Name:            Flash_128K
T4A00 000:177.357           MaxSize:         0x00020000
T4A00 000:177.363           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32L18x_128KB.FLM
T4A00 000:177.370           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:177.380     Device entry created:  HC32F420xA
T4A00 000:177.383       ChipInfo:
T4A00 000:177.389         Vendor:          HDSC
T4A00 000:177.395         Name:            HC32F420xA
T4A00 000:177.400         WorkRAMAddr:     0x20000000
T4A00 000:177.406         WorkRAMSize:     0x00006000
T4A00 000:177.412         Core:            JLINK_CORE_CORTEX_M4
T4A00 000:177.417       FlashBankInfo:
T4A00 000:177.423         Name:            Flash_128K
T4A00 000:177.460         AlwaysPresent:   1
T4A00 000:177.466         LoaderInfo:
T4A00 000:177.472           Name:            Flash_128K
T4A00 000:177.477           MaxSize:         0x00020000
T4A00 000:177.483           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\Devices\HDSC\HC32F420_128KB.FLM
T4A00 000:177.489           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:177.851   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1HA0\Devices.xml
T4A00 000:179.014   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1HA0\Devices.xml evaluated successfully.
T4A00 000:179.032     Device entry created:  YTM32B1HA01
T4A00 000:179.037       ChipInfo:
T4A00 000:179.042         Vendor:          YTMicro
T4A00 000:179.049         Name:            YTM32B1HA01
T4A00 000:179.056         Core:            JLINK_CORE_CORTEX_M7
T4A00 000:179.094         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1HA0\YTM32B1HA0.JLinkScript
T4A00 000:179.101         WorkRAMAddr:     0x20020000
T4A00 000:179.106         WorkRAMSize:     0x00020000
T4A00 000:179.113       FlashBankInfo:
T4A00 000:179.119         Name:            Main Flash
T4A00 000:179.125         BaseAddr:        0x02000000
T4A00 000:179.171         AlwaysPresent:   1
T4A00 000:179.178         LoaderInfo:
T4A00 000:179.183           Name:            Main_Flash
T4A00 000:179.189           MaxSize:         0x00200000
T4A00 000:179.195           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1HA0\YTM32B1HA0_Main.FLM
T4A00 000:179.201           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:179.206       FlashBankInfo:
T4A00 000:179.212         Name:            Data Flash
T4A00 000:179.218         BaseAddr:        0x06000000
T4A00 000:179.258         AlwaysPresent:   1
T4A00 000:179.264         LoaderInfo:
T4A00 000:179.269           Name:            Data_Flash
T4A00 000:179.275           MaxSize:         0x00040000
T4A00 000:179.281           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1HA0\YTM32B1HA0_Dflash.FLM
T4A00 000:179.286           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:179.607   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LD0\Devices.xml
T4A00 000:180.629   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LD0\Devices.xml evaluated successfully.
T4A00 000:180.641     Device entry created:  YTM32B1LD04
T4A00 000:180.645       ChipInfo:
T4A00 000:180.652         Vendor:          YTMicro
T4A00 000:180.658         Name:            YTM32B1LD04
T4A00 000:180.665         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:180.702         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LD0\YTM32B1LD0.JLinkScript
T4A00 000:180.708         WorkRAMAddr:     0x20000000
T4A00 000:180.714         WorkRAMSize:     0x00002000
T4A00 000:180.721       FlashBankInfo:
T4A00 000:180.727         Name:            Internal Flash
T4A00 000:180.780         AlwaysPresent:   1
T4A00 000:180.786         LoaderInfo:
T4A00 000:180.792           Name:            Internal_Flash
T4A00 000:180.798           MaxSize:         0x00010000
T4A00 000:180.804           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LD0\YTM32B1LD0_Main.FLM
T4A00 000:180.813           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:180.819       FlashBankInfo:
T4A00 000:180.824         Name:            Internal Flash
T4A00 000:180.830         BaseAddr:        0x00400200
T4A00 000:180.871         AlwaysPresent:   1
T4A00 000:180.877         LoaderInfo:
T4A00 000:180.883           Name:            Internal_Flash
T4A00 000:180.888           MaxSize:         0x00000800
T4A00 000:180.894           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LD0\YTM32B1LD0_Dflash.FLM
T4A00 000:180.900           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:181.216   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE0\Devices.xml
T4A00 000:182.371   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE0\Devices.xml evaluated successfully.
T4A00 000:182.387     Device entry created:  YTM32B1LE05
T4A00 000:182.393       ChipInfo:
T4A00 000:182.400         Vendor:          YTMicro
T4A00 000:182.408         Name:            YTM32B1LE05
T4A00 000:182.416         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:182.452         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE0\YTM32B1LE05.JLinkScript
T4A00 000:182.459         WorkRAMAddr:     0x20000000
T4A00 000:182.465         WorkRAMSize:     0x00004000
T4A00 000:182.471       FlashBankInfo:
T4A00 000:182.477         Name:            Internal PFlash
T4A00 000:182.523         AlwaysPresent:   1
T4A00 000:182.531         LoaderInfo:
T4A00 000:182.539           Name:            Internal_PFlash
T4A00 000:182.546           MaxSize:         0x00020000
T4A00 000:182.554           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE0\YTM32B1LE0_Main.FLM
T4A00 000:182.559           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:182.565       FlashBankInfo:
T4A00 000:182.571         Name:            Internal DFlash
T4A00 000:182.576         BaseAddr:        0x10000000
T4A00 000:182.628         AlwaysPresent:   1
T4A00 000:182.635         LoaderInfo:
T4A00 000:182.640           Name:            Internal_DFlash
T4A00 000:182.646           MaxSize:         0x00000800
T4A00 000:182.652           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE0\YTM32B1LE0_Dflash.FLM
T4A00 000:182.658           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:182.666     Device entry created:  YTM32B1LE04
T4A00 000:182.670       ChipInfo:
T4A00 000:182.676         Vendor:          YTMicro
T4A00 000:182.682         Name:            YTM32B1LE04
T4A00 000:182.689         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:182.716         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE0\YTM32B1LE04.JLinkScript
T4A00 000:182.722         WorkRAMAddr:     0x20000000
T4A00 000:182.728         WorkRAMSize:     0x00002000
T4A00 000:182.733       FlashBankInfo:
T4A00 000:182.739         Name:            Internal PFlash
T4A00 000:182.776         AlwaysPresent:   1
T4A00 000:182.782         LoaderInfo:
T4A00 000:182.787           Name:            Internal_PFlash
T4A00 000:182.793           MaxSize:         0x00010000
T4A00 000:182.799           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE0\YTM32B1LE0_Main.FLM
T4A00 000:182.805           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:182.810       FlashBankInfo:
T4A00 000:182.816         Name:            Internal DFlash
T4A00 000:182.822         BaseAddr:        0x10000000
T4A00 000:182.857         AlwaysPresent:   1
T4A00 000:182.863         LoaderInfo:
T4A00 000:182.869           Name:            Internal_DFlash
T4A00 000:182.875           MaxSize:         0x00000800
T4A00 000:182.881           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE0\YTM32B1LE0_Dflash.FLM
T4A00 000:182.886           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:183.248   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE1\Devices.xml
T4A00 000:184.277   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE1\Devices.xml evaluated successfully.
T4A00 000:184.302     Device entry created:  YTM32B1LE15
T4A00 000:184.308       ChipInfo:
T4A00 000:184.315         Vendor:          YTMicro
T4A00 000:184.323         Name:            YTM32B1LE15
T4A00 000:184.331         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:184.370         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE1\YTM32B1LE15.JLinkScript
T4A00 000:184.378         WorkRAMAddr:     0x20000000
T4A00 000:184.385         WorkRAMSize:     0x00004000
T4A00 000:184.392       FlashBankInfo:
T4A00 000:184.399         Name:            Internal PFlash
T4A00 000:184.455         AlwaysPresent:   1
T4A00 000:184.463         LoaderInfo:
T4A00 000:184.471           Name:            Internal_PFlash
T4A00 000:184.477           MaxSize:         0x00020000
T4A00 000:184.485           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE1\YTM32B1LE1_Main.FLM
T4A00 000:184.492           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:184.499       FlashBankInfo:
T4A00 000:184.506         Name:            Internal DFlash
T4A00 000:184.514         BaseAddr:        0x10000000
T4A00 000:184.567         AlwaysPresent:   1
T4A00 000:184.576         LoaderInfo:
T4A00 000:184.583           Name:            Internal_DFlash
T4A00 000:184.590           MaxSize:         0x00003000
T4A00 000:184.597           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE1\YTM32B1LE1_Dflash.FLM
T4A00 000:184.604           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:184.615     Device entry created:  YTM32B1LE14
T4A00 000:184.620       ChipInfo:
T4A00 000:184.626         Vendor:          YTMicro
T4A00 000:184.633         Name:            YTM32B1LE14
T4A00 000:184.641         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:184.671         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE1\YTM32B1LE14.JLinkScript
T4A00 000:184.680         WorkRAMAddr:     0x20000000
T4A00 000:184.686         WorkRAMSize:     0x00002000
T4A00 000:184.693       FlashBankInfo:
T4A00 000:184.700         Name:            Internal PFlash
T4A00 000:184.752         AlwaysPresent:   1
T4A00 000:184.760         LoaderInfo:
T4A00 000:184.768           Name:            Internal_PFlash
T4A00 000:184.777           MaxSize:         0x00010000
T4A00 000:184.785           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE1\YTM32B1LE1_Main.FLM
T4A00 000:184.793           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:184.801       FlashBankInfo:
T4A00 000:184.809         Name:            Internal DFlash
T4A00 000:184.817         BaseAddr:        0x10000000
T4A00 000:184.869         AlwaysPresent:   1
T4A00 000:184.877         LoaderInfo:
T4A00 000:184.885           Name:            Internal_DFlash
T4A00 000:184.892           MaxSize:         0x00003000
T4A00 000:184.899           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1LE1\YTM32B1LE1_Dflash.FLM
T4A00 000:184.908           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:185.339   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1MC0\Devices.xml
T4A00 000:186.370   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1MC0\Devices.xml evaluated successfully.
T4A00 000:186.383     Device entry created:  YTM32B1MC03
T4A00 000:186.389       ChipInfo:
T4A00 000:186.396         Vendor:          YTMicro
T4A00 000:186.404         Name:            YTM32B1MC03
T4A00 000:186.413         Core:            JLINK_CORE_CORTEX_M33
T4A00 000:186.455         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1MC0\YTM32B1MC0.JLinkScript
T4A00 000:186.467         WorkRAMAddr:     0x20000000
T4A00 000:186.474         WorkRAMSize:     0x00008000
T4A00 000:186.481       FlashBankInfo:
T4A00 000:186.488         Name:            PFlash
T4A00 000:186.564         AlwaysPresent:   1
T4A00 000:186.572         LoaderInfo:
T4A00 000:186.578           Name:            PFlash
T4A00 000:186.584           MaxSize:         0x00040000
T4A00 000:186.590           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1MC0\YTM32B1MC0_Main.FLM
T4A00 000:186.596           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:186.602       FlashBankInfo:
T4A00 000:186.608         Name:            DFlash0
T4A00 000:186.614         BaseAddr:        0x10001000
T4A00 000:186.654         AlwaysPresent:   1
T4A00 000:186.660         LoaderInfo:
T4A00 000:186.666           Name:            DFlash0
T4A00 000:186.671           MaxSize:         0x00002800
T4A00 000:186.678           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1MC0\YTM32B1MC0_Dflash.FLM
T4A00 000:186.683           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:186.689       FlashBankInfo:
T4A00 000:186.695         Name:            DFlash1
T4A00 000:186.700         BaseAddr:        0x10011000
T4A00 000:186.738         AlwaysPresent:   1
T4A00 000:186.744         LoaderInfo:
T4A00 000:186.749           Name:            DFlash1
T4A00 000:186.755           MaxSize:         0x00002800
T4A00 000:186.761           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1MC0\YTM32B1MC0_Dflash.FLM
T4A00 000:186.767           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:187.041   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1MD1\Devices.xml
T4A00 000:188.024   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1MD1\Devices.xml evaluated successfully.
T4A00 000:188.035     Device entry created:  YTM32B1MD14
T4A00 000:188.039       ChipInfo:
T4A00 000:188.045         Vendor:          YTMicro
T4A00 000:188.051         Name:            YTM32B1MD14
T4A00 000:188.058         Core:            JLINK_CORE_CORTEX_M33
T4A00 000:188.089         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1MD1\YTM32B1MD1.JLinkScript
T4A00 000:188.096         WorkRAMAddr:     0x1FFF8000
T4A00 000:188.101         WorkRAMSize:     0x00010000
T4A00 000:188.107       FlashBankInfo:
T4A00 000:188.113         Name:            Internal Flash
T4A00 000:188.156         AlwaysPresent:   1
T4A00 000:188.162         LoaderInfo:
T4A00 000:188.168           Name:            Internal_Flash
T4A00 000:188.173           MaxSize:         0x00080000
T4A00 000:188.180           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1MD1\YTM32B1MD1_Main.FLM
T4A00 000:188.185           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:188.487   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME0\Devices.xml
T4A00 000:189.449   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME0\Devices.xml evaluated successfully.
T4A00 000:189.459     Device entry created:  YTM32B1ME05
T4A00 000:189.463       ChipInfo:
T4A00 000:189.469         Vendor:          YTMicro
T4A00 000:189.475         Name:            YTM32B1ME05
T4A00 000:189.482         Core:            JLINK_CORE_CORTEX_M33
T4A00 000:189.510         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME0\YTM32B1ME0.JLinkScript
T4A00 000:189.516         WorkRAMAddr:     0x1FFF0000
T4A00 000:189.522         WorkRAMSize:     0x00010000
T4A00 000:189.528       FlashBankInfo:
T4A00 000:189.534         Name:            Internal Flash
T4A00 000:189.575         AlwaysPresent:   1
T4A00 000:189.581         LoaderInfo:
T4A00 000:189.587           Name:            Internal_Flash
T4A00 000:189.592           MaxSize:         0x00100000
T4A00 000:189.601           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME0\YTM32B1ME0_Main.FLM
T4A00 000:189.607           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:189.612       FlashBankInfo:
T4A00 000:189.618         Name:            Internal Flash
T4A00 000:189.624         BaseAddr:        0x00100000
T4A00 000:189.663         AlwaysPresent:   1
T4A00 000:189.669         LoaderInfo:
T4A00 000:189.675           Name:            Internal_Flash
T4A00 000:189.680           MaxSize:         0x00040000
T4A00 000:189.686           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME0\YTM32B1ME0_Dflash.FLM
T4A00 000:189.692           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:189.947   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME1\Devices.xml
T4A00 000:190.914   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME1\Devices.xml evaluated successfully.
T4A00 000:190.925     Device entry created:  YTM32B1ME14
T4A00 000:190.929       ChipInfo:
T4A00 000:190.935         Vendor:          YTMicro
T4A00 000:190.941         Name:            YTM32B1ME14
T4A00 000:190.948         Core:            JLINK_CORE_CORTEX_M33
T4A00 000:190.976         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME1\YTM32B1ME1.JLinkScript
T4A00 000:190.982         WorkRAMAddr:     0x20000000
T4A00 000:190.988         WorkRAMSize:     0x00010000
T4A00 000:190.994       FlashBankInfo:
T4A00 000:190.999         Name:            PFlash
T4A00 000:191.042         AlwaysPresent:   1
T4A00 000:191.048         LoaderInfo:
T4A00 000:191.053           Name:            PFlash
T4A00 000:191.059           MaxSize:         0x00080000
T4A00 000:191.065           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME1\YTM32B1ME1_Main.FLM
T4A00 000:191.071           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:191.076       FlashBankInfo:
T4A00 000:191.082         Name:            NVR ROM
T4A00 000:191.087         BaseAddr:        0x10020000
T4A00 000:191.128         AlwaysPresent:   1
T4A00 000:191.134         LoaderInfo:
T4A00 000:191.140           Name:            NVR_ROM
T4A00 000:191.146           MaxSize:         0x00004000
T4A00 000:191.152           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME1\YTM32B1ME1_NVR_ROM.FLM
T4A00 000:191.157           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:191.166     Device entry created:  YTM32B1ME15
T4A00 000:191.170       ChipInfo:
T4A00 000:191.176         Vendor:          YTMicro
T4A00 000:191.181         Name:            YTM32B1ME15
T4A00 000:191.188         Core:            JLINK_CORE_CORTEX_M33
T4A00 000:191.209         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME1\YTM32B1ME1.JLinkScript
T4A00 000:191.215         WorkRAMAddr:     0x20000000
T4A00 000:191.221         WorkRAMSize:     0x00010000
T4A00 000:191.227       FlashBankInfo:
T4A00 000:191.232         Name:            PFlash
T4A00 000:191.269         AlwaysPresent:   1
T4A00 000:191.274         LoaderInfo:
T4A00 000:191.280           Name:            PFlash
T4A00 000:191.286           MaxSize:         0x00100000
T4A00 000:191.292           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME1\YTM32B1ME1_Main.FLM
T4A00 000:191.297           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:191.303       FlashBankInfo:
T4A00 000:191.308         Name:            NVR ROM
T4A00 000:191.314         BaseAddr:        0x10020000
T4A00 000:191.349         AlwaysPresent:   1
T4A00 000:191.355         LoaderInfo:
T4A00 000:191.361           Name:            NVR_ROM
T4A00 000:191.366           MaxSize:         0x00004000
T4A00 000:191.372           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32B1ME1\YTM32B1ME1_NVR_ROM.FLM
T4A00 000:191.380           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:191.674   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1LS0\Devices.xml
T4A00 000:192.628   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1LS0\Devices.xml evaluated successfully.
T4A00 000:192.638     Device entry created:  YTM32Z1LS06
T4A00 000:192.642       ChipInfo:
T4A00 000:192.647         Vendor:          YTMicro
T4A00 000:192.653         Name:            YTM32Z1LS06
T4A00 000:192.659         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:192.686         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1LS0\YTM32Z1LS0.JLinkScript
T4A00 000:192.693         WorkRAMAddr:     0x20000000
T4A00 000:192.698         WorkRAMSize:     0x00004000
T4A00 000:192.704       FlashBankInfo:
T4A00 000:192.710         Name:            Internal PFlash
T4A00 000:192.751         AlwaysPresent:   1
T4A00 000:192.758         LoaderInfo:
T4A00 000:192.763           Name:            Internal_PFlash
T4A00 000:192.769           MaxSize:         0x00020000
T4A00 000:192.775           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1LS0\YTM32Z1LS0_Main.FLM
T4A00 000:192.781           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:192.786       FlashBankInfo:
T4A00 000:192.792         Name:            Internal DFlash
T4A00 000:192.797         BaseAddr:        0x10000000
T4A00 000:192.836         AlwaysPresent:   1
T4A00 000:192.843         LoaderInfo:
T4A00 000:192.848           Name:            Internal_DFlash
T4A00 000:192.854           MaxSize:         0x00000800
T4A00 000:192.860           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1LS0\YTM32Z1LS0_Dflash.FLM
T4A00 000:192.865           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:193.109   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1MC0\Devices.xml
T4A00 000:194.058   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1MC0\Devices.xml evaluated successfully.
T4A00 000:194.068     Device entry created:  YTM32Z1MC03
T4A00 000:194.072       ChipInfo:
T4A00 000:194.078         Vendor:          YTMicro
T4A00 000:194.083         Name:            YTM32Z1MC03
T4A00 000:194.089         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:194.116         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1MC0\YTM32Z1MC0.JLinkScript
T4A00 000:194.122         WorkRAMAddr:     0x20000000
T4A00 000:194.128         WorkRAMSize:     0x00001000
T4A00 000:194.134       FlashBankInfo:
T4A00 000:194.140         Name:            Internal PFlash
T4A00 000:194.180         AlwaysPresent:   1
T4A00 000:194.186         LoaderInfo:
T4A00 000:194.192           Name:            Internal_PFlash
T4A00 000:194.197           MaxSize:         0x00008000
T4A00 000:194.203           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1MC0\YTM32Z1MC0_Main.FLM
T4A00 000:194.209           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:194.214       FlashBankInfo:
T4A00 000:194.220         Name:            Internal DFlash
T4A00 000:194.226         BaseAddr:        0x00100000
T4A00 000:194.265         AlwaysPresent:   1
T4A00 000:194.271         LoaderInfo:
T4A00 000:194.276           Name:            Internal_DFlash
T4A00 000:194.282           MaxSize:         0x000000C0
T4A00 000:194.288           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1MC0\YTM32Z1MC0_Dflash.FLM
T4A00 000:194.293           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:194.582   XML file found at: C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1MD0\Devices.xml
T4A00 000:195.542   C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1MD0\Devices.xml evaluated successfully.
T4A00 000:195.552     Device entry created:  YTM32Z1MD04
T4A00 000:195.557       ChipInfo:
T4A00 000:195.563         Vendor:          YTMicro
T4A00 000:195.569         Name:            YTM32Z1MD04
T4A00 000:195.575         Core:            JLINK_CORE_CORTEX_M0
T4A00 000:195.603         Script:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1MD0\YTM32Z1MD0.JLinkScript
T4A00 000:195.609         WorkRAMAddr:     0x20000000
T4A00 000:195.615         WorkRAMSize:     0x00001000
T4A00 000:195.620       FlashBankInfo:
T4A00 000:195.626         Name:            Main Flash
T4A00 000:195.667         AlwaysPresent:   1
T4A00 000:195.673         LoaderInfo:
T4A00 000:195.678           Name:            Main_Flash
T4A00 000:195.684           MaxSize:         0x0000C000
T4A00 000:195.690           Loader:          C:\Users\<USER>\AppData\Roaming\SEGGER\JLinkDevices\YTMicro\YTM32Z1MD0\YTM32Z1MD0_Main.FLM
T4A00 000:195.696           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:196.010   Ref file found at: d:\Keil_v5\ARM\Segger\JLinkDevices.ref
T4A00 000:196.114   XML referenced by ref file: d:\Program Files\SEGGER\JLink_V796a\JLinkDevices.xml
T4A00 000:197.382   d:\Program Files\SEGGER\JLink_V796a\JLinkDevices.xml evaluated successfully.
T4A00 000:197.392     Device entry modified: CYCLONE V
T4A00 000:197.405       ChipInfo:
T4A00 000:197.412         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Altera\Cyclone_V\Altera_Cyclone_V.JLinkScript
T4A00 000:197.419     Device entry modified: AMA3B1KK-KBR
T4A00 000:197.429       ChipInfo:
T4A00 000:197.436         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AmbiqMicro\AmbiqMicro_Apollo3.pex
T4A00 000:197.443     Device entry modified: AMA3B1KK-KCR
T4A00 000:197.451       ChipInfo:
T4A00 000:197.457         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AmbiqMicro\AmbiqMicro_Apollo3.pex
T4A00 000:197.465     Device entry modified: AMAPH1KK-KBR
T4A00 000:197.473       ChipInfo:
T4A00 000:197.479         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AmbiqMicro\AmbiqMicro_Apollo2.pex
T4A00 000:197.486     Device entry modified: AMAPH1KK-KCR
T4A00 000:197.494       ChipInfo:
T4A00 000:197.501         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AmbiqMicro\AmbiqMicro_Apollo2.pex
T4A00 000:197.509     Device entry modified: MSP3_AN524_M33
T4A00 000:197.518       ChipInfo:
T4A00 000:197.525         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ARM\SSE200-MPS3\ARM_SSE-200-MPS3_Core0.pex
T4A00 000:197.532     Device entry modified: MSP3_AN524_M33_0
T4A00 000:197.541       ChipInfo:
T4A00 000:197.547         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ARM\SSE200-MPS3\ARM_SSE-200-MPS3_Core0.pex
T4A00 000:197.554     Device entry modified: MSP3_AN524_M33_1
T4A00 000:197.562       ChipInfo:
T4A00 000:197.568         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ARM\SSE200-MPS3\ARM_SSE-200-MPS3_Core1.pex
T4A00 000:197.577     Device entry modified: ATSAMA5D21C
T4A00 000:197.585       ChipInfo:
T4A00 000:197.592         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:197.599     Device entry modified: ATSAMA5D22C
T4A00 000:197.607       ChipInfo:
T4A00 000:197.613         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:197.620     Device entry modified: ATSAMA5D23C
T4A00 000:197.629       ChipInfo:
T4A00 000:197.635         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:197.641     Device entry modified: ATSAMA5D24C
T4A00 000:197.649       ChipInfo:
T4A00 000:197.655         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:197.662     Device entry modified: ATSAMA5D25C
T4A00 000:197.670       ChipInfo:
T4A00 000:197.676         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:197.684     Device entry modified: ATSAMA5D26C
T4A00 000:197.693       ChipInfo:
T4A00 000:197.699         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:197.706     Device entry modified: ATSAMA5D27C
T4A00 000:197.714       ChipInfo:
T4A00 000:197.720         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:197.726       FlashBankInfo:
T4A00 000:197.732         Name:            QSPI Flash
T4A00 000:197.738         BaseAddr:        0xD0000000
T4A00 000:197.765     Device entry modified: ATSAMA5D28C
T4A00 000:197.773       ChipInfo:
T4A00 000:197.779         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_ATSAMA5D2x.pex
T4A00 000:197.787     Device entry modified: ATSAMB11G18A
T4A00 000:197.791       FlashBankInfo:
T4A00 000:197.797         Name:            QSPI Flash
T4A00 000:197.802         BaseAddr:        0x60000000
T4A00 000:197.820     Device entry modified: ATSAMD51G18A
T4A00 000:197.828       ChipInfo:
T4A00 000:197.834         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:197.841     Device entry modified: ATSAMD51G19A
T4A00 000:197.849       ChipInfo:
T4A00 000:197.855         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:197.862     Device entry modified: ATSAMD51J18A
T4A00 000:197.869       ChipInfo:
T4A00 000:197.875         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:197.883     Device entry modified: ATSAMD51J19A
T4A00 000:197.890       ChipInfo:
T4A00 000:197.896         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:197.904     Device entry modified: ATSAMD51J20A
T4A00 000:197.911       ChipInfo:
T4A00 000:197.917         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:197.926     Device entry modified: ATSAMD51N19A
T4A00 000:197.954       ChipInfo:
T4A00 000:197.961         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:197.969     Device entry modified: ATSAMD51N20A
T4A00 000:197.979       ChipInfo:
T4A00 000:197.987         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:197.996     Device entry modified: ATSAMD51P19A
T4A00 000:198.006       ChipInfo:
T4A00 000:198.018         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.027     Device entry modified: ATSAMD51P20A
T4A00 000:198.038       ChipInfo:
T4A00 000:198.046         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.056     Device entry modified: ATSAME51J18A
T4A00 000:198.066       ChipInfo:
T4A00 000:198.076         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.088     Device entry modified: ATSAME51J19A
T4A00 000:198.100       ChipInfo:
T4A00 000:198.110         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.121     Device entry modified: ATSAME51N19A
T4A00 000:198.132       ChipInfo:
T4A00 000:198.141         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.149     Device entry modified: ATSAME51N20A
T4A00 000:198.159       ChipInfo:
T4A00 000:198.167         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.174     Device entry modified: ATSAME53J18A
T4A00 000:198.182       ChipInfo:
T4A00 000:198.188         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.195     Device entry modified: ATSAME53J19A
T4A00 000:198.203       ChipInfo:
T4A00 000:198.209         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.217     Device entry modified: ATSAME53J20A
T4A00 000:198.225       ChipInfo:
T4A00 000:198.231         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.238     Device entry modified: ATSAME53N19A
T4A00 000:198.245       ChipInfo:
T4A00 000:198.251         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.258     Device entry modified: ATSAME53N20A
T4A00 000:198.265       ChipInfo:
T4A00 000:198.271         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.279     Device entry modified: ATSAME54N19A
T4A00 000:198.286       ChipInfo:
T4A00 000:198.292         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.299     Device entry modified: ATSAME54N20A
T4A00 000:198.306       ChipInfo:
T4A00 000:198.312         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.319     Device entry modified: ATSAME54P19A
T4A00 000:198.327       ChipInfo:
T4A00 000:198.333         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.339     Device entry modified: ATSAME54P20A
T4A00 000:198.347       ChipInfo:
T4A00 000:198.353         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAME5x\Atmel_SAME54.pex
T4A00 000:198.360     Device entry modified: ATSAME70J19A
T4A00 000:198.370       ChipInfo:
T4A00 000:198.377         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:198.385     Device entry modified: ATSAME70J20A
T4A00 000:198.393       ChipInfo:
T4A00 000:198.399         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:198.406     Device entry modified: ATSAME70J21A
T4A00 000:198.413       ChipInfo:
T4A00 000:198.419         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:198.426     Device entry modified: ATSAME70N19A
T4A00 000:198.433       ChipInfo:
T4A00 000:198.439         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:198.446     Device entry modified: ATSAME70N20A
T4A00 000:198.453       ChipInfo:
T4A00 000:198.459         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:198.466     Device entry modified: ATSAME70N21A
T4A00 000:198.474       ChipInfo:
T4A00 000:198.480         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:198.487     Device entry modified: ATSAME70Q19A
T4A00 000:198.494       ChipInfo:
T4A00 000:198.500         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:198.507     Device entry modified: ATSAME70Q20A
T4A00 000:198.514       ChipInfo:
T4A00 000:198.520         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:198.527     Device entry modified: ATSAME70Q21A
T4A00 000:198.534       ChipInfo:
T4A00 000:198.540         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\Atmel_SAME70.pex
T4A00 000:198.548     Device entry modified: BCM43907
T4A00 000:198.556       ChipInfo:
T4A00 000:198.562         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Broadcom\BCM43907.JLinkScript
T4A00 000:198.569     Device entry modified: CR600
T4A00 000:198.577       ChipInfo:
T4A00 000:198.583         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ClouderSemi\CR600\CR600.JLinkScript
T4A00 000:198.589       FlashBankInfo:
T4A00 000:198.595         Name:            QSPI flash
T4A00 000:198.600         BaseAddr:        0x20000000
T4A00 000:198.622     Device entry modified: CYW43907
T4A00 000:198.630       ChipInfo:
T4A00 000:198.636         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Broadcom\BCM43907.JLinkScript
T4A00 000:198.644     Device entry modified: S6J328CK
T4A00 000:198.653       ChipInfo:
T4A00 000:198.660         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Cypress_S6J328.pex
T4A00 000:198.667     Device entry modified: S6J328CL
T4A00 000:198.675       ChipInfo:
T4A00 000:198.680         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Cypress_S6J328.pex
T4A00 000:198.687     Device entry modified: S6J324CKSM
T4A00 000:198.696       ChipInfo:
T4A00 000:198.702         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.709     Device entry modified: S6J331EKC
T4A00 000:198.717       ChipInfo:
T4A00 000:198.723         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.730     Device entry modified: S6J331EJA
T4A00 000:198.738       ChipInfo:
T4A00 000:198.744         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.751     Device entry modified: S6J331EKE
T4A00 000:198.758       ChipInfo:
T4A00 000:198.764         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.771     Device entry modified: S6J332CJB
T4A00 000:198.779       ChipInfo:
T4A00 000:198.785         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.791     Device entry modified: S6J332CJT
T4A00 000:198.799       ChipInfo:
T4A00 000:198.805         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.812     Device entry modified: S6J332CKS
T4A00 000:198.820       ChipInfo:
T4A00 000:198.826         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.832     Device entry modified: S6J332EJB
T4A00 000:198.840       ChipInfo:
T4A00 000:198.846         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.853     Device entry modified: S6J334BJD
T4A00 000:198.861       ChipInfo:
T4A00 000:198.867         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.873     Device entry modified: S6J334CHB
T4A00 000:198.881       ChipInfo:
T4A00 000:198.887         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.894     Device entry modified: S6J334CJE
T4A00 000:198.902       ChipInfo:
T4A00 000:198.908         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.916     Device entry modified: S6J334CJT
T4A00 000:198.924       ChipInfo:
T4A00 000:198.930         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.937     Device entry modified: S6J334CKS
T4A00 000:198.944       ChipInfo:
T4A00 000:198.950         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.957     Device entry modified: S6J334DJE
T4A00 000:198.965       ChipInfo:
T4A00 000:198.971         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.978     Device entry modified: S6J334DJT
T4A00 000:198.985       ChipInfo:
T4A00 000:198.991         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:198.998     Device entry modified: S6J334EJA
T4A00 000:199.006       ChipInfo:
T4A00 000:199.012         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:199.018     Device entry modified: S6J334EJE
T4A00 000:199.026       ChipInfo:
T4A00 000:199.032         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:199.075     Device entry modified: S6J334EJT
T4A00 000:199.084       ChipInfo:
T4A00 000:199.090         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\Traveo_S6J3300\Cypress_S6J33xx.pex
T4A00 000:199.109     Device entry modified: CEC1702
T4A00 000:199.114       FlashBankInfo:
T4A00 000:199.120         Name:            SPI Flash
T4A00 000:199.125         BaseAddr:        0x60000000
T4A00 000:199.145     Device entry modified: nRF52832_xxAA
T4A00 000:199.154       ChipInfo:
T4A00 000:199.160         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NordicSemi\nRF52.pex
T4A00 000:199.167     Device entry modified: nRF52832_xxAB
T4A00 000:199.175       ChipInfo:
T4A00 000:199.181         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NordicSemi\nRF52.pex
T4A00 000:199.188     Device entry modified: nRF52840_xxAA
T4A00 000:199.196       ChipInfo:
T4A00 000:199.202         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NordicSemi\nRF52.pex
T4A00 000:199.209     Device entry modified: MCIMX6X1_A9
T4A00 000:199.217       ChipInfo:
T4A00 000:199.223         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6SX\iMX6SX_CortexA9.JLinkScript
T4A00 000:199.229       FlashBankInfo:
T4A00 000:199.234         Name:            QSPI Flash
T4A00 000:199.240         BaseAddr:        0x70000000
T4A00 000:199.258     Device entry modified: MCIMX6X1_M4
T4A00 000:199.266       ChipInfo:
T4A00 000:199.272         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6SX\iMX6SX_CortexM4.JLinkScript
T4A00 000:199.279     Device entry modified: MCIMX6X2_A9
T4A00 000:199.286       ChipInfo:
T4A00 000:199.292         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6SX\iMX6SX_CortexA9.JLinkScript
T4A00 000:199.298       FlashBankInfo:
T4A00 000:199.303         Name:            QSPI Flash
T4A00 000:199.309         BaseAddr:        0x70000000
T4A00 000:199.324     Device entry modified: MCIMX6X2_M4
T4A00 000:199.332       ChipInfo:
T4A00 000:199.338         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6SX\iMX6SX_CortexM4.JLinkScript
T4A00 000:199.345     Device entry modified: MCIMX6X3_A9
T4A00 000:199.353       ChipInfo:
T4A00 000:199.359         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6SX\iMX6SX_CortexA9.JLinkScript
T4A00 000:199.364       FlashBankInfo:
T4A00 000:199.370         Name:            QSPI Flash
T4A00 000:199.375         BaseAddr:        0x70000000
T4A00 000:199.390     Device entry modified: MCIMX6X3_M4
T4A00 000:199.398       ChipInfo:
T4A00 000:199.404         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6SX\iMX6SX_CortexM4.JLinkScript
T4A00 000:199.411     Device entry modified: MCIMX6X4_A9
T4A00 000:199.418       ChipInfo:
T4A00 000:199.424         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6SX\iMX6SX_CortexA9.JLinkScript
T4A00 000:199.430       FlashBankInfo:
T4A00 000:199.435         Name:            QSPI Flash
T4A00 000:199.441         BaseAddr:        0x70000000
T4A00 000:199.456     Device entry modified: MCIMX6X4_M4
T4A00 000:199.464       ChipInfo:
T4A00 000:199.470         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6SX\iMX6SX_CortexM4.JLinkScript
T4A00 000:199.477     Device entry modified: MCIMX6G0
T4A00 000:199.485       ChipInfo:
T4A00 000:199.491         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:199.497       FlashBankInfo:
T4A00 000:199.502         Name:            QSPI flash
T4A00 000:199.508         BaseAddr:        0x60000000
T4A00 000:199.524     Device entry modified: MCIMX6G1
T4A00 000:199.531       ChipInfo:
T4A00 000:199.538         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:199.543       FlashBankInfo:
T4A00 000:199.549         Name:            QSPI flash
T4A00 000:199.555         BaseAddr:        0x60000000
T4A00 000:199.570     Device entry modified: MCIMX6G2
T4A00 000:199.577       ChipInfo:
T4A00 000:199.583         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:199.589       FlashBankInfo:
T4A00 000:199.594         Name:            QSPI flash
T4A00 000:199.600         BaseAddr:        0x60000000
T4A00 000:199.616     Device entry modified: MCIMX6G3
T4A00 000:199.623       ChipInfo:
T4A00 000:199.629         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:199.635       FlashBankInfo:
T4A00 000:199.640         Name:            QSPI flash
T4A00 000:199.646         BaseAddr:        0x60000000
T4A00 000:199.660     Device entry modified: MCIMX6Y0
T4A00 000:199.668       ChipInfo:
T4A00 000:199.674         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:199.679       FlashBankInfo:
T4A00 000:199.685         Name:            QSPI flash
T4A00 000:199.690         BaseAddr:        0x60000000
T4A00 000:199.705     Device entry modified: MCIMX6Y1
T4A00 000:199.712       ChipInfo:
T4A00 000:199.718         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:199.724       FlashBankInfo:
T4A00 000:199.729         Name:            QSPI flash
T4A00 000:199.735         BaseAddr:        0x60000000
T4A00 000:199.749     Device entry modified: MCIMX6Y2
T4A00 000:199.756       ChipInfo:
T4A00 000:199.763         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:199.768       FlashBankInfo:
T4A00 000:199.773         Name:            QSPI flash
T4A00 000:199.779         BaseAddr:        0x60000000
T4A00 000:199.794     Device entry modified: MCIMX6Y7
T4A00 000:199.802       ChipInfo:
T4A00 000:199.808         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX6UL\NXP_iMX6ULL.JLinkScript
T4A00 000:199.813       FlashBankInfo:
T4A00 000:199.819         Name:            QSPI flash
T4A00 000:199.824         BaseAddr:        0x60000000
T4A00 000:199.839     Device entry modified: MCIMX7D3_A7_0
T4A00 000:199.847       ChipInfo:
T4A00 000:199.853         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_0.JLinkScript
T4A00 000:199.859       FlashBankInfo:
T4A00 000:199.864         Name:            QSPI flash
T4A00 000:199.870         BaseAddr:        0x60000000
T4A00 000:199.886     Device entry modified: MCIMX7D3_A7_1
T4A00 000:199.894       ChipInfo:
T4A00 000:199.900         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_1.JLinkScript
T4A00 000:199.906       FlashBankInfo:
T4A00 000:199.912         Name:            QSPI flash
T4A00 000:199.917         BaseAddr:        0x60000000
T4A00 000:199.932     Device entry modified: MCIMX7D3_M4
T4A00 000:199.940       ChipInfo:
T4A00 000:199.946         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexM4.JLinkScript
T4A00 000:199.952       FlashBankInfo:
T4A00 000:199.957         Name:            QSPI flash
T4A00 000:199.963         BaseAddr:        0x60000000
T4A00 000:199.978     Device entry modified: MCIMX7D5_A7_0
T4A00 000:199.986       ChipInfo:
T4A00 000:199.992         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_0.JLinkScript
T4A00 000:199.997       FlashBankInfo:
T4A00 000:200.003         Name:            QSPI flash
T4A00 000:200.009         BaseAddr:        0x60000000
T4A00 000:200.023     Device entry modified: MCIMX7D5_A7_1
T4A00 000:200.031       ChipInfo:
T4A00 000:200.037         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_1.JLinkScript
T4A00 000:200.043       FlashBankInfo:
T4A00 000:200.048         Name:            QSPI flash
T4A00 000:200.059         BaseAddr:        0x60000000
T4A00 000:200.074     Device entry modified: MCIMX7D5_M4
T4A00 000:200.082       ChipInfo:
T4A00 000:200.088         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexM4.JLinkScript
T4A00 000:200.093       FlashBankInfo:
T4A00 000:200.099         Name:            QSPI flash
T4A00 000:200.104         BaseAddr:        0x60000000
T4A00 000:200.119     Device entry modified: MCIMX7D7_A7_0
T4A00 000:200.127       ChipInfo:
T4A00 000:200.133         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_0.JLinkScript
T4A00 000:200.138       FlashBankInfo:
T4A00 000:200.144         Name:            QSPI flash
T4A00 000:200.149         BaseAddr:        0x60000000
T4A00 000:200.165     Device entry modified: MCIMX7D7_A7_1
T4A00 000:200.172       ChipInfo:
T4A00 000:200.178         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexA7_1.JLinkScript
T4A00 000:200.184       FlashBankInfo:
T4A00 000:200.189         Name:            QSPI flash
T4A00 000:200.195         BaseAddr:        0x60000000
T4A00 000:200.209     Device entry modified: MCIMX7D7_M4
T4A00 000:200.217       ChipInfo:
T4A00 000:200.223         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7D\NXP_iMX7D_Connect_CortexM4.JLinkScript
T4A00 000:200.229       FlashBankInfo:
T4A00 000:200.235         Name:            QSPI flash
T4A00 000:200.240         BaseAddr:        0x60000000
T4A00 000:200.255     Device entry modified: MCIMX7U3_M4
T4A00 000:200.263       ChipInfo:
T4A00 000:200.269         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7ULP\NXP_iMX7ULP_CortexM4.JLinkScript
T4A00 000:200.275       FlashBankInfo:
T4A00 000:200.280         Name:            QSPI Flash
T4A00 000:200.286         BaseAddr:        0x04000000
T4A00 000:200.302     Device entry modified: MCIMX7U3_A7
T4A00 000:200.310       ChipInfo:
T4A00 000:200.316         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7ULP\NXP_iMX7ULP_CortexA7.JLinkScript
T4A00 000:200.322       FlashBankInfo:
T4A00 000:200.327         Name:            QSPI Flash
T4A00 000:200.333         BaseAddr:        0xC0000000
T4A00 000:200.348     Device entry modified: MCIMX7U5_M4
T4A00 000:200.356       ChipInfo:
T4A00 000:200.362         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7ULP\NXP_iMX7ULP_CortexM4.JLinkScript
T4A00 000:200.368       FlashBankInfo:
T4A00 000:200.373         Name:            QSPI Flash
T4A00 000:200.379         BaseAddr:        0x04000000
T4A00 000:200.393     Device entry modified: MCIMX7U5_A7
T4A00 000:200.401       ChipInfo:
T4A00 000:200.407         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\iMX7ULP\NXP_iMX7ULP_CortexA7.JLinkScript
T4A00 000:200.413       FlashBankInfo:
T4A00 000:200.418         Name:            QSPI Flash
T4A00 000:200.424         BaseAddr:        0xC0000000
T4A00 000:200.439     Device entry modified: MIMXRT1021xxx4A
T4A00 000:200.444       FlashBankInfo:
T4A00 000:200.450         Name:            QSPI Flash
T4A00 000:200.455         BaseAddr:        0x60000000
T4A00 000:200.471     Device entry modified: MIMXRT1021xxx4A
T4A00 000:200.476       FlashBankInfo:
T4A00 000:200.482         Name:            QSPI Flash
T4A00 000:200.487         BaseAddr:        0x60000000
T4A00 000:200.501     Device entry modified: MIMXRT1021xxx4A
T4A00 000:200.506       FlashBankInfo:
T4A00 000:200.512         Name:            QSPI Flash
T4A00 000:200.517         BaseAddr:        0x60000000
T4A00 000:200.531     Device entry modified: MIMXRT1021xxx5A
T4A00 000:200.536       FlashBankInfo:
T4A00 000:200.542         Name:            QSPI Flash
T4A00 000:200.547         BaseAddr:        0x60000000
T4A00 000:200.561     Device entry modified: MIMXRT1021xxx5A
T4A00 000:200.566       FlashBankInfo:
T4A00 000:200.572         Name:            QSPI Flash
T4A00 000:200.578         BaseAddr:        0x60000000
T4A00 000:200.593     Device entry modified: MIMXRT1021xxx5A
T4A00 000:200.598       FlashBankInfo:
T4A00 000:200.603         Name:            QSPI Flash
T4A00 000:200.609         BaseAddr:        0x60000000
T4A00 000:200.624     Device entry modified: MIMXRT1051xxxxA
T4A00 000:200.629       FlashBankInfo:
T4A00 000:200.634         Name:            HyperFlash
T4A00 000:200.640         BaseAddr:        0x60000000
T4A00 000:200.656     Device entry modified: MIMXRT1051xxxxA
T4A00 000:200.661       FlashBankInfo:
T4A00 000:200.666         Name:            HyperFlash
T4A00 000:200.672         BaseAddr:        0x60000000
T4A00 000:200.688     Device entry modified: MIMXRT1051xxx5A
T4A00 000:200.693       FlashBankInfo:
T4A00 000:200.698         Name:            HyperFlash
T4A00 000:200.704         BaseAddr:        0x60000000
T4A00 000:200.718     Device entry modified: MIMXRT1051CVL5A
T4A00 000:200.724       FlashBankInfo:
T4A00 000:200.729         Name:            HyperFlash
T4A00 000:200.735         BaseAddr:        0x60000000
T4A00 000:200.749     Device entry modified: MIMXRT1051xxx6A
T4A00 000:200.754       FlashBankInfo:
T4A00 000:200.759         Name:            HyperFlash
T4A00 000:200.765         BaseAddr:        0x60000000
T4A00 000:200.780     Device entry modified: MIMXRT1051DVL6A
T4A00 000:200.784       FlashBankInfo:
T4A00 000:200.790         Name:            HyperFlash
T4A00 000:200.796         BaseAddr:        0x60000000
T4A00 000:200.810     Device entry modified: MIMXRT1051xxxxB
T4A00 000:200.815       FlashBankInfo:
T4A00 000:200.821         Name:            HyperFlash
T4A00 000:200.826         BaseAddr:        0x60000000
T4A00 000:200.841     Device entry modified: MIMXRT1051xxx5B
T4A00 000:200.846       FlashBankInfo:
T4A00 000:200.851         Name:            HyperFlash
T4A00 000:200.857         BaseAddr:        0x60000000
T4A00 000:200.871     Device entry modified: MIMXRT1051CVL5B
T4A00 000:200.876       FlashBankInfo:
T4A00 000:200.881         Name:            HyperFlash
T4A00 000:200.887         BaseAddr:        0x60000000
T4A00 000:200.901     Device entry modified: MIMXRT1051xxx6B
T4A00 000:200.906       FlashBankInfo:
T4A00 000:200.911         Name:            HyperFlash
T4A00 000:200.917         BaseAddr:        0x60000000
T4A00 000:200.931     Device entry modified: MIMXRT1051DVL6B
T4A00 000:200.936       FlashBankInfo:
T4A00 000:200.941         Name:            HyperFlash
T4A00 000:200.947         BaseAddr:        0x60000000
T4A00 000:200.962     Device entry modified: MIMXRT1052xxxxA
T4A00 000:200.967       FlashBankInfo:
T4A00 000:200.972         Name:            HyperFlash
T4A00 000:200.978         BaseAddr:        0x60000000
T4A00 000:200.992     Device entry modified: MIMXRT1052xxxxA
T4A00 000:200.997       FlashBankInfo:
T4A00 000:201.003         Name:            HyperFlash
T4A00 000:201.008         BaseAddr:        0x60000000
T4A00 000:201.023     Device entry modified: MIMXRT1052xxx5A
T4A00 000:201.028       FlashBankInfo:
T4A00 000:201.033         Name:            HyperFlash
T4A00 000:201.039         BaseAddr:        0x60000000
T4A00 000:201.053     Device entry modified: MIMXRT1052CVL5A
T4A00 000:201.058       FlashBankInfo:
T4A00 000:201.063         Name:            HyperFlash
T4A00 000:201.069         BaseAddr:        0x60000000
T4A00 000:201.083     Device entry modified: MIMXRT1052xxx6A
T4A00 000:201.088       FlashBankInfo:
T4A00 000:201.094         Name:            HyperFlash
T4A00 000:201.099         BaseAddr:        0x60000000
T4A00 000:201.113     Device entry modified: MIMXRT1052DVL6A
T4A00 000:201.118       FlashBankInfo:
T4A00 000:201.124         Name:            HyperFlash
T4A00 000:201.129         BaseAddr:        0x60000000
T4A00 000:201.144     Device entry modified: MIMXRT1052xxxxB
T4A00 000:201.149       FlashBankInfo:
T4A00 000:201.154         Name:            HyperFlash
T4A00 000:201.160         BaseAddr:        0x60000000
T4A00 000:201.174     Device entry modified: MIMXRT1052xxx5B
T4A00 000:201.179       FlashBankInfo:
T4A00 000:201.185         Name:            HyperFlash
T4A00 000:201.191         BaseAddr:        0x60000000
T4A00 000:201.205     Device entry modified: MIMXRT1052CVL5B
T4A00 000:201.210       FlashBankInfo:
T4A00 000:201.215         Name:            HyperFlash
T4A00 000:201.221         BaseAddr:        0x60000000
T4A00 000:201.235     Device entry modified: MIMXRT1052xxx6B
T4A00 000:201.240       FlashBankInfo:
T4A00 000:201.246         Name:            HyperFlash
T4A00 000:201.251         BaseAddr:        0x60000000
T4A00 000:201.265     Device entry modified: MIMXRT1052DVL6B
T4A00 000:201.270       FlashBankInfo:
T4A00 000:201.276         Name:            HyperFlash
T4A00 000:201.281         BaseAddr:        0x60000000
T4A00 000:201.296     Device entry modified: MIMXRT1061xxx5A
T4A00 000:201.301       FlashBankInfo:
T4A00 000:201.307         Name:            HyperFlash
T4A00 000:201.312         BaseAddr:        0x60000000
T4A00 000:201.328     Device entry modified: MIMXRT1061CVL5A
T4A00 000:201.333       FlashBankInfo:
T4A00 000:201.338         Name:            HyperFlash
T4A00 000:201.344         BaseAddr:        0x60000000
T4A00 000:201.357     Device entry modified: MIMXRT1061CVJ5A
T4A00 000:201.363       FlashBankInfo:
T4A00 000:201.368         Name:            HyperFlash
T4A00 000:201.374         BaseAddr:        0x60000000
T4A00 000:201.391     Device entry modified: MIMXRT1061xxx6A
T4A00 000:201.396       FlashBankInfo:
T4A00 000:201.402         Name:            HyperFlash
T4A00 000:201.407         BaseAddr:        0x60000000
T4A00 000:201.421     Device entry modified: MIMXRT1061DVL6A
T4A00 000:201.426       FlashBankInfo:
T4A00 000:201.432         Name:            HyperFlash
T4A00 000:201.437         BaseAddr:        0x60000000
T4A00 000:201.452     Device entry modified: MIMXRT1062xxx5A
T4A00 000:201.457       FlashBankInfo:
T4A00 000:201.462         Name:            HyperFlash
T4A00 000:201.468         BaseAddr:        0x60000000
T4A00 000:201.482     Device entry modified: MIMXRT1062CVL5A
T4A00 000:201.487       FlashBankInfo:
T4A00 000:201.492         Name:            HyperFlash
T4A00 000:201.498         BaseAddr:        0x60000000
T4A00 000:201.512     Device entry modified: MIMXRT1062CVJ5A
T4A00 000:201.517       FlashBankInfo:
T4A00 000:201.522         Name:            HyperFlash
T4A00 000:201.528         BaseAddr:        0x60000000
T4A00 000:201.542     Device entry modified: MIMXRT1062xxx6A
T4A00 000:201.547       FlashBankInfo:
T4A00 000:201.553         Name:            HyperFlash
T4A00 000:201.558         BaseAddr:        0x60000000
T4A00 000:201.572     Device entry modified: MIMXRT1062DVL6A
T4A00 000:201.577       FlashBankInfo:
T4A00 000:201.583         Name:            HyperFlash
T4A00 000:201.588         BaseAddr:        0x60000000
T4A00 000:201.603     Device entry modified: LPC51U68
T4A00 000:201.611       ChipInfo:
T4A00 000:201.617         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC51U68\NXP_LPC51U68.pex
T4A00 000:201.623     Device entry modified: LPC54005
T4A00 000:201.631       ChipInfo:
T4A00 000:201.637         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:201.644     Device entry modified: LPC54016
T4A00 000:201.651       ChipInfo:
T4A00 000:201.657         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:201.664     Device entry modified: LPC54018
T4A00 000:201.672       ChipInfo:
T4A00 000:201.678         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:201.685     Device entry modified: LPC54S005
T4A00 000:201.693       ChipInfo:
T4A00 000:201.699         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:201.705     Device entry modified: LPC54S016
T4A00 000:201.713       ChipInfo:
T4A00 000:201.719         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:201.726     Device entry modified: LPC54S018
T4A00 000:201.735       ChipInfo:
T4A00 000:201.741         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC540xx\NXP_LPC540xx.pex
T4A00 000:201.747     Device entry modified: LPC54101J256_M0
T4A00 000:201.755       ChipInfo:
T4A00 000:201.761         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:201.767     Device entry modified: LPC54101J512_M0
T4A00 000:201.775       ChipInfo:
T4A00 000:201.781         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:201.787     Device entry modified: LPC54102J256_M0
T4A00 000:201.795       ChipInfo:
T4A00 000:201.801         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:201.808     Device entry modified: LPC54102J512_M0
T4A00 000:201.815       ChipInfo:
T4A00 000:201.821         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:201.828     Device entry modified: LPC54111J128_M0
T4A00 000:201.835       ChipInfo:
T4A00 000:201.841         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:201.848     Device entry modified: LPC54111J256_M0
T4A00 000:201.856       ChipInfo:
T4A00 000:201.862         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:201.869     Device entry modified: LPC54112J256_M0
T4A00 000:201.876       ChipInfo:
T4A00 000:201.882         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:201.888     Device entry modified: LPC54113J128_M0
T4A00 000:201.896       ChipInfo:
T4A00 000:201.902         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:201.908     Device entry modified: LPC54113J256_M0
T4A00 000:201.916       ChipInfo:
T4A00 000:201.922         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:201.928     Device entry modified: LPC54114J256_M0
T4A00 000:201.935       ChipInfo:
T4A00 000:201.941         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5411x\LPC5411x_M0.JLinkScript
T4A00 000:201.948     Device entry modified: LPC54608J512
T4A00 000:201.956       ChipInfo:
T4A00 000:201.962         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5460x\NXP_LPC5460x.pex
T4A00 000:201.969     Device entry modified: LPC54608J512 (allow ECRP)
T4A00 000:201.977       ChipInfo:
T4A00 000:201.983         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\LPC5460x\NXP_LPC5460x.pex
T4A00 000:201.990     Device entry modified: S32V232_A53_A0
T4A00 000:201.998       ChipInfo:
T4A00 000:202.004         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\S32V\NXP_S32V234_A53_0.pex
T4A00 000:202.014     Device entry modified: S32V232_A53_B0
T4A00 000:202.022       ChipInfo:
T4A00 000:202.028         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\S32V\NXP_S32V234_A53_2.pex
T4A00 000:202.034     Device entry modified: S32V234_A53_A0
T4A00 000:202.042       ChipInfo:
T4A00 000:202.048         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\S32V\NXP_S32V234_A53_0.pex
T4A00 000:202.055     Device entry modified: S32V234_A53_A1
T4A00 000:202.062       ChipInfo:
T4A00 000:202.068         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\S32V\NXP_S32V234_A53_1.pex
T4A00 000:202.075     Device entry modified: S32V234_A53_B0
T4A00 000:202.083       ChipInfo:
T4A00 000:202.089         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\S32V\NXP_S32V234_A53_2.pex
T4A00 000:202.096     Device entry modified: S32V234_A53_B1
T4A00 000:202.103       ChipInfo:
T4A00 000:202.109         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\NXP\S32V\NXP_S32V234_A53_3.pex
T4A00 000:202.117     Device entry modified: R9A06G032
T4A00 000:202.125       ChipInfo:
T4A00 000:202.131         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-A7_CPU0.pex
T4A00 000:202.138     Device entry modified: R9A06G032_A7CPU0
T4A00 000:202.146       ChipInfo:
T4A00 000:202.152         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-A7_CPU0.pex
T4A00 000:202.159     Device entry modified: R9A06G032_A7CPU1
T4A00 000:202.166       ChipInfo:
T4A00 000:202.172         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-A7_CPU1.pex
T4A00 000:202.179     Device entry modified: R9A06G032_M3
T4A00 000:202.187       ChipInfo:
T4A00 000:202.193         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-M3.pex
T4A00 000:202.200     Device entry modified: R9A06G033
T4A00 000:202.207       ChipInfo:
T4A00 000:202.213         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-A7_CPU0.pex
T4A00 000:202.220     Device entry modified: R9A06G033_A7
T4A00 000:202.228       ChipInfo:
T4A00 000:202.234         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-A7_CPU0.pex
T4A00 000:202.240     Device entry modified: R9A06G033_M3
T4A00 000:202.248       ChipInfo:
T4A00 000:202.254         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-M3.pex
T4A00 000:202.260     Device entry modified: R9A06G034
T4A00 000:202.268       ChipInfo:
T4A00 000:202.273         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Renesas\RZN1\Renesas_RZN1_Cortex-M3.pex
T4A00 000:202.281     Device entry modified: R7S921040VCBG
T4A00 000:202.285       FlashBankInfo:
T4A00 000:202.291         Name:            HyperFlash
T4A00 000:202.296         BaseAddr:        0x30000000
T4A00 000:202.314     Device entry modified: R7S921041VCBG
T4A00 000:202.319       FlashBankInfo:
T4A00 000:202.324         Name:            HyperFlash
T4A00 000:202.330         BaseAddr:        0x30000000
T4A00 000:202.345     Device entry modified: R7S921042VCBG
T4A00 000:202.350       FlashBankInfo:
T4A00 000:202.355         Name:            HyperFlash
T4A00 000:202.361         BaseAddr:        0x30000000
T4A00 000:202.376     Device entry modified: R7S921043VCBG
T4A00 000:202.380       FlashBankInfo:
T4A00 000:202.386         Name:            HyperFlash
T4A00 000:202.391         BaseAddr:        0x30000000
T4A00 000:202.405     Device entry modified: R7S921051VCBG
T4A00 000:202.410       FlashBankInfo:
T4A00 000:202.415         Name:            HyperFlash
T4A00 000:202.421         BaseAddr:        0x30000000
T4A00 000:202.436     Device entry modified: R7S921052VCBG
T4A00 000:202.441       FlashBankInfo:
T4A00 000:202.446         Name:            HyperFlash
T4A00 000:202.452         BaseAddr:        0x30000000
T4A00 000:202.466     Device entry modified: R7S921053VCBG
T4A00 000:202.471       FlashBankInfo:
T4A00 000:202.477         Name:            HyperFlash
T4A00 000:202.482         BaseAddr:        0x30000000
T4A00 000:202.497     Device entry modified: CC3200
T4A00 000:202.506       ChipInfo:
T4A00 000:202.511         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\TI\TI_CC3200.JLinkScript
T4A00 000:202.518     Device entry modified: CC3220R
T4A00 000:202.526       ChipInfo:
T4A00 000:202.532         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\TI\TI_CC322x.pex
T4A00 000:202.539     Device entry modified: CC3220S
T4A00 000:202.547       ChipInfo:
T4A00 000:202.553         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\TI\TI_CC322x.pex
T4A00 000:202.559     Device entry modified: CC3220SF
T4A00 000:202.567       ChipInfo:
T4A00 000:202.573         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\TI\TI_CC322x.pex
T4A00 000:202.581     Device entry modified: DM3730
T4A00 000:202.588       ChipInfo:
T4A00 000:202.594         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\TI\DM3730\TI_DM3730.pex
T4A00 000:202.601     Device entry modified: TLE9842
T4A00 000:202.606       FlashBankInfo:
T4A00 000:202.611         Name:            NACNAD
T4A00 000:202.617         BaseAddr:        0x10FFFFFC
T4A00 000:202.632         AlwaysPresent:   1
T4A00 000:202.638       FlashBankInfo:
T4A00 000:202.643         Name:            Code Flash
T4A00 000:202.649         BaseAddr:        0x11000000
T4A00 000:202.664         AlwaysPresent:   1
T4A00 000:202.670       FlashBankInfo:
T4A00 000:202.675         Name:            Data Flash
T4A00 000:202.681         BaseAddr:        0x11008000
T4A00 000:202.695         AlwaysPresent:   1
T4A00 000:202.702     Device entry modified: TLE9842-2
T4A00 000:202.706       FlashBankInfo:
T4A00 000:202.712         Name:            NACNAD
T4A00 000:202.717         BaseAddr:        0x10FFFFFC
T4A00 000:202.730         AlwaysPresent:   1
T4A00 000:202.736       FlashBankInfo:
T4A00 000:202.742         Name:            Code Flash
T4A00 000:202.747         BaseAddr:        0x11000000
T4A00 000:202.761         AlwaysPresent:   1
T4A00 000:202.767       FlashBankInfo:
T4A00 000:202.772         Name:            Data Flash
T4A00 000:202.778         BaseAddr:        0x11009000
T4A00 000:202.791         AlwaysPresent:   1
T4A00 000:202.798     Device entry modified: TLE9843
T4A00 000:202.803       FlashBankInfo:
T4A00 000:202.808         Name:            NACNAD
T4A00 000:202.814         BaseAddr:        0x10FFFFFC
T4A00 000:202.827         AlwaysPresent:   1
T4A00 000:202.833       FlashBankInfo:
T4A00 000:202.838         Name:            Code Flash
T4A00 000:202.844         BaseAddr:        0x11000000
T4A00 000:202.857         AlwaysPresent:   1
T4A00 000:202.863       FlashBankInfo:
T4A00 000:202.868         Name:            Data Flash
T4A00 000:202.874         BaseAddr:        0x1100B000
T4A00 000:202.888         AlwaysPresent:   1
T4A00 000:202.895     Device entry modified: TLE9843-2
T4A00 000:202.899       FlashBankInfo:
T4A00 000:202.905         Name:            NACNAD
T4A00 000:202.910         BaseAddr:        0x10FFFFFC
T4A00 000:202.924         AlwaysPresent:   1
T4A00 000:202.930       FlashBankInfo:
T4A00 000:202.936         Name:            Code Flash
T4A00 000:202.941         BaseAddr:        0x11000000
T4A00 000:202.955         AlwaysPresent:   1
T4A00 000:202.961       FlashBankInfo:
T4A00 000:202.966         Name:            Data Flash
T4A00 000:202.972         BaseAddr:        0x1100C000
T4A00 000:202.985         AlwaysPresent:   1
T4A00 000:202.992     Device entry modified: TLE9844
T4A00 000:202.997       FlashBankInfo:
T4A00 000:203.002         Name:            NACNAD
T4A00 000:203.008         BaseAddr:        0x10FFFFFC
T4A00 000:203.021         AlwaysPresent:   1
T4A00 000:203.027       FlashBankInfo:
T4A00 000:203.032         Name:            Code Flash
T4A00 000:203.038         BaseAddr:        0x11000000
T4A00 000:203.051         AlwaysPresent:   1
T4A00 000:203.057       FlashBankInfo:
T4A00 000:203.062         Name:            Data Flash
T4A00 000:203.068         BaseAddr:        0x1100F000
T4A00 000:203.081         AlwaysPresent:   1
T4A00 000:203.088     Device entry modified: TLE9844
T4A00 000:203.092       FlashBankInfo:
T4A00 000:203.097         Name:            NACNAD
T4A00 000:203.103         BaseAddr:        0x10FFFFFC
T4A00 000:203.117         AlwaysPresent:   1
T4A00 000:203.122       FlashBankInfo:
T4A00 000:203.128         Name:            Code Flash
T4A00 000:203.133         BaseAddr:        0x11000000
T4A00 000:203.147         AlwaysPresent:   1
T4A00 000:203.153       FlashBankInfo:
T4A00 000:203.159         Name:            Data Flash
T4A00 000:203.164         BaseAddr:        0x1100F000
T4A00 000:203.178         AlwaysPresent:   1
T4A00 000:203.185     Device entry modified: TLE9845
T4A00 000:203.189       FlashBankInfo:
T4A00 000:203.195         Name:            NACNAD
T4A00 000:203.201         BaseAddr:        0x10FFFFFC
T4A00 000:203.214         AlwaysPresent:   1
T4A00 000:203.220       FlashBankInfo:
T4A00 000:203.225         Name:            Code Flash
T4A00 000:203.237         BaseAddr:        0x11000000
T4A00 000:203.251         AlwaysPresent:   1
T4A00 000:203.257       FlashBankInfo:
T4A00 000:203.262         Name:            Data Flash
T4A00 000:203.268         BaseAddr:        0x1100B000
T4A00 000:203.282         AlwaysPresent:   1
T4A00 000:203.288     Device entry modified: TLE9871
T4A00 000:203.293       FlashBankInfo:
T4A00 000:203.299         Name:            Code Flash
T4A00 000:203.304         BaseAddr:        0x11000000
T4A00 000:203.319         AlwaysPresent:   1
T4A00 000:203.325       FlashBankInfo:
T4A00 000:203.330         Name:            Data Flash
T4A00 000:203.336         BaseAddr:        0x11008000
T4A00 000:203.349         AlwaysPresent:   1
T4A00 000:203.356     Device entry modified: TLE9873
T4A00 000:203.361       FlashBankInfo:
T4A00 000:203.366         Name:            Code Flash
T4A00 000:203.372         BaseAddr:        0x11000000
T4A00 000:203.385         AlwaysPresent:   1
T4A00 000:203.391       FlashBankInfo:
T4A00 000:203.397         Name:            Data Flash
T4A00 000:203.402         BaseAddr:        0x1100B000
T4A00 000:203.416         AlwaysPresent:   1
T4A00 000:203.422     Device entry modified: TLE9873
T4A00 000:203.426       FlashBankInfo:
T4A00 000:203.432         Name:            Code Flash
T4A00 000:203.438         BaseAddr:        0x11000000
T4A00 000:203.451         AlwaysPresent:   1
T4A00 000:203.456       FlashBankInfo:
T4A00 000:203.462         Name:            Data Flash
T4A00 000:203.467         BaseAddr:        0x1100B000
T4A00 000:203.480         AlwaysPresent:   1
T4A00 000:203.487     Device entry modified: TLE9877
T4A00 000:203.492       FlashBankInfo:
T4A00 000:203.497         Name:            Code Flash
T4A00 000:203.503         BaseAddr:        0x11000000
T4A00 000:203.516         AlwaysPresent:   1
T4A00 000:203.522       FlashBankInfo:
T4A00 000:203.527         Name:            Data Flash
T4A00 000:203.533         BaseAddr:        0x1100F000
T4A00 000:203.546         AlwaysPresent:   1
T4A00 000:203.553     Device entry modified: TLE9877
T4A00 000:203.557       FlashBankInfo:
T4A00 000:203.562         Name:            Code Flash
T4A00 000:203.568         BaseAddr:        0x11000000
T4A00 000:203.581         AlwaysPresent:   1
T4A00 000:203.586       FlashBankInfo:
T4A00 000:203.592         Name:            Data Flash
T4A00 000:203.597         BaseAddr:        0x1100F000
T4A00 000:203.610         AlwaysPresent:   1
T4A00 000:203.617     Device entry modified: TLE9877
T4A00 000:203.621       FlashBankInfo:
T4A00 000:203.627         Name:            Code Flash
T4A00 000:203.632         BaseAddr:        0x11000000
T4A00 000:203.646         AlwaysPresent:   1
T4A00 000:203.651       FlashBankInfo:
T4A00 000:203.657         Name:            Data Flash
T4A00 000:203.662         BaseAddr:        0x1100F000
T4A00 000:203.675         AlwaysPresent:   1
T4A00 000:203.682     Device entry modified: TLE9877
T4A00 000:203.686       FlashBankInfo:
T4A00 000:203.691         Name:            Code Flash
T4A00 000:203.697         BaseAddr:        0x11000000
T4A00 000:203.710         AlwaysPresent:   1
T4A00 000:203.716       FlashBankInfo:
T4A00 000:203.721         Name:            Data Flash
T4A00 000:203.727         BaseAddr:        0x1100F000
T4A00 000:203.742         AlwaysPresent:   1
T4A00 000:203.749     Device entry modified: TLE9879
T4A00 000:203.753       FlashBankInfo:
T4A00 000:203.759         Name:            Code Flash
T4A00 000:203.764         BaseAddr:        0x11000000
T4A00 000:203.778         AlwaysPresent:   1
T4A00 000:203.784       FlashBankInfo:
T4A00 000:203.789         Name:            Data Flash
T4A00 000:203.795         BaseAddr:        0x1101F000
T4A00 000:203.809         AlwaysPresent:   1
T4A00 000:203.816     Device entry modified: TLE9879
T4A00 000:203.820       FlashBankInfo:
T4A00 000:203.825         Name:            Code Flash
T4A00 000:203.831         BaseAddr:        0x11000000
T4A00 000:203.844         AlwaysPresent:   1
T4A00 000:203.849       FlashBankInfo:
T4A00 000:203.855         Name:            Data Flash
T4A00 000:203.861         BaseAddr:        0x1101F000
T4A00 000:203.874         AlwaysPresent:   1
T4A00 000:203.880     Device entry modified: TLE9879
T4A00 000:203.884       FlashBankInfo:
T4A00 000:203.890         Name:            Code Flash
T4A00 000:203.895         BaseAddr:        0x11000000
T4A00 000:203.908         AlwaysPresent:   1
T4A00 000:203.914       FlashBankInfo:
T4A00 000:203.920         Name:            Data Flash
T4A00 000:203.925         BaseAddr:        0x1101F000
T4A00 000:204.037         AlwaysPresent:   1
T4A00 000:204.048     Device entry modified: TLE9879
T4A00 000:204.052       FlashBankInfo:
T4A00 000:204.058         Name:            Code Flash
T4A00 000:204.064         BaseAddr:        0x11000000
T4A00 000:204.085         AlwaysPresent:   1
T4A00 000:204.091       FlashBankInfo:
T4A00 000:204.097         Name:            Data Flash
T4A00 000:204.103         BaseAddr:        0x1101F000
T4A00 000:204.117         AlwaysPresent:   1
T4A00 000:204.124     Device entry modified: TLE9879
T4A00 000:204.128       FlashBankInfo:
T4A00 000:204.133         Name:            Code Flash
T4A00 000:204.139         BaseAddr:        0x11000000
T4A00 000:204.152         AlwaysPresent:   1
T4A00 000:204.158       FlashBankInfo:
T4A00 000:204.163         Name:            Data Flash
T4A00 000:204.174         BaseAddr:        0x1101F000
T4A00 000:204.188         AlwaysPresent:   1
T4A00 000:204.195     Device entry modified: TLE9851QXW
T4A00 000:204.204       ChipInfo:
T4A00 000:204.210         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Infineon\TLE985x\Infineon_TLExxx.pex
T4A00 000:204.216       FlashBankInfo:
T4A00 000:204.221         Name:            NACNAD
T4A00 000:204.227         BaseAddr:        0x10FFFFFC
T4A00 000:204.241         AlwaysPresent:   1
T4A00 000:204.246       FlashBankInfo:
T4A00 000:204.252         Name:            Code Flash
T4A00 000:204.257         BaseAddr:        0x11000000
T4A00 000:204.271         AlwaysPresent:   1
T4A00 000:204.277       FlashBankInfo:
T4A00 000:204.282         Name:            Data Flash
T4A00 000:204.288         BaseAddr:        0x1100F000
T4A00 000:204.302         AlwaysPresent:   1
T4A00 000:204.309     Device entry modified: TLE9853QX
T4A00 000:204.317       ChipInfo:
T4A00 000:204.323         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Infineon\TLE985x\Infineon_TLExxx.pex
T4A00 000:204.329       FlashBankInfo:
T4A00 000:204.334         Name:            NACNAD
T4A00 000:204.340         BaseAddr:        0x10FFFFFC
T4A00 000:204.353         AlwaysPresent:   1
T4A00 000:204.358       FlashBankInfo:
T4A00 000:204.364         Name:            Code Flash
T4A00 000:204.369         BaseAddr:        0x11000000
T4A00 000:204.383         AlwaysPresent:   1
T4A00 000:204.388       FlashBankInfo:
T4A00 000:204.394         Name:            Data Flash
T4A00 000:204.400         BaseAddr:        0x1100B000
T4A00 000:204.413         AlwaysPresent:   1
T4A00 000:204.421     Device entry modified: TLE9854QX
T4A00 000:204.429       ChipInfo:
T4A00 000:204.435         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Infineon\TLE985x\Infineon_TLExxx.pex
T4A00 000:204.440       FlashBankInfo:
T4A00 000:204.446         Name:            NACNAD
T4A00 000:204.452         BaseAddr:        0x10FFFFFC
T4A00 000:204.465         AlwaysPresent:   1
T4A00 000:204.471       FlashBankInfo:
T4A00 000:204.477         Name:            Code Flash
T4A00 000:204.482         BaseAddr:        0x11000000
T4A00 000:204.496         AlwaysPresent:   1
T4A00 000:204.502       FlashBankInfo:
T4A00 000:204.507         Name:            Data Flash
T4A00 000:204.515         BaseAddr:        0x1100F000
T4A00 000:204.529         AlwaysPresent:   1
T4A00 000:204.536     Device entry modified: TLE9855QX
T4A00 000:204.543       ChipInfo:
T4A00 000:204.549         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Infineon\TLE985x\Infineon_TLExxx.pex
T4A00 000:204.555       FlashBankInfo:
T4A00 000:204.560         Name:            NACNAD
T4A00 000:204.566         BaseAddr:        0x10FFFFFC
T4A00 000:204.579         AlwaysPresent:   1
T4A00 000:204.585       FlashBankInfo:
T4A00 000:204.590         Name:            Code Flash
T4A00 000:204.596         BaseAddr:        0x11000000
T4A00 000:204.610         AlwaysPresent:   1
T4A00 000:204.615       FlashBankInfo:
T4A00 000:204.621         Name:            Data Flash
T4A00 000:204.626         BaseAddr:        0x11017000
T4A00 000:204.640         AlwaysPresent:   1
T4A00 000:204.647     Device entry modified: GP570NKDx
T4A00 000:204.656       ChipInfo:
T4A00 000:204.662         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.668     Device entry modified: GP570NMDx
T4A00 000:204.677       ChipInfo:
T4A00 000:204.682         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.689     Device entry modified: GP570NMEx
T4A00 000:204.697       ChipInfo:
T4A00 000:204.703         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.710     Device entry modified: GP870NKCG
T4A00 000:204.718       ChipInfo:
T4A00 000:204.724         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.732     Device entry modified: GP870NMDG
T4A00 000:204.739       ChipInfo:
T4A00 000:204.745         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.752     Device entry modified: GP870NMEG
T4A00 000:204.760       ChipInfo:
T4A00 000:204.766         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.773     Device entry modified: UE878NHCG
T4A00 000:204.781       ChipInfo:
T4A00 000:204.787         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.794     Device entry modified: UE878NKDx
T4A00 000:204.802       ChipInfo:
T4A00 000:204.808         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.814     Device entry modified: UE878NKEx
T4A00 000:204.822       ChipInfo:
T4A00 000:204.828         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.835     Device entry modified: UE878NMDx
T4A00 000:204.843       ChipInfo:
T4A00 000:204.849         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.855     Device entry modified: UE878NMEx
T4A00 000:204.863       ChipInfo:
T4A00 000:204.869         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.876     Device entry modified: QPG6095x
T4A00 000:204.884       ChipInfo:
T4A00 000:204.890         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Qorvo\GPxxx\GPxxx.pex
T4A00 000:204.898     Device entry modified: CY8C6xx6_CM0p
T4A00 000:204.905       ChipInfo:
T4A00 000:204.912         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM0p.JLinkScript
T4A00 000:204.918       FlashBankInfo:
T4A00 000:204.923         Name:            FLASH  (Main)
T4A00 000:204.929         BaseAddr:        0x10000000
T4A00 000:204.944         AlwaysPresent:   1
T4A00 000:204.950       FlashBankInfo:
T4A00 000:204.955         Name:            WFLASH (Work)
T4A00 000:204.961         BaseAddr:        0x14000000
T4A00 000:204.975         AlwaysPresent:   1
T4A00 000:204.980       FlashBankInfo:
T4A00 000:204.986         Name:            SFLASH: User Data
T4A00 000:204.992         BaseAddr:        0x16000800
T4A00 000:205.007         AlwaysPresent:   1
T4A00 000:205.013       FlashBankInfo:
T4A00 000:205.018         Name:            SFLASH: NAR
T4A00 000:205.024         BaseAddr:        0x16001A00
T4A00 000:205.038         AlwaysPresent:   1
T4A00 000:205.044       FlashBankInfo:
T4A00 000:205.049         Name:            SFLASH: Public Key
T4A00 000:205.055         BaseAddr:        0x16005A00
T4A00 000:205.069         AlwaysPresent:   1
T4A00 000:205.075       FlashBankInfo:
T4A00 000:205.080         Name:            SFLASH: TOC2
T4A00 000:205.086         BaseAddr:        0x16007C00
T4A00 000:205.100         AlwaysPresent:   1
T4A00 000:205.106       FlashBankInfo:
T4A00 000:205.111         Name:            SMIF
T4A00 000:205.117         BaseAddr:        0x18000000
T4A00 000:205.133     Device entry modified: CY8C6xx6_CM0p_sect256KB
T4A00 000:205.141       ChipInfo:
T4A00 000:205.147         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM0p.JLinkScript
T4A00 000:205.153       FlashBankInfo:
T4A00 000:205.158         Name:            FLASH  (Main)
T4A00 000:205.164         BaseAddr:        0x10000000
T4A00 000:205.178         AlwaysPresent:   1
T4A00 000:205.184       FlashBankInfo:
T4A00 000:205.190         Name:            WFLASH (Work)
T4A00 000:205.195         BaseAddr:        0x14000000
T4A00 000:205.212         AlwaysPresent:   1
T4A00 000:205.219       FlashBankInfo:
T4A00 000:205.224         Name:            SFLASH: User Data
T4A00 000:205.230         BaseAddr:        0x16000800
T4A00 000:205.244         AlwaysPresent:   1
T4A00 000:205.249       FlashBankInfo:
T4A00 000:205.255         Name:            SFLASH: NAR
T4A00 000:205.261         BaseAddr:        0x16001A00
T4A00 000:205.274         AlwaysPresent:   1
T4A00 000:205.280       FlashBankInfo:
T4A00 000:205.285         Name:            SFLASH: Public Key
T4A00 000:205.291         BaseAddr:        0x16005A00
T4A00 000:205.304         AlwaysPresent:   1
T4A00 000:205.310       FlashBankInfo:
T4A00 000:205.315         Name:            SFLASH: TOC2
T4A00 000:205.321         BaseAddr:        0x16007C00
T4A00 000:205.334         AlwaysPresent:   1
T4A00 000:205.340       FlashBankInfo:
T4A00 000:205.345         Name:            SMIF
T4A00 000:205.351         BaseAddr:        0x18000000
T4A00 000:205.366     Device entry modified: CY8C6xx6_CM0p_tm
T4A00 000:205.373       ChipInfo:
T4A00 000:205.380         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM0p_tm_xA.JLinkScript
T4A00 000:205.386       FlashBankInfo:
T4A00 000:205.391         Name:            FLASH  (Main)
T4A00 000:205.397         BaseAddr:        0x10000000
T4A00 000:205.410         AlwaysPresent:   1
T4A00 000:205.416       FlashBankInfo:
T4A00 000:205.421         Name:            WFLASH (Work)
T4A00 000:205.427         BaseAddr:        0x14000000
T4A00 000:205.440         AlwaysPresent:   1
T4A00 000:205.446       FlashBankInfo:
T4A00 000:205.451         Name:            SFLASH: User Data
T4A00 000:205.457         BaseAddr:        0x16000800
T4A00 000:205.470         AlwaysPresent:   1
T4A00 000:205.476       FlashBankInfo:
T4A00 000:205.482         Name:            SFLASH: NAR
T4A00 000:205.487         BaseAddr:        0x16001A00
T4A00 000:205.501         AlwaysPresent:   1
T4A00 000:205.507       FlashBankInfo:
T4A00 000:205.513         Name:            SFLASH: Public Key
T4A00 000:205.518         BaseAddr:        0x16005A00
T4A00 000:205.532         AlwaysPresent:   1
T4A00 000:205.537       FlashBankInfo:
T4A00 000:205.543         Name:            SFLASH: TOC2
T4A00 000:205.548         BaseAddr:        0x16007C00
T4A00 000:205.562         AlwaysPresent:   1
T4A00 000:205.568       FlashBankInfo:
T4A00 000:205.573         Name:            SMIF
T4A00 000:205.579         BaseAddr:        0x18000000
T4A00 000:205.594     Device entry modified: CY8C6xx6_CM0p_sect256KB_tm
T4A00 000:205.602       ChipInfo:
T4A00 000:205.608         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM0p_tm_xA.JLinkScript
T4A00 000:205.615       FlashBankInfo:
T4A00 000:205.620         Name:            FLASH  (Main)
T4A00 000:205.626         BaseAddr:        0x10000000
T4A00 000:205.640         AlwaysPresent:   1
T4A00 000:205.645       FlashBankInfo:
T4A00 000:205.651         Name:            WFLASH (Work)
T4A00 000:205.657         BaseAddr:        0x14000000
T4A00 000:205.670         AlwaysPresent:   1
T4A00 000:205.676       FlashBankInfo:
T4A00 000:205.681         Name:            SFLASH: User Data
T4A00 000:205.687         BaseAddr:        0x16000800
T4A00 000:205.700         AlwaysPresent:   1
T4A00 000:205.706       FlashBankInfo:
T4A00 000:205.711         Name:            SFLASH: NAR
T4A00 000:205.717         BaseAddr:        0x16001A00
T4A00 000:205.730         AlwaysPresent:   1
T4A00 000:205.736       FlashBankInfo:
T4A00 000:205.742         Name:            SFLASH: Public Key
T4A00 000:205.747         BaseAddr:        0x16005A00
T4A00 000:205.761         AlwaysPresent:   1
T4A00 000:205.767       FlashBankInfo:
T4A00 000:205.772         Name:            SFLASH: TOC2
T4A00 000:205.778         BaseAddr:        0x16007C00
T4A00 000:205.791         AlwaysPresent:   1
T4A00 000:205.797       FlashBankInfo:
T4A00 000:205.802         Name:            SMIF
T4A00 000:205.808         BaseAddr:        0x18000000
T4A00 000:205.822     Device entry modified: CY8C6xx6_CM4
T4A00 000:205.830       ChipInfo:
T4A00 000:205.836         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM4.JLinkScript
T4A00 000:205.843       FlashBankInfo:
T4A00 000:205.849         Name:            FLASH  (Main)
T4A00 000:205.854         BaseAddr:        0x10000000
T4A00 000:205.868         AlwaysPresent:   1
T4A00 000:205.873       FlashBankInfo:
T4A00 000:205.879         Name:            WFLASH (Work)
T4A00 000:205.884         BaseAddr:        0x14000000
T4A00 000:205.898         AlwaysPresent:   1
T4A00 000:205.904       FlashBankInfo:
T4A00 000:205.909         Name:            SFLASH: User Data
T4A00 000:205.915         BaseAddr:        0x16000800
T4A00 000:205.928         AlwaysPresent:   1
T4A00 000:205.934       FlashBankInfo:
T4A00 000:205.939         Name:            SFLASH: NAR
T4A00 000:205.945         BaseAddr:        0x16001A00
T4A00 000:205.958         AlwaysPresent:   1
T4A00 000:205.964       FlashBankInfo:
T4A00 000:205.970         Name:            SFLASH: Public Key
T4A00 000:205.975         BaseAddr:        0x16005A00
T4A00 000:205.989         AlwaysPresent:   1
T4A00 000:205.994       FlashBankInfo:
T4A00 000:206.000         Name:            SFLASH: TOC2
T4A00 000:206.005         BaseAddr:        0x16007C00
T4A00 000:206.019         AlwaysPresent:   1
T4A00 000:206.024       FlashBankInfo:
T4A00 000:206.030         Name:            SMIF
T4A00 000:206.035         BaseAddr:        0x18000000
T4A00 000:206.050     Device entry modified: CY8C6xx6_CM4_sect256KB
T4A00 000:206.058       ChipInfo:
T4A00 000:206.064         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM4.JLinkScript
T4A00 000:206.070       FlashBankInfo:
T4A00 000:206.076         Name:            FLASH  (Main)
T4A00 000:206.081         BaseAddr:        0x10000000
T4A00 000:206.095         AlwaysPresent:   1
T4A00 000:206.101       FlashBankInfo:
T4A00 000:206.106         Name:            WFLASH (Work)
T4A00 000:206.112         BaseAddr:        0x14000000
T4A00 000:206.125         AlwaysPresent:   1
T4A00 000:206.131       FlashBankInfo:
T4A00 000:206.136         Name:            SFLASH: User Data
T4A00 000:206.142         BaseAddr:        0x16000800
T4A00 000:206.155         AlwaysPresent:   1
T4A00 000:206.161       FlashBankInfo:
T4A00 000:206.166         Name:            SFLASH: NAR
T4A00 000:206.172         BaseAddr:        0x16001A00
T4A00 000:206.185         AlwaysPresent:   1
T4A00 000:206.191       FlashBankInfo:
T4A00 000:206.197         Name:            SFLASH: Public Key
T4A00 000:206.203         BaseAddr:        0x16005A00
T4A00 000:206.217         AlwaysPresent:   1
T4A00 000:206.223       FlashBankInfo:
T4A00 000:206.228         Name:            SFLASH: TOC2
T4A00 000:206.234         BaseAddr:        0x16007C00
T4A00 000:206.247         AlwaysPresent:   1
T4A00 000:206.253       FlashBankInfo:
T4A00 000:206.258         Name:            SMIF
T4A00 000:206.264         BaseAddr:        0x18000000
T4A00 000:206.278     Device entry modified: CY8C6xx7_CM0p
T4A00 000:206.286       ChipInfo:
T4A00 000:206.292         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM0p.JLinkScript
T4A00 000:206.298       FlashBankInfo:
T4A00 000:206.303         Name:            FLASH  (Main)
T4A00 000:206.309         BaseAddr:        0x10000000
T4A00 000:206.322         AlwaysPresent:   1
T4A00 000:206.328       FlashBankInfo:
T4A00 000:206.333         Name:            WFLASH (Work)
T4A00 000:206.339         BaseAddr:        0x14000000
T4A00 000:206.352         AlwaysPresent:   1
T4A00 000:206.358       FlashBankInfo:
T4A00 000:206.363         Name:            SFLASH: User Data
T4A00 000:206.369         BaseAddr:        0x16000800
T4A00 000:206.382         AlwaysPresent:   1
T4A00 000:206.388       FlashBankInfo:
T4A00 000:206.393         Name:            SFLASH: NAR
T4A00 000:206.399         BaseAddr:        0x16001A00
T4A00 000:206.412         AlwaysPresent:   1
T4A00 000:206.418       FlashBankInfo:
T4A00 000:206.423         Name:            SFLASH: Public Key
T4A00 000:206.429         BaseAddr:        0x16005A00
T4A00 000:206.442         AlwaysPresent:   1
T4A00 000:206.448       FlashBankInfo:
T4A00 000:206.454         Name:            SFLASH: TOC2
T4A00 000:206.459         BaseAddr:        0x16007C00
T4A00 000:206.473         AlwaysPresent:   1
T4A00 000:206.478       FlashBankInfo:
T4A00 000:206.484         Name:            SMIF
T4A00 000:206.489         BaseAddr:        0x18000000
T4A00 000:206.505     Device entry modified: CY8C6xx7_CM0p_sect256KB
T4A00 000:206.513       ChipInfo:
T4A00 000:206.519         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM0p.JLinkScript
T4A00 000:206.524       FlashBankInfo:
T4A00 000:206.530         Name:            FLASH  (Main)
T4A00 000:206.535         BaseAddr:        0x10000000
T4A00 000:206.549         AlwaysPresent:   1
T4A00 000:206.555       FlashBankInfo:
T4A00 000:206.560         Name:            WFLASH (Work)
T4A00 000:206.566         BaseAddr:        0x14000000
T4A00 000:206.579         AlwaysPresent:   1
T4A00 000:206.585       FlashBankInfo:
T4A00 000:206.591         Name:            SFLASH: User Data
T4A00 000:206.596         BaseAddr:        0x16000800
T4A00 000:206.610         AlwaysPresent:   1
T4A00 000:206.615       FlashBankInfo:
T4A00 000:206.621         Name:            SFLASH: NAR
T4A00 000:206.626         BaseAddr:        0x16001A00
T4A00 000:206.639         AlwaysPresent:   1
T4A00 000:206.645       FlashBankInfo:
T4A00 000:206.651         Name:            SFLASH: Public Key
T4A00 000:206.656         BaseAddr:        0x16005A00
T4A00 000:206.670         AlwaysPresent:   1
T4A00 000:206.676       FlashBankInfo:
T4A00 000:206.681         Name:            SFLASH: TOC2
T4A00 000:206.687         BaseAddr:        0x16007C00
T4A00 000:206.700         AlwaysPresent:   1
T4A00 000:206.706       FlashBankInfo:
T4A00 000:206.711         Name:            SMIF
T4A00 000:206.717         BaseAddr:        0x18000000
T4A00 000:206.731     Device entry modified: CY8C6xx7_CM0p_tm
T4A00 000:206.739       ChipInfo:
T4A00 000:206.745         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM0p_tm_xA.JLinkScript
T4A00 000:206.751       FlashBankInfo:
T4A00 000:206.757         Name:            FLASH  (Main)
T4A00 000:206.762         BaseAddr:        0x10000000
T4A00 000:206.776         AlwaysPresent:   1
T4A00 000:206.781       FlashBankInfo:
T4A00 000:206.788         Name:            WFLASH (Work)
T4A00 000:206.794         BaseAddr:        0x14000000
T4A00 000:206.807         AlwaysPresent:   1
T4A00 000:206.812       FlashBankInfo:
T4A00 000:206.818         Name:            SFLASH: User Data
T4A00 000:206.824         BaseAddr:        0x16000800
T4A00 000:206.837         AlwaysPresent:   1
T4A00 000:206.843       FlashBankInfo:
T4A00 000:206.848         Name:            SFLASH: NAR
T4A00 000:206.854         BaseAddr:        0x16001A00
T4A00 000:206.867         AlwaysPresent:   1
T4A00 000:206.873       FlashBankInfo:
T4A00 000:206.879         Name:            SFLASH: Public Key
T4A00 000:206.884         BaseAddr:        0x16005A00
T4A00 000:206.897         AlwaysPresent:   1
T4A00 000:206.903       FlashBankInfo:
T4A00 000:206.909         Name:            SFLASH: TOC2
T4A00 000:206.914         BaseAddr:        0x16007C00
T4A00 000:206.928         AlwaysPresent:   1
T4A00 000:206.934       FlashBankInfo:
T4A00 000:206.939         Name:            SMIF
T4A00 000:206.945         BaseAddr:        0x18000000
T4A00 000:206.960     Device entry modified: CY8C6xx7_CM0p_sect256KB_tm
T4A00 000:206.967       ChipInfo:
T4A00 000:206.974         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM0p_tm_xA.JLinkScript
T4A00 000:206.980       FlashBankInfo:
T4A00 000:206.985         Name:            FLASH  (Main)
T4A00 000:206.991         BaseAddr:        0x10000000
T4A00 000:207.004         AlwaysPresent:   1
T4A00 000:207.010       FlashBankInfo:
T4A00 000:207.015         Name:            WFLASH (Work)
T4A00 000:207.021         BaseAddr:        0x14000000
T4A00 000:207.034         AlwaysPresent:   1
T4A00 000:207.040       FlashBankInfo:
T4A00 000:207.045         Name:            SFLASH: User Data
T4A00 000:207.051         BaseAddr:        0x16000800
T4A00 000:207.064         AlwaysPresent:   1
T4A00 000:207.070       FlashBankInfo:
T4A00 000:207.076         Name:            SFLASH: NAR
T4A00 000:207.081         BaseAddr:        0x16001A00
T4A00 000:207.095         AlwaysPresent:   1
T4A00 000:207.100       FlashBankInfo:
T4A00 000:207.106         Name:            SFLASH: Public Key
T4A00 000:207.111         BaseAddr:        0x16005A00
T4A00 000:207.125         AlwaysPresent:   1
T4A00 000:207.130       FlashBankInfo:
T4A00 000:207.136         Name:            SFLASH: TOC2
T4A00 000:207.141         BaseAddr:        0x16007C00
T4A00 000:207.155         AlwaysPresent:   1
T4A00 000:207.161       FlashBankInfo:
T4A00 000:207.166         Name:            SMIF
T4A00 000:207.172         BaseAddr:        0x18000000
T4A00 000:207.186     Device entry modified: CY8C6xx7_CM4
T4A00 000:207.194       ChipInfo:
T4A00 000:207.200         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM4.JLinkScript
T4A00 000:207.206       FlashBankInfo:
T4A00 000:207.212         Name:            FLASH  (Main)
T4A00 000:207.217         BaseAddr:        0x10000000
T4A00 000:207.230         AlwaysPresent:   1
T4A00 000:207.236       FlashBankInfo:
T4A00 000:207.242         Name:            WFLASH (Work)
T4A00 000:207.247         BaseAddr:        0x14000000
T4A00 000:207.261         AlwaysPresent:   1
T4A00 000:207.266       FlashBankInfo:
T4A00 000:207.272         Name:            SFLASH: User Data
T4A00 000:207.278         BaseAddr:        0x16000800
T4A00 000:207.291         AlwaysPresent:   1
T4A00 000:207.297       FlashBankInfo:
T4A00 000:207.302         Name:            SFLASH: NAR
T4A00 000:207.308         BaseAddr:        0x16001A00
T4A00 000:207.321         AlwaysPresent:   1
T4A00 000:207.327       FlashBankInfo:
T4A00 000:207.332         Name:            SFLASH: Public Key
T4A00 000:207.338         BaseAddr:        0x16005A00
T4A00 000:207.351         AlwaysPresent:   1
T4A00 000:207.357       FlashBankInfo:
T4A00 000:207.362         Name:            SFLASH: TOC2
T4A00 000:207.368         BaseAddr:        0x16007C00
T4A00 000:207.381         AlwaysPresent:   1
T4A00 000:207.388       FlashBankInfo:
T4A00 000:207.393         Name:            SMIF
T4A00 000:207.399         BaseAddr:        0x18000000
T4A00 000:207.413     Device entry modified: CY8C6xx7_CM4_sect256KB
T4A00 000:207.421       ChipInfo:
T4A00 000:207.427         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Cypress\PSoC6\CY8C6xxx_CM4.JLinkScript
T4A00 000:207.434       FlashBankInfo:
T4A00 000:207.439         Name:            FLASH  (Main)
T4A00 000:207.445         BaseAddr:        0x10000000
T4A00 000:207.458         AlwaysPresent:   1
T4A00 000:207.464       FlashBankInfo:
T4A00 000:207.469         Name:            WFLASH (Work)
T4A00 000:207.475         BaseAddr:        0x14000000
T4A00 000:207.488         AlwaysPresent:   1
T4A00 000:207.494       FlashBankInfo:
T4A00 000:207.499         Name:            SFLASH: User Data
T4A00 000:207.505         BaseAddr:        0x16000800
T4A00 000:207.518         AlwaysPresent:   1
T4A00 000:207.524       FlashBankInfo:
T4A00 000:207.529         Name:            SFLASH: NAR
T4A00 000:207.535         BaseAddr:        0x16001A00
T4A00 000:207.548         AlwaysPresent:   1
T4A00 000:207.554       FlashBankInfo:
T4A00 000:207.560         Name:            SFLASH: Public Key
T4A00 000:207.565         BaseAddr:        0x16005A00
T4A00 000:207.578         AlwaysPresent:   1
T4A00 000:207.584       FlashBankInfo:
T4A00 000:207.590         Name:            SFLASH: TOC2
T4A00 000:207.595         BaseAddr:        0x16007C00
T4A00 000:207.608         AlwaysPresent:   1
T4A00 000:207.614       FlashBankInfo:
T4A00 000:207.619         Name:            SMIF
T4A00 000:207.625         BaseAddr:        0x18000000
T4A00 000:207.639     Device entry modified: RSL10
T4A00 000:207.648       ChipInfo:
T4A00 000:207.654         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ONSemiconductor\RSL10\ONSemiconductor_RSL10.JLinkScript
T4A00 000:207.660       FlashBankInfo:
T4A00 000:207.665         Name:            Main Flash
T4A00 000:207.671         BaseAddr:        0x00100000
T4A00 000:207.688         AlwaysPresent:   1
T4A00 000:207.693       FlashBankInfo:
T4A00 000:207.699         Name:            NVR Flash
T4A00 000:207.705         BaseAddr:        0x00080000
T4A00 000:207.720     Device entry modified: E31ARTY
T4A00 000:207.726       FlashBankInfo:
T4A00 000:207.731         Name:            QSPI Flash
T4A00 000:207.737         BaseAddr:        0x40000000
T4A00 000:207.753     Device entry modified: STM32F412CE
T4A00 000:207.758       FlashBankInfo:
T4A00 000:207.764         Name:            QSPI Flash
T4A00 000:207.769         BaseAddr:        0x90000000
T4A00 000:207.785     Device entry modified: STM32F412RE
T4A00 000:207.790       FlashBankInfo:
T4A00 000:207.796         Name:            QSPI Flash
T4A00 000:207.801         BaseAddr:        0x90000000
T4A00 000:207.816     Device entry modified: STM32F412ZG
T4A00 000:207.821       FlashBankInfo:
T4A00 000:207.827         Name:            QSPI Flash
T4A00 000:207.832         BaseAddr:        0x90000000
T4A00 000:207.848     Device entry modified: STM32F412RG
T4A00 000:207.853       FlashBankInfo:
T4A00 000:207.858         Name:            QSPI Flash
T4A00 000:207.864         BaseAddr:        0x90000000
T4A00 000:207.878     Device entry modified: STM32F412VE
T4A00 000:207.883       FlashBankInfo:
T4A00 000:207.889         Name:            QSPI Flash
T4A00 000:207.894         BaseAddr:        0x90000000
T4A00 000:207.909     Device entry modified: STM32F412VG
T4A00 000:207.914       FlashBankInfo:
T4A00 000:207.919         Name:            QSPI Flash
T4A00 000:207.925         BaseAddr:        0x90000000
T4A00 000:207.940     Device entry modified: STM32F412CG
T4A00 000:207.945       FlashBankInfo:
T4A00 000:207.950         Name:            QSPI Flash
T4A00 000:207.956         BaseAddr:        0x90000000
T4A00 000:207.971     Device entry modified: STM32F412ZE
T4A00 000:207.976       FlashBankInfo:
T4A00 000:207.981         Name:            QSPI Flash
T4A00 000:208.026         BaseAddr:        0x90000000
T4A00 000:208.043     Device entry modified: STM32F469AE
T4A00 000:208.048       FlashBankInfo:
T4A00 000:208.054         Name:            QSPI Flash
T4A00 000:208.060         BaseAddr:        0x90000000
T4A00 000:208.075     Device entry modified: STM32F469AG
T4A00 000:208.087       FlashBankInfo:
T4A00 000:208.092         Name:            QSPI Flash
T4A00 000:208.098         BaseAddr:        0x90000000
T4A00 000:208.113     Device entry modified: STM32F469AI
T4A00 000:208.118       FlashBankInfo:
T4A00 000:208.123         Name:            QSPI Flash
T4A00 000:208.129         BaseAddr:        0x90000000
T4A00 000:208.143     Device entry modified: STM32F469BE
T4A00 000:208.149       FlashBankInfo:
T4A00 000:208.154         Name:            QSPI Flash
T4A00 000:208.160         BaseAddr:        0x90000000
T4A00 000:208.174     Device entry modified: STM32F469BG
T4A00 000:208.179       FlashBankInfo:
T4A00 000:208.185         Name:            QSPI Flash
T4A00 000:208.190         BaseAddr:        0x90000000
T4A00 000:208.206     Device entry modified: STM32F469BI
T4A00 000:208.211       FlashBankInfo:
T4A00 000:208.216         Name:            QSPI Flash
T4A00 000:208.222         BaseAddr:        0x90000000
T4A00 000:208.236     Device entry modified: STM32F469IE
T4A00 000:208.241       FlashBankInfo:
T4A00 000:208.247         Name:            QSPI Flash
T4A00 000:208.252         BaseAddr:        0x90000000
T4A00 000:208.267     Device entry modified: STM32F469IG
T4A00 000:208.272       FlashBankInfo:
T4A00 000:208.277         Name:            QSPI Flash
T4A00 000:208.283         BaseAddr:        0x90000000
T4A00 000:208.298     Device entry modified: STM32F469II
T4A00 000:208.303       FlashBankInfo:
T4A00 000:208.308         Name:            QSPI Flash
T4A00 000:208.314         BaseAddr:        0x90000000
T4A00 000:208.328     Device entry modified: STM32F469NE
T4A00 000:208.333       FlashBankInfo:
T4A00 000:208.339         Name:            QSPI Flash
T4A00 000:208.344         BaseAddr:        0x90000000
T4A00 000:208.359     Device entry modified: STM32F469NG
T4A00 000:208.363       FlashBankInfo:
T4A00 000:208.369         Name:            QSPI Flash
T4A00 000:208.375         BaseAddr:        0x90000000
T4A00 000:208.389     Device entry modified: STM32F469NI
T4A00 000:208.394       FlashBankInfo:
T4A00 000:208.400         Name:            QSPI Flash
T4A00 000:208.405         BaseAddr:        0x90000000
T4A00 000:208.419     Device entry modified: STM32F469VE
T4A00 000:208.424       FlashBankInfo:
T4A00 000:208.430         Name:            QSPI Flash
T4A00 000:208.435         BaseAddr:        0x90000000
T4A00 000:208.450     Device entry modified: STM32F469VG
T4A00 000:208.455       FlashBankInfo:
T4A00 000:208.460         Name:            QSPI Flash
T4A00 000:208.466         BaseAddr:        0x90000000
T4A00 000:208.481     Device entry modified: STM32F469VI
T4A00 000:208.486       FlashBankInfo:
T4A00 000:208.491         Name:            QSPI Flash
T4A00 000:208.497         BaseAddr:        0x90000000
T4A00 000:208.511     Device entry modified: STM32F469ZE
T4A00 000:208.516       FlashBankInfo:
T4A00 000:208.522         Name:            QSPI Flash
T4A00 000:208.527         BaseAddr:        0x90000000
T4A00 000:208.542     Device entry modified: STM32F469ZG
T4A00 000:208.547       FlashBankInfo:
T4A00 000:208.552         Name:            QSPI Flash
T4A00 000:208.558         BaseAddr:        0x90000000
T4A00 000:208.573     Device entry modified: STM32F469ZI
T4A00 000:208.577       FlashBankInfo:
T4A00 000:208.583         Name:            QSPI Flash
T4A00 000:208.588         BaseAddr:        0x90000000
T4A00 000:208.604     Device entry modified: STM32F723IC
T4A00 000:208.609       FlashBankInfo:
T4A00 000:208.614         Name:            QSPI Flash
T4A00 000:208.620         BaseAddr:        0x90000000
T4A00 000:208.636     Device entry modified: STM32F723IE
T4A00 000:208.641       FlashBankInfo:
T4A00 000:208.657         Name:            QSPI Flash
T4A00 000:208.663         BaseAddr:        0x90000000
T4A00 000:208.678     Device entry modified: STM32F723VE
T4A00 000:208.683       FlashBankInfo:
T4A00 000:208.688         Name:            QSPI Flash
T4A00 000:208.694         BaseAddr:        0x90000000
T4A00 000:208.709     Device entry modified: STM32F723ZC
T4A00 000:208.713       FlashBankInfo:
T4A00 000:208.719         Name:            QSPI Flash
T4A00 000:208.725         BaseAddr:        0x90000000
T4A00 000:208.740     Device entry modified: STM32F723ZE
T4A00 000:208.744       FlashBankInfo:
T4A00 000:208.750         Name:            QSPI Flash
T4A00 000:208.756         BaseAddr:        0x90000000
T4A00 000:208.770     Device entry modified: STM32F745IE
T4A00 000:208.775       FlashBankInfo:
T4A00 000:208.781         Name:            QSPI Flash
T4A00 000:208.786         BaseAddr:        0x90000000
T4A00 000:208.801     Device entry modified: STM32F745IG
T4A00 000:208.806       FlashBankInfo:
T4A00 000:208.812         Name:            QSPI Flash
T4A00 000:208.817         BaseAddr:        0x90000000
T4A00 000:208.832     Device entry modified: STM32F745VE
T4A00 000:208.837       FlashBankInfo:
T4A00 000:208.842         Name:            QSPI Flash
T4A00 000:208.848         BaseAddr:        0x90000000
T4A00 000:208.863     Device entry modified: STM32F745VG
T4A00 000:208.867       FlashBankInfo:
T4A00 000:208.873         Name:            QSPI Flash
T4A00 000:208.878         BaseAddr:        0x90000000
T4A00 000:208.893     Device entry modified: STM32F745ZE
T4A00 000:208.897       FlashBankInfo:
T4A00 000:208.903         Name:            QSPI Flash
T4A00 000:208.909         BaseAddr:        0x90000000
T4A00 000:208.923     Device entry modified: STM32F745ZG
T4A00 000:208.928       FlashBankInfo:
T4A00 000:208.933         Name:            QSPI Flash
T4A00 000:208.939         BaseAddr:        0x90000000
T4A00 000:208.953     Device entry modified: STM32F746BE
T4A00 000:208.958       FlashBankInfo:
T4A00 000:208.964         Name:            QSPI Flash
T4A00 000:208.969         BaseAddr:        0x90000000
T4A00 000:208.984     Device entry modified: STM32F746BG
T4A00 000:208.989       FlashBankInfo:
T4A00 000:208.994         Name:            QSPI Flash
T4A00 000:209.000         BaseAddr:        0x90000000
T4A00 000:209.014     Device entry modified: STM32F746IE
T4A00 000:209.019       FlashBankInfo:
T4A00 000:209.024         Name:            QSPI Flash
T4A00 000:209.030         BaseAddr:        0x90000000
T4A00 000:209.045     Device entry modified: STM32F746IG
T4A00 000:209.049       FlashBankInfo:
T4A00 000:209.055         Name:            QSPI Flash
T4A00 000:209.060         BaseAddr:        0x90000000
T4A00 000:209.075     Device entry modified: STM32F746NE
T4A00 000:209.080       FlashBankInfo:
T4A00 000:209.086         Name:            QSPI Flash
T4A00 000:209.091         BaseAddr:        0x90000000
T4A00 000:209.106     Device entry modified: STM32F746NG
T4A00 000:209.111       FlashBankInfo:
T4A00 000:209.116         Name:            QSPI Flash
T4A00 000:209.122         BaseAddr:        0x90000000
T4A00 000:209.136     Device entry modified: STM32F746VE
T4A00 000:209.141       FlashBankInfo:
T4A00 000:209.146         Name:            QSPI Flash
T4A00 000:209.152         BaseAddr:        0x90000000
T4A00 000:209.166     Device entry modified: STM32F746VG
T4A00 000:209.171       FlashBankInfo:
T4A00 000:209.176         Name:            QSPI Flash
T4A00 000:209.182         BaseAddr:        0x90000000
T4A00 000:209.196     Device entry modified: STM32F746ZE
T4A00 000:209.201       FlashBankInfo:
T4A00 000:209.206         Name:            QSPI Flash
T4A00 000:209.212         BaseAddr:        0x90000000
T4A00 000:209.226     Device entry modified: STM32F746ZG
T4A00 000:209.231       FlashBankInfo:
T4A00 000:209.237         Name:            QSPI Flash
T4A00 000:209.243         BaseAddr:        0x90000000
T4A00 000:209.257     Device entry modified: STM32F756BE
T4A00 000:209.270       FlashBankInfo:
T4A00 000:209.275         Name:            QSPI Flash
T4A00 000:209.281         BaseAddr:        0x90000000
T4A00 000:209.296     Device entry modified: STM32F756BG
T4A00 000:209.301       FlashBankInfo:
T4A00 000:209.307         Name:            QSPI Flash
T4A00 000:209.312         BaseAddr:        0x90000000
T4A00 000:209.327     Device entry modified: STM32F756IE
T4A00 000:209.332       FlashBankInfo:
T4A00 000:209.337         Name:            QSPI Flash
T4A00 000:209.343         BaseAddr:        0x90000000
T4A00 000:209.358     Device entry modified: STM32F756IG
T4A00 000:209.362       FlashBankInfo:
T4A00 000:209.368         Name:            QSPI Flash
T4A00 000:209.373         BaseAddr:        0x90000000
T4A00 000:209.388     Device entry modified: STM32F756NE
T4A00 000:209.393       FlashBankInfo:
T4A00 000:209.398         Name:            QSPI Flash
T4A00 000:209.404         BaseAddr:        0x90000000
T4A00 000:209.418     Device entry modified: STM32F756NG
T4A00 000:209.423       FlashBankInfo:
T4A00 000:209.429         Name:            QSPI Flash
T4A00 000:209.434         BaseAddr:        0x90000000
T4A00 000:209.448     Device entry modified: STM32F756VE
T4A00 000:209.453       FlashBankInfo:
T4A00 000:209.459         Name:            QSPI Flash
T4A00 000:209.464         BaseAddr:        0x90000000
T4A00 000:209.479     Device entry modified: STM32F756VG
T4A00 000:209.483       FlashBankInfo:
T4A00 000:209.489         Name:            QSPI Flash
T4A00 000:209.495         BaseAddr:        0x90000000
T4A00 000:209.509     Device entry modified: STM32F756ZE
T4A00 000:209.514       FlashBankInfo:
T4A00 000:209.520         Name:            QSPI Flash
T4A00 000:209.525         BaseAddr:        0x90000000
T4A00 000:209.540     Device entry modified: STM32F756ZG
T4A00 000:209.545       FlashBankInfo:
T4A00 000:209.551         Name:            QSPI Flash
T4A00 000:209.556         BaseAddr:        0x90000000
T4A00 000:209.572     Device entry modified: STM32F765BG
T4A00 000:209.576       FlashBankInfo:
T4A00 000:209.582         Name:            QSPI Flash
T4A00 000:209.588         BaseAddr:        0x90000000
T4A00 000:209.602     Device entry modified: STM32F765BI
T4A00 000:209.607       FlashBankInfo:
T4A00 000:209.612         Name:            QSPI Flash
T4A00 000:209.618         BaseAddr:        0x90000000
T4A00 000:209.632     Device entry modified: STM32F765IG
T4A00 000:209.637       FlashBankInfo:
T4A00 000:209.643         Name:            QSPI Flash
T4A00 000:209.648         BaseAddr:        0x90000000
T4A00 000:209.663     Device entry modified: STM32F765II
T4A00 000:209.668       FlashBankInfo:
T4A00 000:209.673         Name:            QSPI Flash
T4A00 000:209.679         BaseAddr:        0x90000000
T4A00 000:209.693     Device entry modified: STM32F765NG
T4A00 000:209.698       FlashBankInfo:
T4A00 000:209.703         Name:            QSPI Flash
T4A00 000:209.709         BaseAddr:        0x90000000
T4A00 000:209.723     Device entry modified: STM32F765NI
T4A00 000:209.728       FlashBankInfo:
T4A00 000:209.733         Name:            QSPI Flash
T4A00 000:209.739         BaseAddr:        0x90000000
T4A00 000:209.753     Device entry modified: STM32F765VG
T4A00 000:209.758       FlashBankInfo:
T4A00 000:209.763         Name:            QSPI Flash
T4A00 000:209.769         BaseAddr:        0x90000000
T4A00 000:209.784     Device entry modified: STM32F765VI
T4A00 000:209.788       FlashBankInfo:
T4A00 000:209.794         Name:            QSPI Flash
T4A00 000:209.799         BaseAddr:        0x90000000
T4A00 000:209.813     Device entry modified: STM32F765ZG
T4A00 000:209.818       FlashBankInfo:
T4A00 000:209.824         Name:            QSPI Flash
T4A00 000:209.829         BaseAddr:        0x90000000
T4A00 000:209.844     Device entry modified: STM32F765ZI
T4A00 000:209.849       FlashBankInfo:
T4A00 000:209.854         Name:            QSPI Flash
T4A00 000:209.860         BaseAddr:        0x90000000
T4A00 000:209.882     Device entry modified: STM32F767BG
T4A00 000:209.887       FlashBankInfo:
T4A00 000:209.893         Name:            QSPI Flash
T4A00 000:209.898         BaseAddr:        0x90000000
T4A00 000:209.913     Device entry modified: STM32F767BI
T4A00 000:209.918       FlashBankInfo:
T4A00 000:209.923         Name:            QSPI Flash
T4A00 000:209.929         BaseAddr:        0x90000000
T4A00 000:209.943     Device entry modified: STM32F767IG
T4A00 000:209.948       FlashBankInfo:
T4A00 000:209.953         Name:            QSPI Flash
T4A00 000:209.959         BaseAddr:        0x90000000
T4A00 000:209.974     Device entry modified: STM32F767II
T4A00 000:209.978       FlashBankInfo:
T4A00 000:209.984         Name:            QSPI Flash
T4A00 000:209.990         BaseAddr:        0x90000000
T4A00 000:210.004     Device entry modified: STM32F767NG
T4A00 000:210.009       FlashBankInfo:
T4A00 000:210.014         Name:            QSPI Flash
T4A00 000:210.020         BaseAddr:        0x90000000
T4A00 000:210.035     Device entry modified: STM32F767NI
T4A00 000:210.040       FlashBankInfo:
T4A00 000:210.045         Name:            QSPI Flash
T4A00 000:210.051         BaseAddr:        0x90000000
T4A00 000:210.065     Device entry modified: STM32F767VG
T4A00 000:210.070       FlashBankInfo:
T4A00 000:210.075         Name:            QSPI Flash
T4A00 000:210.081         BaseAddr:        0x90000000
T4A00 000:210.096     Device entry modified: STM32F767VI
T4A00 000:210.101       FlashBankInfo:
T4A00 000:210.106         Name:            QSPI Flash
T4A00 000:210.112         BaseAddr:        0x90000000
T4A00 000:210.126     Device entry modified: STM32F767ZG
T4A00 000:210.131       FlashBankInfo:
T4A00 000:210.137         Name:            QSPI Flash
T4A00 000:210.142         BaseAddr:        0x90000000
T4A00 000:210.157     Device entry modified: STM32F767ZI
T4A00 000:210.161       FlashBankInfo:
T4A00 000:210.167         Name:            QSPI Flash
T4A00 000:210.173         BaseAddr:        0x90000000
T4A00 000:210.186     Device entry modified: STM32F768AI
T4A00 000:210.191       FlashBankInfo:
T4A00 000:210.197         Name:            QSPI Flash
T4A00 000:210.202         BaseAddr:        0x90000000
T4A00 000:210.217     Device entry modified: STM32F769AG
T4A00 000:210.222       FlashBankInfo:
T4A00 000:210.227         Name:            QSPI Flash
T4A00 000:210.233         BaseAddr:        0x90000000
T4A00 000:210.247     Device entry modified: STM32F769AI
T4A00 000:210.251       FlashBankInfo:
T4A00 000:210.257         Name:            QSPI Flash
T4A00 000:210.262         BaseAddr:        0x90000000
T4A00 000:210.277     Device entry modified: STM32F769BG
T4A00 000:210.282       FlashBankInfo:
T4A00 000:210.287         Name:            QSPI Flash
T4A00 000:210.293         BaseAddr:        0x90000000
T4A00 000:210.307     Device entry modified: STM32F769BI
T4A00 000:210.312       FlashBankInfo:
T4A00 000:210.317         Name:            QSPI Flash
T4A00 000:210.323         BaseAddr:        0x90000000
T4A00 000:210.337     Device entry modified: STM32F769IG
T4A00 000:210.342       FlashBankInfo:
T4A00 000:210.347         Name:            QSPI Flash
T4A00 000:210.353         BaseAddr:        0x90000000
T4A00 000:210.367     Device entry modified: STM32F769II
T4A00 000:210.372       FlashBankInfo:
T4A00 000:210.377         Name:            QSPI Flash
T4A00 000:210.383         BaseAddr:        0x90000000
T4A00 000:210.397     Device entry modified: STM32F769NG
T4A00 000:210.401       FlashBankInfo:
T4A00 000:210.407         Name:            QSPI Flash
T4A00 000:210.413         BaseAddr:        0x90000000
T4A00 000:210.427     Device entry modified: STM32F769NI
T4A00 000:210.432       FlashBankInfo:
T4A00 000:210.438         Name:            QSPI Flash
T4A00 000:210.443         BaseAddr:        0x90000000
T4A00 000:210.458     Device entry modified: STM32F777BI
T4A00 000:210.463       FlashBankInfo:
T4A00 000:210.468         Name:            QSPI Flash
T4A00 000:210.481         BaseAddr:        0x90000000
T4A00 000:210.497     Device entry modified: STM32F777II
T4A00 000:210.502       FlashBankInfo:
T4A00 000:210.507         Name:            QSPI Flash
T4A00 000:210.513         BaseAddr:        0x90000000
T4A00 000:210.527     Device entry modified: STM32F777NI
T4A00 000:210.532       FlashBankInfo:
T4A00 000:210.537         Name:            QSPI Flash
T4A00 000:210.543         BaseAddr:        0x90000000
T4A00 000:210.557     Device entry modified: STM32F777VI
T4A00 000:210.562       FlashBankInfo:
T4A00 000:210.568         Name:            QSPI Flash
T4A00 000:210.573         BaseAddr:        0x90000000
T4A00 000:210.587     Device entry modified: STM32F777ZI
T4A00 000:210.592       FlashBankInfo:
T4A00 000:210.598         Name:            QSPI Flash
T4A00 000:210.603         BaseAddr:        0x90000000
T4A00 000:210.617     Device entry modified: STM32F778AI
T4A00 000:210.622       FlashBankInfo:
T4A00 000:210.628         Name:            QSPI Flash
T4A00 000:210.633         BaseAddr:        0x90000000
T4A00 000:210.648     Device entry modified: STM32F779AI
T4A00 000:210.652       FlashBankInfo:
T4A00 000:210.658         Name:            QSPI Flash
T4A00 000:210.663         BaseAddr:        0x90000000
T4A00 000:210.678     Device entry modified: STM32F779BI
T4A00 000:210.683       FlashBankInfo:
T4A00 000:210.688         Name:            QSPI Flash
T4A00 000:210.694         BaseAddr:        0x90000000
T4A00 000:210.708     Device entry modified: STM32F779II
T4A00 000:210.713       FlashBankInfo:
T4A00 000:210.719         Name:            QSPI Flash
T4A00 000:210.724         BaseAddr:        0x90000000
T4A00 000:210.738     Device entry modified: STM32F779NI
T4A00 000:210.743       FlashBankInfo:
T4A00 000:210.749         Name:            QSPI Flash
T4A00 000:210.755         BaseAddr:        0x90000000
T4A00 000:210.770     Device entry modified: STM32H743BI
T4A00 000:210.778       ChipInfo:
T4A00 000:210.784         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:210.792     Device entry modified: STM32H743II
T4A00 000:210.800       ChipInfo:
T4A00 000:210.806         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:210.812     Device entry modified: STM32H743VI
T4A00 000:210.820       ChipInfo:
T4A00 000:210.826         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:210.833     Device entry modified: STM32H743XI
T4A00 000:210.841       ChipInfo:
T4A00 000:210.846         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:210.853     Device entry modified: STM32H743ZI
T4A00 000:210.861       ChipInfo:
T4A00 000:210.867         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:210.874     Device entry modified: STM32H753BI
T4A00 000:210.882       ChipInfo:
T4A00 000:210.888         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:210.894     Device entry modified: STM32H753II
T4A00 000:210.902       ChipInfo:
T4A00 000:210.908         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:210.915     Device entry modified: STM32H753VI
T4A00 000:210.923       ChipInfo:
T4A00 000:210.929         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:210.935     Device entry modified: STM32H753XI
T4A00 000:210.943       ChipInfo:
T4A00 000:210.949         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:210.956     Device entry modified: STM32H753ZI
T4A00 000:210.964       ChipInfo:
T4A00 000:210.970         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ST\STM32H7\ST_STM32H7xx.pex
T4A00 000:210.976     Device entry modified: Z32F0642
T4A00 000:210.981       FlashBankInfo:
T4A00 000:210.986         Name:            Internal Flash
T4A00 000:211.011         AlwaysPresent:   1
T4A00 000:211.019     Device entry modified: CM40z_128_512
T4A00 000:211.027       ChipInfo:
T4A00 000:211.033         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM40\Analog_CM40x.pex
T4A00 000:211.040     Device entry modified: CM40z_128_256
T4A00 000:211.047       ChipInfo:
T4A00 000:211.053         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM40\Analog_CM40x.pex
T4A00 000:211.060     Device entry modified: CM40z_384_2048
T4A00 000:211.067       ChipInfo:
T4A00 000:211.073         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM40\Analog_CM40x.pex
T4A00 000:211.080     Device entry modified: CM40z_128_512
T4A00 000:211.088     Device entry modified: CM40z_128_256
T4A00 000:211.096     Device entry modified: CM40z_384_2048
T4A00 000:211.104     Device entry modified: CM40z_384_2048
T4A00 000:211.113     Device entry modified: CM40z_128_1024
T4A00 000:211.120       ChipInfo:
T4A00 000:211.126         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM40\Analog_CM40x.pex
T4A00 000:211.132     Device entry modified: CM40z_384_2048
T4A00 000:211.141     Device entry modified: CM40z_384_2048
T4A00 000:211.149     Device entry modified: CM40z_384_2048
T4A00 000:211.157     Device entry modified: CM40z_384_2048
T4A00 000:211.166     Device entry modified: ADSP-CM416CSWZ-BF_M0
T4A00 000:211.174       ChipInfo:
T4A00 000:211.180         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:211.186     Device entry modified: ADSP-CM416CSWZ-CF_M0
T4A00 000:211.194       ChipInfo:
T4A00 000:211.200         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:211.206     Device entry modified: ADSP-CM417CSWZ-CF_M0
T4A00 000:211.213       ChipInfo:
T4A00 000:211.219         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:211.226     Device entry modified: ADSP-CM417CSWZ-DF_M0
T4A00 000:211.233       ChipInfo:
T4A00 000:211.239         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:211.246     Device entry modified: ADSP-CM418CBCZ-BF_M0
T4A00 000:211.253       ChipInfo:
T4A00 000:211.259         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:211.266     Device entry modified: ADSP-CM418CBCZ-CF_M0
T4A00 000:211.274       ChipInfo:
T4A00 000:211.280         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:211.286     Device entry modified: ADSP-CM419CBCZ-CF_M0
T4A00 000:211.294       ChipInfo:
T4A00 000:211.300         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:211.306     Device entry modified: ADSP-CM419CBCZ-DF_M0
T4A00 000:211.313       ChipInfo:
T4A00 000:211.319         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M0.pex
T4A00 000:211.326     Device entry modified: ADSP-CM411CBCZ-AF_M4
T4A00 000:211.334       ChipInfo:
T4A00 000:211.339         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:211.346       FlashBankInfo:
T4A00 000:211.351         Name:            Flash Block A
T4A00 000:211.357         BaseAddr:        0x11000000
T4A00 000:211.373         AlwaysPresent:   1
T4A00 000:211.378       FlashBankInfo:
T4A00 000:211.384         Name:            Flash Block B
T4A00 000:211.390         BaseAddr:        0x11080000
T4A00 000:211.404         AlwaysPresent:   1
T4A00 000:211.412     Device entry modified: ADSP-CM411CBCZ-BF_M4
T4A00 000:211.419       ChipInfo:
T4A00 000:211.425         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:211.439       FlashBankInfo:
T4A00 000:211.445         Name:            Flash Block A
T4A00 000:211.451         BaseAddr:        0x11000000
T4A00 000:211.465         AlwaysPresent:   1
T4A00 000:211.471       FlashBankInfo:
T4A00 000:211.477         Name:            Flash Block B
T4A00 000:211.483         BaseAddr:        0x11080000
T4A00 000:211.496         AlwaysPresent:   1
T4A00 000:211.503     Device entry modified: ADSP-CM412CSWZ-AF_M4
T4A00 000:211.510       ChipInfo:
T4A00 000:211.516         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:211.523       FlashBankInfo:
T4A00 000:211.528         Name:            Flash Block A
T4A00 000:211.534         BaseAddr:        0x11000000
T4A00 000:211.548         AlwaysPresent:   1
T4A00 000:211.553       FlashBankInfo:
T4A00 000:211.559         Name:            Flash Block B
T4A00 000:211.565         BaseAddr:        0x11080000
T4A00 000:211.578         AlwaysPresent:   1
T4A00 000:211.585     Device entry modified: ADSP-CM412CBCZ-BF_M4
T4A00 000:211.593       ChipInfo:
T4A00 000:211.599         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:211.605       FlashBankInfo:
T4A00 000:211.611         Name:            Flash Block A
T4A00 000:211.616         BaseAddr:        0x11000000
T4A00 000:211.630         AlwaysPresent:   1
T4A00 000:211.636       FlashBankInfo:
T4A00 000:211.641         Name:            Flash Block B
T4A00 000:211.647         BaseAddr:        0x11080000
T4A00 000:211.660         AlwaysPresent:   1
T4A00 000:211.667     Device entry modified: ADSP-CM413CSWZ-BF_M4
T4A00 000:211.674       ChipInfo:
T4A00 000:211.680         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:211.687       FlashBankInfo:
T4A00 000:211.692         Name:            Flash Block A
T4A00 000:211.698         BaseAddr:        0x11000000
T4A00 000:211.711         AlwaysPresent:   1
T4A00 000:211.717       FlashBankInfo:
T4A00 000:211.723         Name:            Flash Block B
T4A00 000:211.728         BaseAddr:        0x11080000
T4A00 000:211.742         AlwaysPresent:   1
T4A00 000:211.749     Device entry modified: ADSP-CM413CSWZ-CF_M4
T4A00 000:211.756       ChipInfo:
T4A00 000:211.762         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:211.769       FlashBankInfo:
T4A00 000:211.774         Name:            Flash Block A
T4A00 000:211.780         BaseAddr:        0x11000000
T4A00 000:211.794         AlwaysPresent:   1
T4A00 000:211.799       FlashBankInfo:
T4A00 000:211.805         Name:            Flash Block B
T4A00 000:211.810         BaseAddr:        0x11080000
T4A00 000:211.824         AlwaysPresent:   1
T4A00 000:211.831     Device entry modified: ADSP-CM416CSWZ-BF_M4
T4A00 000:211.839       ChipInfo:
T4A00 000:211.845         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:211.851       FlashBankInfo:
T4A00 000:211.856         Name:            Flash Block A
T4A00 000:211.862         BaseAddr:        0x11000000
T4A00 000:211.876         AlwaysPresent:   1
T4A00 000:211.881       FlashBankInfo:
T4A00 000:211.887         Name:            Flash Block B
T4A00 000:211.892         BaseAddr:        0x11080000
T4A00 000:211.906         AlwaysPresent:   1
T4A00 000:211.913     Device entry modified: ADSP-CM416CSWZ-CF_M4
T4A00 000:211.921       ChipInfo:
T4A00 000:211.927         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:211.933       FlashBankInfo:
T4A00 000:211.939         Name:            Flash Block A
T4A00 000:211.944         BaseAddr:        0x11000000
T4A00 000:211.959         AlwaysPresent:   1
T4A00 000:211.965       FlashBankInfo:
T4A00 000:211.978         Name:            Flash Block B
T4A00 000:211.984         BaseAddr:        0x11080000
T4A00 000:211.998         AlwaysPresent:   1
T4A00 000:212.005     Device entry modified: ADSP-CM417CSWZ-CF_M4
T4A00 000:212.012       ChipInfo:
T4A00 000:212.019         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:212.025       FlashBankInfo:
T4A00 000:212.031         Name:            Flash Block A
T4A00 000:212.036         BaseAddr:        0x11000000
T4A00 000:212.049         AlwaysPresent:   1
T4A00 000:212.055       FlashBankInfo:
T4A00 000:212.061         Name:            Flash Block B
T4A00 000:212.066         BaseAddr:        0x11080000
T4A00 000:212.080         AlwaysPresent:   1
T4A00 000:212.087     Device entry modified: ADSP-CM417CSWZ-DF_M4
T4A00 000:212.094       ChipInfo:
T4A00 000:212.100         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:212.106       FlashBankInfo:
T4A00 000:212.112         Name:            Flash Block A
T4A00 000:212.118         BaseAddr:        0x11000000
T4A00 000:212.131         AlwaysPresent:   1
T4A00 000:212.137       FlashBankInfo:
T4A00 000:212.142         Name:            Flash Block B
T4A00 000:212.148         BaseAddr:        0x11080000
T4A00 000:212.162         AlwaysPresent:   1
T4A00 000:212.169     Device entry modified: ADSP-CM418CBCZ-BF_M4
T4A00 000:212.176       ChipInfo:
T4A00 000:212.183         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:212.189       FlashBankInfo:
T4A00 000:212.195         Name:            Flash Block A
T4A00 000:212.200         BaseAddr:        0x11000000
T4A00 000:212.214         AlwaysPresent:   1
T4A00 000:212.219       FlashBankInfo:
T4A00 000:212.225         Name:            Flash Block B
T4A00 000:212.230         BaseAddr:        0x11080000
T4A00 000:212.244         AlwaysPresent:   1
T4A00 000:212.251     Device entry modified: ADSP-CM418CBCZ-CF_M4
T4A00 000:212.259       ChipInfo:
T4A00 000:212.265         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:212.271       FlashBankInfo:
T4A00 000:212.277         Name:            Flash Block A
T4A00 000:212.282         BaseAddr:        0x11000000
T4A00 000:212.296         AlwaysPresent:   1
T4A00 000:212.301       FlashBankInfo:
T4A00 000:212.307         Name:            Flash Block B
T4A00 000:212.312         BaseAddr:        0x11080000
T4A00 000:212.326         AlwaysPresent:   1
T4A00 000:212.333     Device entry modified: ADSP-CM419CBCZ-CF_M4
T4A00 000:212.340       ChipInfo:
T4A00 000:212.347         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:212.353       FlashBankInfo:
T4A00 000:212.358         Name:            Flash Block A
T4A00 000:212.364         BaseAddr:        0x11000000
T4A00 000:212.377         AlwaysPresent:   1
T4A00 000:212.383       FlashBankInfo:
T4A00 000:212.389         Name:            Flash Block B
T4A00 000:212.394         BaseAddr:        0x11080000
T4A00 000:212.408         AlwaysPresent:   1
T4A00 000:212.414     Device entry modified: ADSP-CM419CBCZ-DF_M4
T4A00 000:212.422       ChipInfo:
T4A00 000:212.428         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\AnalogDevices\ADSP-CM41\Analog_CM41x_M4.pex
T4A00 000:212.434       FlashBankInfo:
T4A00 000:212.440         Name:            Flash Block A
T4A00 000:212.445         BaseAddr:        0x11000000
T4A00 000:212.459         AlwaysPresent:   1
T4A00 000:212.464       FlashBankInfo:
T4A00 000:212.470         Name:            Flash Block B
T4A00 000:212.475         BaseAddr:        0x11080000
T4A00 000:212.489         AlwaysPresent:   1
T4A00 000:212.496     Device entry modified: ADuCM4050
T4A00 000:212.501       FlashBankInfo:
T4A00 000:212.506         Name:            Flash Block
T4A00 000:212.531         AlwaysPresent:   1
T4A00 000:212.539     Device entry modified: MAX32600
T4A00 000:212.543       FlashBankInfo:
T4A00 000:212.549         Name:            Internal Flash
T4A00 000:212.563         AlwaysPresent:   1
T4A00 000:212.571     Device entry modified: ATSAML10D14A
T4A00 000:212.579       ChipInfo:
T4A00 000:212.591         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.597     Device entry modified: ATSAML10D15A
T4A00 000:212.605       ChipInfo:
T4A00 000:212.611         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.617     Device entry modified: ATSAML10D16A
T4A00 000:212.625       ChipInfo:
T4A00 000:212.631         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.637     Device entry modified: ATSAML10E14A
T4A00 000:212.645       ChipInfo:
T4A00 000:212.651         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.657     Device entry modified: ATSAML10E15A
T4A00 000:212.664       ChipInfo:
T4A00 000:212.670         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.677     Device entry modified: ATSAML10E16A
T4A00 000:212.684       ChipInfo:
T4A00 000:212.690         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.697     Device entry modified: ATSAML11D14A
T4A00 000:212.704       ChipInfo:
T4A00 000:212.711         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.717     Device entry modified: ATSAML11D15A
T4A00 000:212.724       ChipInfo:
T4A00 000:212.730         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.737     Device entry modified: ATSAML11D16A
T4A00 000:212.745       ChipInfo:
T4A00 000:212.751         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.757     Device entry modified: ATSAML11E14A
T4A00 000:212.764       ChipInfo:
T4A00 000:212.770         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.777     Device entry modified: ATSAML11E15A
T4A00 000:212.784       ChipInfo:
T4A00 000:212.790         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.797     Device entry modified: ATSAML11E16A
T4A00 000:212.805       ChipInfo:
T4A00 000:212.811         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\ATMEL\SAML1x\Atmel_SAML1x.pex
T4A00 000:212.817     Device entry modified: ARTIK05X
T4A00 000:212.826       ChipInfo:
T4A00 000:212.832         Script:          d:\Program Files\SEGGER\JLink_V796a\Devices\Samsung\ARTIK05X.JLinkScript
T4A00 000:212.838     Device entry modified: HC32L176
T4A00 000:212.843       FlashBankInfo:
T4A00 000:212.848         Name:            Flash_128K
T4A00 000:212.864         AlwaysPresent:   1
T4A00 000:212.870         LoaderInfo:
T4A00 000:212.876           Name:            Flash_128K
T4A00 000:212.882           MaxSize:         0x00020000
T4A00 000:212.887           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32L17X_128K.FLM
T4A00 000:212.893           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:212.900     Device entry modified: HC32L136
T4A00 000:212.904       FlashBankInfo:
T4A00 000:212.910         Name:            Flash_64K
T4A00 000:212.924         AlwaysPresent:   1
T4A00 000:212.929         LoaderInfo:
T4A00 000:212.935           Name:            Flash_64K
T4A00 000:212.940           MaxSize:         0x00010000
T4A00 000:212.946           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32L13X_64K.FLM
T4A00 000:212.952           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:212.958     Device entry modified: HC32L130
T4A00 000:212.971       FlashBankInfo:
T4A00 000:212.977         Name:            Flash_64K
T4A00 000:212.990         AlwaysPresent:   1
T4A00 000:212.996         LoaderInfo:
T4A00 000:213.001           Name:            Flash_64K
T4A00 000:213.007           MaxSize:         0x00010000
T4A00 000:213.013           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32L13X_64K.FLM
T4A00 000:213.018           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.025     Device entry modified: HC32F030
T4A00 000:213.029       FlashBankInfo:
T4A00 000:213.035         Name:            Flash_64K
T4A00 000:213.048         AlwaysPresent:   1
T4A00 000:213.054         LoaderInfo:
T4A00 000:213.059           Name:            Flash_64K
T4A00 000:213.065           MaxSize:         0x00010000
T4A00 000:213.071           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32F030_64K.FLM
T4A00 000:213.076           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.083     Device entry modified: HC32L110x4
T4A00 000:213.088       FlashBankInfo:
T4A00 000:213.093         Name:            Flash_16K
T4A00 000:213.107         AlwaysPresent:   1
T4A00 000:213.113         LoaderInfo:
T4A00 000:213.118           Name:            Flash_16K
T4A00 000:213.124           MaxSize:         0x00004000
T4A00 000:213.130           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32L110_16K.FLM
T4A00 000:213.135           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.141     Device entry modified: HC32L110x6
T4A00 000:213.146       FlashBankInfo:
T4A00 000:213.151         Name:            Flash_32K
T4A00 000:213.165         AlwaysPresent:   1
T4A00 000:213.171         LoaderInfo:
T4A00 000:213.177           Name:            Flash_32K
T4A00 000:213.182           MaxSize:         0x00008000
T4A00 000:213.188           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32L110_32K.FLM
T4A00 000:213.194           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.200     Device entry modified: HC32F003
T4A00 000:213.204       FlashBankInfo:
T4A00 000:213.210         Name:            Flash_16K
T4A00 000:213.223         AlwaysPresent:   1
T4A00 000:213.229         LoaderInfo:
T4A00 000:213.234           Name:            Flash_16K
T4A00 000:213.240           MaxSize:         0x00004000
T4A00 000:213.246           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32F003_16K.FLM
T4A00 000:213.251           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.258     Device entry modified: HC32F005
T4A00 000:213.262       FlashBankInfo:
T4A00 000:213.267         Name:            Flash_32K
T4A00 000:213.281         AlwaysPresent:   1
T4A00 000:213.287         LoaderInfo:
T4A00 000:213.292           Name:            Flash_32K
T4A00 000:213.298           MaxSize:         0x00008000
T4A00 000:213.304           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32F005_32K.FLM
T4A00 000:213.309           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.316     Device entry modified: HC32L15
T4A00 000:213.320       FlashBankInfo:
T4A00 000:213.326         Name:            Flash_128K
T4A00 000:213.341         AlwaysPresent:   1
T4A00 000:213.347         LoaderInfo:
T4A00 000:213.352           Name:            Flash_128K
T4A00 000:213.358           MaxSize:         0x00020000
T4A00 000:213.363           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32L15.FLM
T4A00 000:213.369           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.376     Device entry modified: HC32F_M14
T4A00 000:213.380       FlashBankInfo:
T4A00 000:213.385         Name:            Flash_128K
T4A00 000:213.399         AlwaysPresent:   1
T4A00 000:213.405         LoaderInfo:
T4A00 000:213.411           Name:            Flash_128K
T4A00 000:213.416           MaxSize:         0x00020000
T4A00 000:213.422           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F_M14.FLM
T4A00 000:213.439           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.446     Device entry modified: HC32L19x
T4A00 000:213.450       FlashBankInfo:
T4A00 000:213.456         Name:            Flash_256K
T4A00 000:213.470         AlwaysPresent:   1
T4A00 000:213.476         LoaderInfo:
T4A00 000:213.481           Name:            Flash_256K
T4A00 000:213.487           MaxSize:         0x00040000
T4A00 000:213.493           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32L19X_256K.FLM
T4A00 000:213.498           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.504     Device entry modified: HC32F19x
T4A00 000:213.509       FlashBankInfo:
T4A00 000:213.514         Name:            Flash_256K
T4A00 000:213.527         AlwaysPresent:   1
T4A00 000:213.533         LoaderInfo:
T4A00 000:213.539           Name:            Flash_256K
T4A00 000:213.544           MaxSize:         0x00040000
T4A00 000:213.550           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32F19X_256K.FLM
T4A00 000:213.556           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.562     Device entry modified: HC32F17x
T4A00 000:213.566       FlashBankInfo:
T4A00 000:213.572         Name:            Flash_128K
T4A00 000:213.585         AlwaysPresent:   1
T4A00 000:213.591         LoaderInfo:
T4A00 000:213.596           Name:            Flash_128K
T4A00 000:213.602           MaxSize:         0x00020000
T4A00 000:213.607           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32F17X_128K.FLM
T4A00 000:213.613           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.620     Device entry modified: HC32L17x
T4A00 000:213.624       FlashBankInfo:
T4A00 000:213.629         Name:            Flash_128K
T4A00 000:213.642         AlwaysPresent:   1
T4A00 000:213.647         LoaderInfo:
T4A00 000:213.653           Name:            Flash_128K
T4A00 000:213.658           MaxSize:         0x00020000
T4A00 000:213.664           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32L17X_128K.FLM
T4A00 000:213.670           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.676     Device entry modified: HC32F072
T4A00 000:213.680       FlashBankInfo:
T4A00 000:213.685         Name:            Flash_128K
T4A00 000:213.699         AlwaysPresent:   1
T4A00 000:213.705         LoaderInfo:
T4A00 000:213.710           Name:            Flash_128K
T4A00 000:213.716           MaxSize:         0x00020000
T4A00 000:213.722           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32F072_128K.FLM
T4A00 000:213.728           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.734     Device entry modified: HC32L07X
T4A00 000:213.738       FlashBankInfo:
T4A00 000:213.744         Name:            Flash_128K
T4A00 000:213.758         AlwaysPresent:   1
T4A00 000:213.764         LoaderInfo:
T4A00 000:213.769           Name:            Flash_128K
T4A00 000:213.775           MaxSize:         0x00020000
T4A00 000:213.781           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\FlashHC32L07X_128K.FLM
T4A00 000:213.786           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.793     Device entry modified: HC32F120x6
T4A00 000:213.797       FlashBankInfo:
T4A00 000:213.802         Name:            Flash_32K
T4A00 000:213.817         AlwaysPresent:   1
T4A00 000:213.822         LoaderInfo:
T4A00 000:213.828           Name:            Flash_32K
T4A00 000:213.833           MaxSize:         0x00008000
T4A00 000:213.839           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F120_32K.FLM
T4A00 000:213.845           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.851     Device entry modified: HC32F120x8
T4A00 000:213.855       FlashBankInfo:
T4A00 000:213.861         Name:            Flash_64K
T4A00 000:213.874         AlwaysPresent:   1
T4A00 000:213.880         LoaderInfo:
T4A00 000:213.886           Name:            Flash_64K
T4A00 000:213.892           MaxSize:         0x00010000
T4A00 000:213.898           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F120_64K.FLM
T4A00 000:213.904           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.911     Device entry modified: HC32F160xA
T4A00 000:213.915       FlashBankInfo:
T4A00 000:213.920         Name:            Flash_128K
T4A00 000:213.934         AlwaysPresent:   1
T4A00 000:213.940         LoaderInfo:
T4A00 000:213.945           Name:            Flash_128K
T4A00 000:213.951           MaxSize:         0x00020000
T4A00 000:213.956           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F160_128K.FLM
T4A00 000:213.962           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:213.968       FlashBankInfo:
T4A00 000:213.973         Name:            Flash_1K
T4A00 000:213.979         BaseAddr:        0x01000800
T4A00 000:213.992         AlwaysPresent:   1
T4A00 000:213.998         LoaderInfo:
T4A00 000:214.003           Name:            Flash_1K
T4A00 000:214.009           MaxSize:         0x00000400
T4A00 000:214.015           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F160_1K.FLM
T4A00 000:214.021           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.027     Device entry modified: HC32F160xC
T4A00 000:214.031       FlashBankInfo:
T4A00 000:214.037         Name:            Flash_256K
T4A00 000:214.050         AlwaysPresent:   1
T4A00 000:214.056         LoaderInfo:
T4A00 000:214.061           Name:            Flash_256K
T4A00 000:214.067           MaxSize:         0x00040000
T4A00 000:214.073           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F160_256K.FLM
T4A00 000:214.078           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.084       FlashBankInfo:
T4A00 000:214.089         Name:            Flash_1K
T4A00 000:214.095         BaseAddr:        0x01000800
T4A00 000:214.107         AlwaysPresent:   1
T4A00 000:214.112         LoaderInfo:
T4A00 000:214.118           Name:            Flash_1K
T4A00 000:214.123           MaxSize:         0x00000400
T4A00 000:214.129           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F160_1K.FLM
T4A00 000:214.135           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.142     Device entry modified: HC32F460xC
T4A00 000:214.147       FlashBankInfo:
T4A00 000:214.153         Name:            Flash_256K
T4A00 000:214.166         AlwaysPresent:   1
T4A00 000:214.172         LoaderInfo:
T4A00 000:214.177           Name:            Flash_256K
T4A00 000:214.183           MaxSize:         0x00040000
T4A00 000:214.189           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F460_256K.FLM
T4A00 000:214.194           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.201     Device entry modified: HC32F460xE
T4A00 000:214.206       FlashBankInfo:
T4A00 000:214.211         Name:            Flash_512K
T4A00 000:214.224         AlwaysPresent:   1
T4A00 000:214.230         LoaderInfo:
T4A00 000:214.236           Name:            Flash_512K
T4A00 000:214.241           MaxSize:         0x00080000
T4A00 000:214.247           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F460_512K.FLM
T4A00 000:214.253           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.259     Device entry modified: HC32F4A0xG
T4A00 000:214.264       FlashBankInfo:
T4A00 000:214.269         Name:            Flash_1M
T4A00 000:214.282         AlwaysPresent:   1
T4A00 000:214.288         LoaderInfo:
T4A00 000:214.294           Name:            Flash_1M
T4A00 000:214.299           MaxSize:         0x00100000
T4A00 000:214.305           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F4A0_1M.FLM
T4A00 000:214.311           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.317     Device entry modified: HC32F4A0xI
T4A00 000:214.322       FlashBankInfo:
T4A00 000:214.327         Name:            Flash_2M
T4A00 000:214.341         AlwaysPresent:   1
T4A00 000:214.347         LoaderInfo:
T4A00 000:214.353           Name:            Flash_2M
T4A00 000:214.358           MaxSize:         0x00200000
T4A00 000:214.364           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F4A0_2M.FLM
T4A00 000:214.370           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.376     Device entry modified: HC32M120x6
T4A00 000:214.381       FlashBankInfo:
T4A00 000:214.386         Name:            Flash_32K
T4A00 000:214.400         AlwaysPresent:   1
T4A00 000:214.406         LoaderInfo:
T4A00 000:214.412           Name:            Flash_32K
T4A00 000:214.417           MaxSize:         0x00008000
T4A00 000:214.423           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32M120_32K.FLM
T4A00 000:214.429           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.436     Device entry modified: HC32M423xA
T4A00 000:214.441       FlashBankInfo:
T4A00 000:214.446         Name:            Flash_128K
T4A00 000:214.533         AlwaysPresent:   1
T4A00 000:214.584         LoaderInfo:
T4A00 000:214.594           Name:            Flash_128K
T4A00 000:214.602           MaxSize:         0x00020000
T4A00 000:214.611           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32M423_128K.FLM
T4A00 000:214.618           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.627     Device entry modified: HC32L18xC
T4A00 000:214.633       FlashBankInfo:
T4A00 000:214.638         Name:            Flash_256K
T4A00 000:214.667         AlwaysPresent:   1
T4A00 000:214.673         LoaderInfo:
T4A00 000:214.678           Name:            Flash_256K
T4A00 000:214.684           MaxSize:         0x00040000
T4A00 000:214.690           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32L18x_256KB.FLM
T4A00 000:214.695           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.702     Device entry modified: HC32L18xA
T4A00 000:214.707       FlashBankInfo:
T4A00 000:214.712         Name:            Flash_128K
T4A00 000:214.727         AlwaysPresent:   1
T4A00 000:214.733         LoaderInfo:
T4A00 000:214.738           Name:            Flash_128K
T4A00 000:214.744           MaxSize:         0x00020000
T4A00 000:214.750           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32L18x_128KB.FLM
T4A00 000:214.755           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.763     Device entry modified: HC32F420xA
T4A00 000:214.768       FlashBankInfo:
T4A00 000:214.773         Name:            Flash_128K
T4A00 000:214.787         AlwaysPresent:   1
T4A00 000:214.793         LoaderInfo:
T4A00 000:214.798           Name:            Flash_128K
T4A00 000:214.804           MaxSize:         0x00020000
T4A00 000:214.810           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\HDSC\HC32F420_128KB.FLM
T4A00 000:214.815           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.823     Device entry created:  SPC1158
T4A00 000:214.827       ChipInfo:
T4A00 000:214.832         Vendor:          Spintrol
T4A00 000:214.838         Name:            SPC1158
T4A00 000:214.844         WorkRAMAddr:     0x20000000
T4A00 000:214.849         WorkRAMSize:     0x00004000
T4A00 000:214.856         Core:            JLINK_CORE_CORTEX_M4
T4A00 000:214.862       FlashBankInfo:
T4A00 000:214.867         Name:            Internal Flash(NVR)
T4A00 000:214.873         BaseAddr:        0x11000400
T4A00 000:214.888         AlwaysPresent:   1
T4A00 000:214.894         LoaderInfo:
T4A00 000:214.899           Name:            Internal_Flash(NVR)
T4A00 000:214.905           MaxSize:         0x00000400
T4A00 000:214.911           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\Spintrol\SPC1168_NVR.FLM
T4A00 000:214.916           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:214.922       FlashBankInfo:
T4A00 000:214.928         Name:            Internal Flash(Main)
T4A00 000:214.933         BaseAddr:        0x10000000
T4A00 000:214.949         AlwaysPresent:   1
T4A00 000:214.954         LoaderInfo:
T4A00 000:214.960           Name:            Internal_Flash(Main)
T4A00 000:214.966           MaxSize:         0x00010000
T4A00 000:214.978           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\Spintrol\SPC1168.FLM
T4A00 000:214.984           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:215.023     Device entry created:  SPC1168
T4A00 000:215.026       ChipInfo:
T4A00 000:215.032         Vendor:          Spintrol
T4A00 000:215.038         Name:            SPC1168
T4A00 000:215.043         WorkRAMAddr:     0x20000000
T4A00 000:215.049         WorkRAMSize:     0x00004000
T4A00 000:215.055         Core:            JLINK_CORE_CORTEX_M4
T4A00 000:215.061       FlashBankInfo:
T4A00 000:215.066         Name:            Internal Flash(NVR)
T4A00 000:215.072         BaseAddr:        0x11000400
T4A00 000:215.085         AlwaysPresent:   1
T4A00 000:215.091         LoaderInfo:
T4A00 000:215.096           Name:            Internal_Flash(NVR)
T4A00 000:215.102           MaxSize:         0x00000400
T4A00 000:215.108           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\Spintrol\SPC1168_NVR.FLM
T4A00 000:215.113           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:215.119       FlashBankInfo:
T4A00 000:215.124         Name:            Internal Flash(Main)
T4A00 000:215.130         BaseAddr:        0x10000000
T4A00 000:215.142         AlwaysPresent:   1
T4A00 000:215.148         LoaderInfo:
T4A00 000:215.153           Name:            Internal_Flash(Main)
T4A00 000:215.159           MaxSize:         0x00020000
T4A00 000:215.165           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\Spintrol\SPC1168.FLM
T4A00 000:215.171           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:215.178     Device entry created:  SPC2168
T4A00 000:215.182       ChipInfo:
T4A00 000:215.187         Vendor:          Spintrol
T4A00 000:215.193         Name:            SPC2168
T4A00 000:215.198         WorkRAMAddr:     0x20000000
T4A00 000:215.204         WorkRAMSize:     0x00004000
T4A00 000:215.210         Core:            JLINK_CORE_CORTEX_M4
T4A00 000:215.216       FlashBankInfo:
T4A00 000:215.221         Name:            Internal Flash(NVR)
T4A00 000:215.227         BaseAddr:        0x11000400
T4A00 000:215.240         AlwaysPresent:   1
T4A00 000:215.246         LoaderInfo:
T4A00 000:215.251           Name:            Internal_Flash(NVR)
T4A00 000:215.257           MaxSize:         0x00000400
T4A00 000:215.262           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\Spintrol\SPC2168_NVR.FLM
T4A00 000:215.268           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:215.273       FlashBankInfo:
T4A00 000:215.279         Name:            Internal Flash(Main)
T4A00 000:215.285         BaseAddr:        0x10000000
T4A00 000:215.298         AlwaysPresent:   1
T4A00 000:215.303         LoaderInfo:
T4A00 000:215.309           Name:            Internal_Flash(Main)
T4A00 000:215.314           MaxSize:         0x00080000
T4A00 000:215.320           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\Spintrol\SPC2168.FLM
T4A00 000:215.326           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:215.333     Device entry created:  SPC2166
T4A00 000:215.337       ChipInfo:
T4A00 000:215.343         Vendor:          Spintrol
T4A00 000:215.348         Name:            SPC2166
T4A00 000:215.354         WorkRAMAddr:     0x20000000
T4A00 000:215.359         WorkRAMSize:     0x00004000
T4A00 000:215.366         Core:            JLINK_CORE_CORTEX_M4
T4A00 000:215.371       FlashBankInfo:
T4A00 000:215.377         Name:            Internal Flash(NVR)
T4A00 000:215.382         BaseAddr:        0x11000400
T4A00 000:215.394         AlwaysPresent:   1
T4A00 000:215.400         LoaderInfo:
T4A00 000:215.405           Name:            Internal_Flash(NVR)
T4A00 000:215.411           MaxSize:         0x00000400
T4A00 000:215.417           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\Spintrol\SPC2168_NVR.FLM
T4A00 000:215.423           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:215.429       FlashBankInfo:
T4A00 000:215.434         Name:            Internal Flash(Main)
T4A00 000:215.440         BaseAddr:        0x10000000
T4A00 000:215.452         AlwaysPresent:   1
T4A00 000:215.458         LoaderInfo:
T4A00 000:215.463           Name:            Internal_Flash(Main)
T4A00 000:215.469           MaxSize:         0x00040000
T4A00 000:215.475           Loader:          d:\Program Files\SEGGER\JLink_V796a\Devices\Spintrol\SPC2168.FLM
T4A00 000:215.480           LoaderType:      FLASH_ALGO_TYPE_OPEN
T4A00 000:220.120   Device "CORTEX-M0+" selected.
T4A00 000:220.475 - 84.543ms returns 0x00
T4A00 000:222.371 JLINK_ExecCommand("Device = HC32L186KATH", ...). 
T4A00 000:224.229   Device "CORTEX-M0+" selected.
T4A00 000:224.539 - 2.155ms returns 0x00
T4A00 000:224.550 JLINK_ExecCommand("DisableConnectionTimeout", ...). 
T4A00 000:224.557 - 0.001ms returns 0x01
T4A00 000:224.565 JLINK_GetHardwareVersion()
T4A00 000:224.570 - 0.006ms returns 110000
T4A00 000:224.575 JLINK_GetDLLVersion()
T4A00 000:224.578 - 0.003ms returns 79601
T4A00 000:224.583 JLINK_GetOEMString(...)
T4A00 000:224.588 JLINK_GetFirmwareString(...)
T4A00 000:224.592 - 0.003ms
T4A00 000:229.644 JLINK_GetDLLVersion()
T4A00 000:229.656 - 0.011ms returns 79601
T4A00 000:229.661 JLINK_GetCompileDateTime()
T4A00 000:229.664 - 0.003ms
T4A00 000:231.991 JLINK_GetFirmwareString(...)
T4A00 000:232.006 - 0.015ms
T4A00 000:233.653 JLINK_GetHardwareVersion()
T4A00 000:233.666 - 0.013ms returns 110000
T4A00 000:235.231 JLINK_GetSN()
T4A00 000:235.243 - 0.012ms returns 601012352
T4A00 000:236.809 JLINK_GetOEMString(...)
T4A00 000:239.604 JLINK_TIF_Select(JLINKARM_TIF_SWD)
T4A00 000:250.270 - 10.665ms returns 0x00
T4A00 000:250.298 JLINK_HasError()
T4A00 000:250.323 JLINK_SetSpeed(500)
T4A00 000:253.413 - 3.090ms
T4A00 000:253.445 JLINK_GetId()
T4A00 000:282.075   Found SW-DP with ID 0x0BC11477
T4A00 000:293.900   DPIDR: 0x0BC11477
T4A00 000:295.600   CoreSight SoC-400 or earlier
T4A00 000:297.145   Scanning AP map to find all available APs
T4A00 000:301.463   AP[1]: Stopped AP scan as end of AP map has been reached
T4A00 000:302.973   AP[0]: AHB-AP (IDR: 0x04770031)
T4A00 000:304.485   Iterating through AP map to find AHB-AP to use
T4A00 000:309.593   AP[0]: Core found
T4A00 000:311.129   AP[0]: AHB-AP ROM base: 0xE00FF000
T4A00 000:314.423   CPUID register: 0x410CC601. Implementer code: 0x41 (ARM)
T4A00 000:316.666   Found Cortex-M0 r0p1, Little endian.
T4A00 000:317.828   -- Max. mem block: 0x0000C448
T4A00 000:319.933   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T4A00 000:321.791   CPU_ReadMem(4 bytes @ 0x********)
T4A00 000:325.275   FPUnit: 4 code (BP) slots and 0 literal slots
T4A00 000:325.289   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T4A00 000:327.076   CPU_ReadMem(4 bytes @ 0x********)
T4A00 000:328.845   CPU_WriteMem(4 bytes @ 0x********)
T4A00 000:332.702   CoreSight components:
T4A00 000:334.142   ROMTbl[0] @ E00FF000
T4A00 000:334.158   CPU_ReadMem(64 bytes @ 0xE00FF000)
T4A00 000:339.239   CPU_ReadMem(32 bytes @ 0xE000EFE0)
T4A00 000:344.126   [0][0]: E000E000 CID B105E00D PID 000BB008 SCS
T4A00 000:344.143   CPU_ReadMem(32 bytes @ 0xE0001FE0)
T4A00 000:349.367   [0][1]: ******** CID B105E00D PID 000BB00A DWT
T4A00 000:349.383   CPU_ReadMem(32 bytes @ 0xE0002FE0)
T4A00 000:354.220   [0][2]: ******** CID B105E00D PID 000BB00B FPB
T4A00 000:355.150 - 101.704ms returns 0x0BC11477
T4A00 000:355.206 JLINK_GetDLLVersion()
T4A00 000:355.212 - 0.005ms returns 79601
T4A00 000:355.231 JLINK_CORE_GetFound()
T4A00 000:355.235 - 0.004ms returns 0x60000FF
T4A00 000:355.241 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T4A00 000:355.246   Value=0xE00FF000
T4A00 000:355.251 - 0.011ms returns 0
T4A00 000:356.718 JLINK_GetDebugInfo(0x100 = JLINKARM_ROM_TABLE_ADDR_INDEX)
T4A00 000:356.729   Value=0xE00FF000
T4A00 000:356.735 - 0.017ms returns 0
T4A00 000:356.743 JLINK_GetDebugInfo(0x101 = JLINKARM_DEBUG_INFO_ETM_ADDR_INDEX)
T4A00 000:356.747   Value=0x00000000
T4A00 000:356.752 - 0.009ms returns 0
T4A00 000:356.763 JLINK_ReadMemEx(0xE0041FF0, 0x10 Bytes, Flags = 0x02000004)
T4A00 000:356.795   CPU_ReadMem(16 bytes @ 0xE0041FF0)
T4A00 000:364.816   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T4A00 000:364.843 - 8.079ms returns 16 (0x10)
T4A00 000:364.880 JLINK_GetDebugInfo(0x102 = JLINKARM_DEBUG_INFO_MTB_ADDR_INDEX)
T4A00 000:364.891   Value=0x00000000
T4A00 000:364.904 - 0.023ms returns 0
T4A00 000:364.914 JLINK_GetDebugInfo(0x103 = JLINKARM_DEBUG_INFO_TPIU_ADDR_INDEX)
T4A00 000:364.923   Value=0x00000000
T4A00 000:364.935 - 0.021ms returns 0
T4A00 000:364.946 JLINK_ReadMemEx(0xE0040FF0, 0x10 Bytes, Flags = 0x02000004)
T4A00 000:364.961   CPU_ReadMem(16 bytes @ 0xE0040FF0)
T4A00 000:372.932   Data:  00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00
T4A00 000:372.952 - 8.006ms returns 16 (0x10)
T4A00 000:372.990 JLINK_GetDebugInfo(0x104 = JLINKARM_DEBUG_INFO_ITM_ADDR_INDEX)
T4A00 000:372.995   Value=0xE0000000
T4A00 000:373.000 - 0.011ms returns 0
T4A00 000:373.005 JLINK_GetDebugInfo(0x105 = JLINKARM_DEBUG_INFO_DWT_ADDR_INDEX)
T4A00 000:373.009   Value=0x********
T4A00 000:373.014 - 0.009ms returns 0
T4A00 000:373.018 JLINK_GetDebugInfo(0x106 = JLINKARM_DEBUG_INFO_FPB_ADDR_INDEX)
T4A00 000:373.022   Value=0x********
T4A00 000:373.027 - 0.009ms returns 0
T4A00 000:373.031 JLINK_GetDebugInfo(0x107 = JLINKARM_DEBUG_INFO_NVIC_ADDR_INDEX)
T4A00 000:373.035   Value=0xE000E000
T4A00 000:373.040 - 0.009ms returns 0
T4A00 000:373.044 JLINK_GetDebugInfo(0x10C = JLINKARM_DEBUG_INFO_DBG_ADDR_INDEX)
T4A00 000:373.048   Value=0xE000EDF0
T4A00 000:373.053 - 0.009ms returns 0
T4A00 000:373.058 JLINK_GetDebugInfo(0x01 = Unknown)
T4A00 000:373.062   Value=0x00000000
T4A00 000:373.067 - 0.009ms returns 0
T4A00 000:373.071 JLINK_ReadMemU32(0xE000ED00, 0x1 Items)
T4A00 000:373.082   CPU_ReadMem(4 bytes @ 0xE000ED00)
T4A00 000:380.338   Data:  01 C6 0C 41
T4A00 000:380.380   Debug reg: CPUID
T4A00 000:380.386 - 7.314ms returns 1 (0x1)
T4A00 000:380.405 JLINK_GetDebugInfo(0x10F = JLINKARM_DEBUG_INFO_HAS_CORTEX_M_SECURITY_EXT_INDEX)
T4A00 000:380.409   Value=0x00000000
T4A00 000:380.414 - 0.009ms returns 0
T4A00 000:380.425 JLINK_HasError()
T4A00 000:380.431 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T4A00 000:380.435 - 0.004ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T4A00 000:380.439 JLINK_Reset()
T4A00 000:396.193   CPU is running
T4A00 000:396.222   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T4A00 000:398.040   CPU is running
T4A00 000:398.067   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T4A00 000:403.333   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T4A00 000:406.492   Reset: Reset device via AIRCR.SYSRESETREQ.
T4A00 000:406.512   CPU is running
T4A00 000:406.522   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T4A00 000:462.853   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T4A00 000:464.649   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T4A00 000:490.532   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T4A00 000:498.399   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T4A00 000:524.411   CPU_WriteMem(4 bytes @ 0x********)
T4A00 000:526.294   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T4A00 000:528.040   CPU_ReadMem(4 bytes @ 0x********)
T4A00 000:529.830   CPU_WriteMem(4 bytes @ 0x********)
T4A00 000:531.753 - 151.313ms
T4A00 000:531.785 JLINK_Halt()
T4A00 000:531.792 - 0.006ms returns 0x00
T4A00 000:531.803 JLINK_ReadMemU32(0xE000EDF0, 0x1 Items)
T4A00 000:531.816   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T4A00 000:533.798   Data:  03 00 03 00
T4A00 000:533.823   Debug reg: DHCSR
T4A00 000:533.839 - 2.035ms returns 1 (0x1)
T4A00 000:533.864 JLINK_WriteU32_64(0xE000EDF0, 0xA05F0003)
T4A00 000:533.869   Debug reg: DHCSR
T4A00 000:534.206   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T4A00 000:759.680 - 225.815ms returns 0 (0x00000000)
T4A00 000:759.746 JLINK_WriteU32_64(0xE000EDFC, 0x01000000)
T4A00 000:759.753   Debug reg: DEMCR
T4A00 000:759.769   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T4A00 000:761.540 - 1.791ms returns 0 (0x00000000)
T4A00 000:768.358 JLINK_GetHWStatus(...)
T4A00 000:769.126 - 0.767ms returns 0
T4A00 000:774.217 JLINK_GetNumBPUnits(Type = 0xFFFFFF00)
T4A00 000:774.231 - 0.014ms returns 0x04
T4A00 000:774.236 JLINK_GetNumBPUnits(Type = 0xF0)
T4A00 000:774.240 - 0.004ms returns 0x2000
T4A00 000:774.244 JLINK_GetNumWPUnits()
T4A00 000:774.260 - 0.015ms returns 2
T4A00 000:778.445 JLINK_GetSpeed()
T4A00 000:778.456 - 0.011ms returns 500
T4A00 000:781.201 JLINK_ReadMemU32(0xE000E004, 0x1 Items)
T4A00 000:781.225   CPU_ReadMem(4 bytes @ 0xE000E004)
T4A00 000:783.002   Data:  00 00 00 00
T4A00 000:783.040 - 1.839ms returns 1 (0x1)
T4A00 000:783.084 JLINK_Halt()
T4A00 000:783.089 - 0.004ms returns 0x00
T4A00 000:783.093 JLINK_IsHalted()
T4A00 000:783.097 - 0.004ms returns TRUE
T4A00 000:785.385 JLINK_WriteMem(0x20000000, 0x460 Bytes, ...)
T4A00 000:785.392   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T4A00 000:785.653   CPU_WriteMem(1120 bytes @ 0x20000000)
T4A00 000:844.824 - 59.437ms returns 0x460
T4A00 000:844.919 JLINK_HasError()
T4A00 000:844.942 JLINK_WriteReg(R0, 0x00000000)
T4A00 000:844.953 - 0.023ms returns 0
T4A00 000:844.959 JLINK_WriteReg(R1, 0x00B71B00)
T4A00 000:844.965 - 0.005ms returns 0
T4A00 000:844.971 JLINK_WriteReg(R2, 0x00000001)
T4A00 000:844.976 - 0.005ms returns 0
T4A00 000:844.990 JLINK_WriteReg(R3, 0x00000000)
T4A00 000:844.995 - 0.005ms returns 0
T4A00 000:845.001 JLINK_WriteReg(R4, 0x00000000)
T4A00 000:845.006 - 0.005ms returns 0
T4A00 000:845.012 JLINK_WriteReg(R5, 0x00000000)
T4A00 000:845.017 - 0.005ms returns 0
T4A00 000:845.023 JLINK_WriteReg(R6, 0x00000000)
T4A00 000:845.028 - 0.005ms returns 0
T4A00 000:845.033 JLINK_WriteReg(R7, 0x00000000)
T4A00 000:845.038 - 0.005ms returns 0
T4A00 000:845.044 JLINK_WriteReg(R8, 0x00000000)
T4A00 000:845.049 - 0.005ms returns 0
T4A00 000:845.055 JLINK_WriteReg(R9, 0x2000045C)
T4A00 000:845.060 - 0.005ms returns 0
T4A00 000:845.066 JLINK_WriteReg(R10, 0x00000000)
T4A00 000:845.071 - 0.005ms returns 0
T4A00 000:845.077 JLINK_WriteReg(R11, 0x00000000)
T4A00 000:845.082 - 0.005ms returns 0
T4A00 000:845.088 JLINK_WriteReg(R12, 0x00000000)
T4A00 000:845.093 - 0.005ms returns 0
T4A00 000:845.099 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 000:845.105 - 0.006ms returns 0
T4A00 000:845.111 JLINK_WriteReg(R14, 0x20000001)
T4A00 000:845.116 - 0.005ms returns 0
T4A00 000:845.126 JLINK_WriteReg(R15 (PC), 0x20000130)
T4A00 000:845.131 - 0.009ms returns 0
T4A00 000:845.137 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 000:845.142 - 0.005ms returns 0
T4A00 000:845.148 JLINK_WriteReg(MSP, 0x20001000)
T4A00 000:845.153 - 0.005ms returns 0
T4A00 000:845.159 JLINK_WriteReg(PSP, 0x20001000)
T4A00 000:845.164 - 0.005ms returns 0
T4A00 000:845.170 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 000:845.175 - 0.005ms returns 0
T4A00 000:845.182 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 000:845.194   CPU_ReadMem(4 bytes @ 0x20000000)
T4A00 000:847.003   CPU_WriteMem(4 bytes @ 0x20000000)
T4A00 000:848.908   CPU_ReadMem(4 bytes @ 0x20000000)
T4A00 000:850.695   CPU_WriteMem(4 bytes @ 0x20000000)
T4A00 000:852.552   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 000:854.295 - 9.113ms returns 0x00000001
T4A00 000:854.313 JLINK_Go()
T4A00 000:854.321   CPU_WriteMem(2 bytes @ 0x20000000)
T4A00 000:856.113   CPU_ReadMem(4 bytes @ 0x********)
T4A00 000:857.853   CPU_WriteMem(4 bytes @ 0x********)
T4A00 000:859.668   CPU_WriteMem(4 bytes @ 0xE0002008)
T4A00 000:859.685   CPU_WriteMem(4 bytes @ 0xE000200C)
T4A00 000:859.698   CPU_WriteMem(4 bytes @ 0xE0002010)
T4A00 000:859.710   CPU_WriteMem(4 bytes @ 0xE0002014)
T4A00 000:865.770   CPU_WriteMem(4 bytes @ 0xE0001004)
T4A00 000:891.511   Memory map 'after startup completion point' is active
T4A00 000:891.526 - 37.212ms
T4A00 000:891.562 JLINK_IsHalted()
T4A00 000:915.929   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 000:917.780 - 26.217ms returns TRUE
T4A00 000:917.822 JLINK_ReadReg(R15 (PC))
T4A00 000:917.832 - 0.010ms returns 0x20000000
T4A00 000:917.840 JLINK_ClrBPEx(BPHandle = 0x00000001)
T4A00 000:917.844 - 0.004ms returns 0x00
T4A00 000:917.849 JLINK_ReadReg(R0)
T4A00 000:917.853 - 0.004ms returns 0x00000000
T4A00 000:918.320 JLINK_HasError()
T4A00 000:918.332 JLINK_WriteReg(R0, 0x00000000)
T4A00 000:918.338 - 0.005ms returns 0
T4A00 000:918.342 JLINK_WriteReg(R1, 0x00000200)
T4A00 000:918.346 - 0.003ms returns 0
T4A00 000:918.350 JLINK_WriteReg(R2, 0x000000FF)
T4A00 000:918.354 - 0.003ms returns 0
T4A00 000:918.358 JLINK_WriteReg(R3, 0x00000000)
T4A00 000:918.361 - 0.003ms returns 0
T4A00 000:918.366 JLINK_WriteReg(R4, 0x00000000)
T4A00 000:918.369 - 0.003ms returns 0
T4A00 000:918.373 JLINK_WriteReg(R5, 0x00000000)
T4A00 000:918.377 - 0.003ms returns 0
T4A00 000:918.381 JLINK_WriteReg(R6, 0x00000000)
T4A00 000:918.385 - 0.003ms returns 0
T4A00 000:918.389 JLINK_WriteReg(R7, 0x00000000)
T4A00 000:918.393 - 0.003ms returns 0
T4A00 000:918.397 JLINK_WriteReg(R8, 0x00000000)
T4A00 000:918.401 - 0.003ms returns 0
T4A00 000:918.405 JLINK_WriteReg(R9, 0x2000045C)
T4A00 000:918.408 - 0.003ms returns 0
T4A00 000:918.413 JLINK_WriteReg(R10, 0x00000000)
T4A00 000:918.416 - 0.003ms returns 0
T4A00 000:918.420 JLINK_WriteReg(R11, 0x00000000)
T4A00 000:918.424 - 0.003ms returns 0
T4A00 000:918.428 JLINK_WriteReg(R12, 0x00000000)
T4A00 000:918.432 - 0.003ms returns 0
T4A00 000:918.436 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 000:918.440 - 0.004ms returns 0
T4A00 000:918.445 JLINK_WriteReg(R14, 0x20000001)
T4A00 000:918.448 - 0.003ms returns 0
T4A00 000:918.453 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 000:918.456 - 0.003ms returns 0
T4A00 000:918.460 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 000:918.464 - 0.003ms returns 0
T4A00 000:918.468 JLINK_WriteReg(MSP, 0x20001000)
T4A00 000:918.472 - 0.003ms returns 0
T4A00 000:918.476 JLINK_WriteReg(PSP, 0x20001000)
T4A00 000:918.480 - 0.003ms returns 0
T4A00 000:918.484 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 000:918.487 - 0.003ms returns 0
T4A00 000:918.492 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 000:918.497 - 0.004ms returns 0x00000002
T4A00 000:918.501 JLINK_Go()
T4A00 000:918.511   CPU_ReadMem(4 bytes @ 0x********)
T4A00 000:940.852 - 22.349ms
T4A00 000:940.906 JLINK_IsHalted()
T4A00 000:965.428   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 000:967.296 - 26.388ms returns TRUE
T4A00 000:967.347 JLINK_ReadReg(R15 (PC))
T4A00 000:967.364 - 0.016ms returns 0x20000000
T4A00 000:967.376 JLINK_ClrBPEx(BPHandle = 0x00000002)
T4A00 000:967.380 - 0.004ms returns 0x00
T4A00 000:967.386 JLINK_ReadReg(R0)
T4A00 000:967.390 - 0.004ms returns 0x00000001
T4A00 000:967.398 JLINK_HasError()
T4A00 000:967.404 JLINK_WriteReg(R0, 0x00000000)
T4A00 000:967.409 - 0.004ms returns 0
T4A00 000:967.413 JLINK_WriteReg(R1, 0x00000200)
T4A00 000:967.417 - 0.004ms returns 0
T4A00 000:967.422 JLINK_WriteReg(R2, 0x000000FF)
T4A00 000:967.425 - 0.003ms returns 0
T4A00 000:967.430 JLINK_WriteReg(R3, 0x00000000)
T4A00 000:967.434 - 0.003ms returns 0
T4A00 000:967.438 JLINK_WriteReg(R4, 0x00000000)
T4A00 000:967.442 - 0.003ms returns 0
T4A00 000:967.446 JLINK_WriteReg(R5, 0x00000000)
T4A00 000:967.450 - 0.003ms returns 0
T4A00 000:967.455 JLINK_WriteReg(R6, 0x00000000)
T4A00 000:967.458 - 0.003ms returns 0
T4A00 000:967.463 JLINK_WriteReg(R7, 0x00000000)
T4A00 000:967.467 - 0.004ms returns 0
T4A00 000:967.471 JLINK_WriteReg(R8, 0x00000000)
T4A00 000:967.475 - 0.004ms returns 0
T4A00 000:967.480 JLINK_WriteReg(R9, 0x2000045C)
T4A00 000:967.484 - 0.003ms returns 0
T4A00 000:967.488 JLINK_WriteReg(R10, 0x00000000)
T4A00 000:967.492 - 0.003ms returns 0
T4A00 000:967.496 JLINK_WriteReg(R11, 0x00000000)
T4A00 000:967.500 - 0.003ms returns 0
T4A00 000:967.504 JLINK_WriteReg(R12, 0x00000000)
T4A00 000:967.508 - 0.003ms returns 0
T4A00 000:967.513 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 000:967.517 - 0.004ms returns 0
T4A00 000:967.521 JLINK_WriteReg(R14, 0x20000001)
T4A00 000:967.525 - 0.003ms returns 0
T4A00 000:967.532 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 000:967.537 - 0.005ms returns 0
T4A00 000:967.541 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 000:967.545 - 0.004ms returns 0
T4A00 000:967.550 JLINK_WriteReg(MSP, 0x20001000)
T4A00 000:967.554 - 0.003ms returns 0
T4A00 000:967.558 JLINK_WriteReg(PSP, 0x20001000)
T4A00 000:967.562 - 0.003ms returns 0
T4A00 000:967.566 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 000:967.570 - 0.003ms returns 0
T4A00 000:967.575 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 000:967.579 - 0.004ms returns 0x00000003
T4A00 000:967.584 JLINK_Go()
T4A00 000:967.593   CPU_ReadMem(4 bytes @ 0x********)
T4A00 000:990.265 - 22.680ms
T4A00 000:990.314 JLINK_IsHalted()
T4A00 000:992.381 - 2.066ms returns FALSE
T4A00 000:992.453 JLINK_HasError()
T4A00 001:012.405 JLINK_IsHalted()
T4A00 001:252.271   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 001:254.112 - 241.706ms returns TRUE
T4A00 001:254.147 JLINK_ReadReg(R15 (PC))
T4A00 001:254.155 - 0.008ms returns 0x20000000
T4A00 001:254.160 JLINK_ClrBPEx(BPHandle = 0x00000003)
T4A00 001:254.165 - 0.004ms returns 0x00
T4A00 001:254.170 JLINK_ReadReg(R0)
T4A00 001:254.174 - 0.004ms returns 0x00000000
T4A00 001:255.374 JLINK_HasError()
T4A00 001:255.400 JLINK_WriteReg(R0, 0x00000200)
T4A00 001:255.407 - 0.007ms returns 0
T4A00 001:255.412 JLINK_WriteReg(R1, 0x00000200)
T4A00 001:255.416 - 0.004ms returns 0
T4A00 001:255.421 JLINK_WriteReg(R2, 0x000000FF)
T4A00 001:255.425 - 0.004ms returns 0
T4A00 001:255.429 JLINK_WriteReg(R3, 0x00000000)
T4A00 001:255.433 - 0.004ms returns 0
T4A00 001:255.438 JLINK_WriteReg(R4, 0x00000000)
T4A00 001:255.442 - 0.003ms returns 0
T4A00 001:255.446 JLINK_WriteReg(R5, 0x00000000)
T4A00 001:255.450 - 0.003ms returns 0
T4A00 001:255.455 JLINK_WriteReg(R6, 0x00000000)
T4A00 001:255.458 - 0.003ms returns 0
T4A00 001:255.463 JLINK_WriteReg(R7, 0x00000000)
T4A00 001:255.467 - 0.003ms returns 0
T4A00 001:255.471 JLINK_WriteReg(R8, 0x00000000)
T4A00 001:255.476 - 0.004ms returns 0
T4A00 001:255.480 JLINK_WriteReg(R9, 0x2000045C)
T4A00 001:255.484 - 0.004ms returns 0
T4A00 001:255.489 JLINK_WriteReg(R10, 0x00000000)
T4A00 001:255.493 - 0.004ms returns 0
T4A00 001:255.497 JLINK_WriteReg(R11, 0x00000000)
T4A00 001:255.501 - 0.003ms returns 0
T4A00 001:255.506 JLINK_WriteReg(R12, 0x00000000)
T4A00 001:255.510 - 0.003ms returns 0
T4A00 001:255.514 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 001:255.519 - 0.004ms returns 0
T4A00 001:255.523 JLINK_WriteReg(R14, 0x20000001)
T4A00 001:255.527 - 0.004ms returns 0
T4A00 001:255.532 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 001:255.536 - 0.004ms returns 0
T4A00 001:255.541 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 001:255.545 - 0.004ms returns 0
T4A00 001:255.549 JLINK_WriteReg(MSP, 0x20001000)
T4A00 001:255.553 - 0.004ms returns 0
T4A00 001:255.558 JLINK_WriteReg(PSP, 0x20001000)
T4A00 001:255.562 - 0.004ms returns 0
T4A00 001:255.566 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 001:255.570 - 0.003ms returns 0
T4A00 001:255.575 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 001:255.580 - 0.005ms returns 0x00000004
T4A00 001:255.585 JLINK_Go()
T4A00 001:255.595   CPU_ReadMem(4 bytes @ 0x********)
T4A00 001:278.282 - 22.696ms
T4A00 001:278.344 JLINK_IsHalted()
T4A00 001:302.694   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 001:304.489 - 26.144ms returns TRUE
T4A00 001:304.547 JLINK_ReadReg(R15 (PC))
T4A00 001:304.558 - 0.010ms returns 0x20000000
T4A00 001:304.566 JLINK_ClrBPEx(BPHandle = 0x00000004)
T4A00 001:304.573 - 0.006ms returns 0x00
T4A00 001:304.582 JLINK_ReadReg(R0)
T4A00 001:304.588 - 0.006ms returns 0x00000001
T4A00 001:304.597 JLINK_HasError()
T4A00 001:304.607 JLINK_WriteReg(R0, 0x00000200)
T4A00 001:304.614 - 0.007ms returns 0
T4A00 001:304.621 JLINK_WriteReg(R1, 0x00000200)
T4A00 001:304.628 - 0.006ms returns 0
T4A00 001:304.634 JLINK_WriteReg(R2, 0x000000FF)
T4A00 001:304.640 - 0.006ms returns 0
T4A00 001:304.647 JLINK_WriteReg(R3, 0x00000000)
T4A00 001:304.653 - 0.006ms returns 0
T4A00 001:304.662 JLINK_WriteReg(R4, 0x00000000)
T4A00 001:304.670 - 0.007ms returns 0
T4A00 001:304.677 JLINK_WriteReg(R5, 0x00000000)
T4A00 001:304.683 - 0.005ms returns 0
T4A00 001:304.690 JLINK_WriteReg(R6, 0x00000000)
T4A00 001:304.696 - 0.006ms returns 0
T4A00 001:304.702 JLINK_WriteReg(R7, 0x00000000)
T4A00 001:304.708 - 0.006ms returns 0
T4A00 001:304.715 JLINK_WriteReg(R8, 0x00000000)
T4A00 001:304.721 - 0.006ms returns 0
T4A00 001:304.728 JLINK_WriteReg(R9, 0x2000045C)
T4A00 001:304.734 - 0.005ms returns 0
T4A00 001:304.741 JLINK_WriteReg(R10, 0x00000000)
T4A00 001:304.747 - 0.006ms returns 0
T4A00 001:304.753 JLINK_WriteReg(R11, 0x00000000)
T4A00 001:304.759 - 0.006ms returns 0
T4A00 001:304.766 JLINK_WriteReg(R12, 0x00000000)
T4A00 001:304.772 - 0.006ms returns 0
T4A00 001:304.779 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 001:304.786 - 0.006ms returns 0
T4A00 001:304.792 JLINK_WriteReg(R14, 0x20000001)
T4A00 001:304.798 - 0.006ms returns 0
T4A00 001:304.805 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 001:304.811 - 0.006ms returns 0
T4A00 001:304.818 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 001:304.824 - 0.006ms returns 0
T4A00 001:304.831 JLINK_WriteReg(MSP, 0x20001000)
T4A00 001:304.837 - 0.005ms returns 0
T4A00 001:304.844 JLINK_WriteReg(PSP, 0x20001000)
T4A00 001:304.850 - 0.006ms returns 0
T4A00 001:304.857 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 001:304.863 - 0.006ms returns 0
T4A00 001:304.870 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 001:304.877 - 0.007ms returns 0x00000005
T4A00 001:304.884 JLINK_Go()
T4A00 001:304.897   CPU_ReadMem(4 bytes @ 0x********)
T4A00 001:327.436 - 22.551ms
T4A00 001:327.511 JLINK_IsHalted()
T4A00 001:329.673 - 2.161ms returns FALSE
T4A00 001:329.750 JLINK_HasError()
T4A00 001:336.639 JLINK_IsHalted()
T4A00 001:360.826   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 001:362.593 - 25.952ms returns TRUE
T4A00 001:362.638 JLINK_ReadReg(R15 (PC))
T4A00 001:362.646 - 0.007ms returns 0x20000000
T4A00 001:362.657 JLINK_ClrBPEx(BPHandle = 0x00000005)
T4A00 001:362.662 - 0.005ms returns 0x00
T4A00 001:362.667 JLINK_ReadReg(R0)
T4A00 001:362.672 - 0.004ms returns 0x00000000
T4A00 001:363.134 JLINK_HasError()
T4A00 001:363.147 JLINK_WriteReg(R0, 0x00000400)
T4A00 001:363.153 - 0.005ms returns 0
T4A00 001:363.158 JLINK_WriteReg(R1, 0x00000200)
T4A00 001:363.163 - 0.004ms returns 0
T4A00 001:363.167 JLINK_WriteReg(R2, 0x000000FF)
T4A00 001:363.171 - 0.004ms returns 0
T4A00 001:363.176 JLINK_WriteReg(R3, 0x00000000)
T4A00 001:363.180 - 0.004ms returns 0
T4A00 001:363.185 JLINK_WriteReg(R4, 0x00000000)
T4A00 001:363.189 - 0.004ms returns 0
T4A00 001:363.194 JLINK_WriteReg(R5, 0x00000000)
T4A00 001:363.198 - 0.004ms returns 0
T4A00 001:363.202 JLINK_WriteReg(R6, 0x00000000)
T4A00 001:363.207 - 0.004ms returns 0
T4A00 001:363.211 JLINK_WriteReg(R7, 0x00000000)
T4A00 001:363.215 - 0.004ms returns 0
T4A00 001:363.220 JLINK_WriteReg(R8, 0x00000000)
T4A00 001:363.224 - 0.004ms returns 0
T4A00 001:363.229 JLINK_WriteReg(R9, 0x2000045C)
T4A00 001:363.233 - 0.004ms returns 0
T4A00 001:363.238 JLINK_WriteReg(R10, 0x00000000)
T4A00 001:363.242 - 0.004ms returns 0
T4A00 001:363.247 JLINK_WriteReg(R11, 0x00000000)
T4A00 001:363.251 - 0.004ms returns 0
T4A00 001:363.256 JLINK_WriteReg(R12, 0x00000000)
T4A00 001:363.260 - 0.004ms returns 0
T4A00 001:363.265 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 001:363.269 - 0.004ms returns 0
T4A00 001:363.274 JLINK_WriteReg(R14, 0x20000001)
T4A00 001:363.278 - 0.004ms returns 0
T4A00 001:363.283 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 001:363.287 - 0.004ms returns 0
T4A00 001:363.292 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 001:363.296 - 0.004ms returns 0
T4A00 001:363.301 JLINK_WriteReg(MSP, 0x20001000)
T4A00 001:363.305 - 0.004ms returns 0
T4A00 001:363.309 JLINK_WriteReg(PSP, 0x20001000)
T4A00 001:363.314 - 0.004ms returns 0
T4A00 001:363.318 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 001:363.322 - 0.004ms returns 0
T4A00 001:363.328 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 001:363.335 - 0.007ms returns 0x00000006
T4A00 001:363.341 JLINK_Go()
T4A00 001:363.352   CPU_ReadMem(4 bytes @ 0x********)
T4A00 001:385.595 - 22.252ms
T4A00 001:385.648 JLINK_IsHalted()
T4A00 001:410.264   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 001:412.019 - 26.370ms returns TRUE
T4A00 001:412.073 JLINK_ReadReg(R15 (PC))
T4A00 001:412.086 - 0.012ms returns 0x20000000
T4A00 001:412.096 JLINK_ClrBPEx(BPHandle = 0x00000006)
T4A00 001:412.105 - 0.008ms returns 0x00
T4A00 001:412.118 JLINK_ReadReg(R0)
T4A00 001:412.126 - 0.007ms returns 0x00000001
T4A00 001:412.136 JLINK_HasError()
T4A00 001:412.145 JLINK_WriteReg(R0, 0x00000400)
T4A00 001:412.154 - 0.008ms returns 0
T4A00 001:412.162 JLINK_WriteReg(R1, 0x00000200)
T4A00 001:412.170 - 0.007ms returns 0
T4A00 001:412.178 JLINK_WriteReg(R2, 0x000000FF)
T4A00 001:412.185 - 0.007ms returns 0
T4A00 001:412.193 JLINK_WriteReg(R3, 0x00000000)
T4A00 001:412.201 - 0.007ms returns 0
T4A00 001:412.209 JLINK_WriteReg(R4, 0x00000000)
T4A00 001:412.216 - 0.007ms returns 0
T4A00 001:412.224 JLINK_WriteReg(R5, 0x00000000)
T4A00 001:412.232 - 0.007ms returns 0
T4A00 001:412.240 JLINK_WriteReg(R6, 0x00000000)
T4A00 001:412.247 - 0.007ms returns 0
T4A00 001:412.255 JLINK_WriteReg(R7, 0x00000000)
T4A00 001:412.262 - 0.007ms returns 0
T4A00 001:412.271 JLINK_WriteReg(R8, 0x00000000)
T4A00 001:412.278 - 0.007ms returns 0
T4A00 001:412.286 JLINK_WriteReg(R9, 0x2000045C)
T4A00 001:412.293 - 0.007ms returns 0
T4A00 001:412.302 JLINK_WriteReg(R10, 0x00000000)
T4A00 001:412.309 - 0.007ms returns 0
T4A00 001:412.317 JLINK_WriteReg(R11, 0x00000000)
T4A00 001:412.324 - 0.007ms returns 0
T4A00 001:412.333 JLINK_WriteReg(R12, 0x00000000)
T4A00 001:412.340 - 0.007ms returns 0
T4A00 001:412.348 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 001:412.356 - 0.008ms returns 0
T4A00 001:412.365 JLINK_WriteReg(R14, 0x20000001)
T4A00 001:412.373 - 0.008ms returns 0
T4A00 001:412.381 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 001:412.389 - 0.007ms returns 0
T4A00 001:412.397 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 001:412.405 - 0.007ms returns 0
T4A00 001:412.413 JLINK_WriteReg(MSP, 0x20001000)
T4A00 001:412.420 - 0.007ms returns 0
T4A00 001:412.428 JLINK_WriteReg(PSP, 0x20001000)
T4A00 001:412.436 - 0.007ms returns 0
T4A00 001:412.444 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 001:412.451 - 0.007ms returns 0
T4A00 001:412.460 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 001:412.469 - 0.008ms returns 0x00000007
T4A00 001:412.477 JLINK_Go()
T4A00 001:412.491   CPU_ReadMem(4 bytes @ 0x********)
T4A00 001:434.786 - 22.308ms
T4A00 001:434.838 JLINK_IsHalted()
T4A00 001:436.953 - 2.114ms returns FALSE
T4A00 001:437.017 JLINK_HasError()
T4A00 001:446.362 JLINK_IsHalted()
T4A00 001:470.699   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 001:472.459 - 26.097ms returns TRUE
T4A00 001:472.497 JLINK_ReadReg(R15 (PC))
T4A00 001:472.504 - 0.007ms returns 0x20000000
T4A00 001:472.515 JLINK_ClrBPEx(BPHandle = 0x00000007)
T4A00 001:472.519 - 0.004ms returns 0x00
T4A00 001:472.524 JLINK_ReadReg(R0)
T4A00 001:472.528 - 0.003ms returns 0x00000000
T4A00 001:472.949 JLINK_HasError()
T4A00 001:472.959 JLINK_WriteReg(R0, 0x00000600)
T4A00 001:472.964 - 0.004ms returns 0
T4A00 001:472.968 JLINK_WriteReg(R1, 0x00000200)
T4A00 001:472.972 - 0.003ms returns 0
T4A00 001:472.976 JLINK_WriteReg(R2, 0x000000FF)
T4A00 001:472.980 - 0.003ms returns 0
T4A00 001:472.984 JLINK_WriteReg(R3, 0x00000000)
T4A00 001:472.988 - 0.003ms returns 0
T4A00 001:472.992 JLINK_WriteReg(R4, 0x00000000)
T4A00 001:472.995 - 0.003ms returns 0
T4A00 001:473.000 JLINK_WriteReg(R5, 0x00000000)
T4A00 001:473.003 - 0.003ms returns 0
T4A00 001:473.007 JLINK_WriteReg(R6, 0x00000000)
T4A00 001:473.011 - 0.003ms returns 0
T4A00 001:473.015 JLINK_WriteReg(R7, 0x00000000)
T4A00 001:473.019 - 0.003ms returns 0
T4A00 001:473.023 JLINK_WriteReg(R8, 0x00000000)
T4A00 001:473.027 - 0.003ms returns 0
T4A00 001:473.031 JLINK_WriteReg(R9, 0x2000045C)
T4A00 001:473.034 - 0.003ms returns 0
T4A00 001:473.041 JLINK_WriteReg(R10, 0x00000000)
T4A00 001:473.046 - 0.005ms returns 0
T4A00 001:473.050 JLINK_WriteReg(R11, 0x00000000)
T4A00 001:473.053 - 0.003ms returns 0
T4A00 001:473.057 JLINK_WriteReg(R12, 0x00000000)
T4A00 001:473.061 - 0.003ms returns 0
T4A00 001:473.065 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 001:473.070 - 0.004ms returns 0
T4A00 001:473.074 JLINK_WriteReg(R14, 0x20000001)
T4A00 001:473.090 - 0.003ms returns 0
T4A00 001:473.094 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 001:473.098 - 0.003ms returns 0
T4A00 001:473.102 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 001:473.106 - 0.003ms returns 0
T4A00 001:473.110 JLINK_WriteReg(MSP, 0x20001000)
T4A00 001:473.119 - 0.009ms returns 0
T4A00 001:473.124 JLINK_WriteReg(PSP, 0x20001000)
T4A00 001:473.127 - 0.003ms returns 0
T4A00 001:473.131 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 001:473.135 - 0.003ms returns 0
T4A00 001:473.140 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 001:473.144 - 0.005ms returns 0x00000008
T4A00 001:473.149 JLINK_Go()
T4A00 001:473.158   CPU_ReadMem(4 bytes @ 0x********)
T4A00 001:719.568 - 246.417ms
T4A00 001:719.618 JLINK_IsHalted()
T4A00 001:744.534   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 001:746.275 - 26.656ms returns TRUE
T4A00 001:746.304 JLINK_ReadReg(R15 (PC))
T4A00 001:746.313 - 0.009ms returns 0x20000000
T4A00 001:746.320 JLINK_ClrBPEx(BPHandle = 0x00000008)
T4A00 001:746.326 - 0.005ms returns 0x00
T4A00 001:746.332 JLINK_ReadReg(R0)
T4A00 001:746.337 - 0.005ms returns 0x00000001
T4A00 001:746.344 JLINK_HasError()
T4A00 001:746.351 JLINK_WriteReg(R0, 0x00000600)
T4A00 001:746.357 - 0.005ms returns 0
T4A00 001:746.363 JLINK_WriteReg(R1, 0x00000200)
T4A00 001:746.368 - 0.005ms returns 0
T4A00 001:746.374 JLINK_WriteReg(R2, 0x000000FF)
T4A00 001:746.379 - 0.004ms returns 0
T4A00 001:746.391 JLINK_WriteReg(R3, 0x00000000)
T4A00 001:746.396 - 0.005ms returns 0
T4A00 001:746.402 JLINK_WriteReg(R4, 0x00000000)
T4A00 001:746.407 - 0.005ms returns 0
T4A00 001:746.413 JLINK_WriteReg(R5, 0x00000000)
T4A00 001:746.418 - 0.004ms returns 0
T4A00 001:746.423 JLINK_WriteReg(R6, 0x00000000)
T4A00 001:746.428 - 0.004ms returns 0
T4A00 001:746.434 JLINK_WriteReg(R7, 0x00000000)
T4A00 001:746.439 - 0.004ms returns 0
T4A00 001:746.444 JLINK_WriteReg(R8, 0x00000000)
T4A00 001:746.450 - 0.005ms returns 0
T4A00 001:746.455 JLINK_WriteReg(R9, 0x2000045C)
T4A00 001:746.460 - 0.005ms returns 0
T4A00 001:746.466 JLINK_WriteReg(R10, 0x00000000)
T4A00 001:746.471 - 0.005ms returns 0
T4A00 001:746.476 JLINK_WriteReg(R11, 0x00000000)
T4A00 001:746.481 - 0.004ms returns 0
T4A00 001:746.487 JLINK_WriteReg(R12, 0x00000000)
T4A00 001:746.492 - 0.005ms returns 0
T4A00 001:746.498 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 001:746.503 - 0.005ms returns 0
T4A00 001:746.509 JLINK_WriteReg(R14, 0x20000001)
T4A00 001:746.514 - 0.005ms returns 0
T4A00 001:746.519 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 001:746.524 - 0.005ms returns 0
T4A00 001:746.530 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 001:746.535 - 0.005ms returns 0
T4A00 001:746.541 JLINK_WriteReg(MSP, 0x20001000)
T4A00 001:746.546 - 0.005ms returns 0
T4A00 001:746.552 JLINK_WriteReg(PSP, 0x20001000)
T4A00 001:746.556 - 0.005ms returns 0
T4A00 001:746.562 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 001:746.567 - 0.005ms returns 0
T4A00 001:746.574 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 001:746.579 - 0.006ms returns 0x00000009
T4A00 001:746.585 JLINK_Go()
T4A00 001:746.596   CPU_ReadMem(4 bytes @ 0x********)
T4A00 001:769.048 - 22.462ms
T4A00 001:769.093 JLINK_IsHalted()
T4A00 001:771.213 - 2.119ms returns FALSE
T4A00 001:771.256 JLINK_HasError()
T4A00 001:778.434 JLINK_IsHalted()
T4A00 001:802.688   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 001:804.486 - 26.050ms returns TRUE
T4A00 001:804.532 JLINK_ReadReg(R15 (PC))
T4A00 001:804.541 - 0.008ms returns 0x20000000
T4A00 001:804.553 JLINK_ClrBPEx(BPHandle = 0x00000009)
T4A00 001:804.558 - 0.005ms returns 0x00
T4A00 001:804.563 JLINK_ReadReg(R0)
T4A00 001:804.572 - 0.008ms returns 0x00000000
T4A00 001:805.069 JLINK_HasError()
T4A00 001:805.082 JLINK_WriteReg(R0, 0x00000800)
T4A00 001:805.088 - 0.005ms returns 0
T4A00 001:805.093 JLINK_WriteReg(R1, 0x00000200)
T4A00 001:805.098 - 0.004ms returns 0
T4A00 001:805.103 JLINK_WriteReg(R2, 0x000000FF)
T4A00 001:805.107 - 0.004ms returns 0
T4A00 001:805.112 JLINK_WriteReg(R3, 0x00000000)
T4A00 001:805.116 - 0.004ms returns 0
T4A00 001:805.121 JLINK_WriteReg(R4, 0x00000000)
T4A00 001:805.126 - 0.004ms returns 0
T4A00 001:805.131 JLINK_WriteReg(R5, 0x00000000)
T4A00 001:805.135 - 0.004ms returns 0
T4A00 001:805.140 JLINK_WriteReg(R6, 0x00000000)
T4A00 001:805.144 - 0.004ms returns 0
T4A00 001:805.149 JLINK_WriteReg(R7, 0x00000000)
T4A00 001:805.154 - 0.004ms returns 0
T4A00 001:805.159 JLINK_WriteReg(R8, 0x00000000)
T4A00 001:805.163 - 0.004ms returns 0
T4A00 001:805.168 JLINK_WriteReg(R9, 0x2000045C)
T4A00 001:805.172 - 0.004ms returns 0
T4A00 001:805.177 JLINK_WriteReg(R10, 0x00000000)
T4A00 001:805.182 - 0.004ms returns 0
T4A00 001:805.186 JLINK_WriteReg(R11, 0x00000000)
T4A00 001:805.191 - 0.004ms returns 0
T4A00 001:805.196 JLINK_WriteReg(R12, 0x00000000)
T4A00 001:805.200 - 0.004ms returns 0
T4A00 001:805.205 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 001:805.210 - 0.005ms returns 0
T4A00 001:805.215 JLINK_WriteReg(R14, 0x20000001)
T4A00 001:805.219 - 0.004ms returns 0
T4A00 001:805.224 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 001:805.229 - 0.004ms returns 0
T4A00 001:805.234 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 001:805.238 - 0.004ms returns 0
T4A00 001:805.243 JLINK_WriteReg(MSP, 0x20001000)
T4A00 001:805.248 - 0.004ms returns 0
T4A00 001:805.253 JLINK_WriteReg(PSP, 0x20001000)
T4A00 001:805.257 - 0.004ms returns 0
T4A00 001:805.262 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 001:805.266 - 0.004ms returns 0
T4A00 001:805.272 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 001:805.277 - 0.005ms returns 0x0000000A
T4A00 001:805.282 JLINK_Go()
T4A00 001:805.294   CPU_ReadMem(4 bytes @ 0x********)
T4A00 001:828.126 - 22.842ms
T4A00 001:828.318 JLINK_IsHalted()
T4A00 001:852.474   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 001:854.188 - 25.869ms returns TRUE
T4A00 001:854.236 JLINK_ReadReg(R15 (PC))
T4A00 001:854.253 - 0.016ms returns 0x20000000
T4A00 001:854.263 JLINK_ClrBPEx(BPHandle = 0x0000000A)
T4A00 001:854.272 - 0.008ms returns 0x00
T4A00 001:854.286 JLINK_ReadReg(R0)
T4A00 001:854.294 - 0.007ms returns 0x00000001
T4A00 001:854.304 JLINK_HasError()
T4A00 001:854.314 JLINK_WriteReg(R0, 0x00000800)
T4A00 001:854.323 - 0.008ms returns 0
T4A00 001:854.332 JLINK_WriteReg(R1, 0x00000200)
T4A00 001:854.339 - 0.007ms returns 0
T4A00 001:854.348 JLINK_WriteReg(R2, 0x000000FF)
T4A00 001:854.356 - 0.007ms returns 0
T4A00 001:854.364 JLINK_WriteReg(R3, 0x00000000)
T4A00 001:854.372 - 0.007ms returns 0
T4A00 001:854.380 JLINK_WriteReg(R4, 0x00000000)
T4A00 001:854.388 - 0.007ms returns 0
T4A00 001:854.396 JLINK_WriteReg(R5, 0x00000000)
T4A00 001:854.404 - 0.007ms returns 0
T4A00 001:854.412 JLINK_WriteReg(R6, 0x00000000)
T4A00 001:854.420 - 0.007ms returns 0
T4A00 001:854.429 JLINK_WriteReg(R7, 0x00000000)
T4A00 001:854.436 - 0.007ms returns 0
T4A00 001:854.445 JLINK_WriteReg(R8, 0x00000000)
T4A00 001:854.452 - 0.007ms returns 0
T4A00 001:854.461 JLINK_WriteReg(R9, 0x2000045C)
T4A00 001:854.469 - 0.007ms returns 0
T4A00 001:854.477 JLINK_WriteReg(R10, 0x00000000)
T4A00 001:854.485 - 0.007ms returns 0
T4A00 001:854.493 JLINK_WriteReg(R11, 0x00000000)
T4A00 001:854.501 - 0.007ms returns 0
T4A00 001:854.510 JLINK_WriteReg(R12, 0x00000000)
T4A00 001:854.517 - 0.007ms returns 0
T4A00 001:854.526 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 001:854.534 - 0.008ms returns 0
T4A00 001:854.543 JLINK_WriteReg(R14, 0x20000001)
T4A00 001:854.551 - 0.007ms returns 0
T4A00 001:854.559 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 001:854.567 - 0.007ms returns 0
T4A00 001:854.576 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 001:854.583 - 0.008ms returns 0
T4A00 001:854.594 JLINK_WriteReg(MSP, 0x20001000)
T4A00 001:854.605 - 0.010ms returns 0
T4A00 001:854.613 JLINK_WriteReg(PSP, 0x20001000)
T4A00 001:854.621 - 0.007ms returns 0
T4A00 001:854.630 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 001:854.637 - 0.007ms returns 0
T4A00 001:854.647 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 001:854.656 - 0.009ms returns 0x0000000B
T4A00 001:854.664 JLINK_Go()
T4A00 001:854.680   CPU_ReadMem(4 bytes @ 0x********)
T4A00 001:877.033 - 22.367ms
T4A00 001:877.091 JLINK_IsHalted()
T4A00 001:879.228 - 2.136ms returns FALSE
T4A00 001:879.272 JLINK_HasError()
T4A00 001:883.722 JLINK_IsHalted()
T4A00 001:907.891   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 001:909.662 - 25.940ms returns TRUE
T4A00 001:909.712 JLINK_ReadReg(R15 (PC))
T4A00 001:909.729 - 0.017ms returns 0x20000000
T4A00 001:909.740 JLINK_ClrBPEx(BPHandle = 0x0000000B)
T4A00 001:909.748 - 0.008ms returns 0x00
T4A00 001:909.758 JLINK_ReadReg(R0)
T4A00 001:909.766 - 0.007ms returns 0x00000000
T4A00 001:910.485 JLINK_HasError()
T4A00 001:910.508 JLINK_WriteReg(R0, 0x00000A00)
T4A00 001:910.519 - 0.010ms returns 0
T4A00 001:910.528 JLINK_WriteReg(R1, 0x00000200)
T4A00 001:910.535 - 0.007ms returns 0
T4A00 001:910.544 JLINK_WriteReg(R2, 0x000000FF)
T4A00 001:910.551 - 0.007ms returns 0
T4A00 001:910.559 JLINK_WriteReg(R3, 0x00000000)
T4A00 001:910.567 - 0.007ms returns 0
T4A00 001:910.575 JLINK_WriteReg(R4, 0x00000000)
T4A00 001:910.582 - 0.007ms returns 0
T4A00 001:910.590 JLINK_WriteReg(R5, 0x00000000)
T4A00 001:910.598 - 0.007ms returns 0
T4A00 001:910.606 JLINK_WriteReg(R6, 0x00000000)
T4A00 001:910.613 - 0.007ms returns 0
T4A00 001:910.621 JLINK_WriteReg(R7, 0x00000000)
T4A00 001:910.629 - 0.007ms returns 0
T4A00 001:910.637 JLINK_WriteReg(R8, 0x00000000)
T4A00 001:910.644 - 0.007ms returns 0
T4A00 001:910.652 JLINK_WriteReg(R9, 0x2000045C)
T4A00 001:910.660 - 0.007ms returns 0
T4A00 001:910.668 JLINK_WriteReg(R10, 0x00000000)
T4A00 001:910.675 - 0.007ms returns 0
T4A00 001:910.684 JLINK_WriteReg(R11, 0x00000000)
T4A00 001:910.691 - 0.007ms returns 0
T4A00 001:910.699 JLINK_WriteReg(R12, 0x00000000)
T4A00 001:910.706 - 0.007ms returns 0
T4A00 001:910.715 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 001:910.725 - 0.010ms returns 0
T4A00 001:910.731 JLINK_WriteReg(R14, 0x20000001)
T4A00 001:910.736 - 0.005ms returns 0
T4A00 001:910.743 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 001:910.748 - 0.005ms returns 0
T4A00 001:910.754 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 001:910.760 - 0.005ms returns 0
T4A00 001:910.766 JLINK_WriteReg(MSP, 0x20001000)
T4A00 001:910.772 - 0.005ms returns 0
T4A00 001:910.778 JLINK_WriteReg(PSP, 0x20001000)
T4A00 001:910.783 - 0.005ms returns 0
T4A00 001:910.789 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 001:910.795 - 0.005ms returns 0
T4A00 001:910.802 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 001:910.810 - 0.008ms returns 0x0000000C
T4A00 001:910.816 JLINK_Go()
T4A00 001:910.836   CPU_ReadMem(4 bytes @ 0x********)
T4A00 001:933.125 - 22.306ms
T4A00 001:933.198 JLINK_IsHalted()
T4A00 001:957.393   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 001:959.173 - 25.974ms returns TRUE
T4A00 001:959.216 JLINK_ReadReg(R15 (PC))
T4A00 001:959.225 - 0.009ms returns 0x20000000
T4A00 001:959.239 JLINK_ClrBPEx(BPHandle = 0x0000000C)
T4A00 001:959.246 - 0.006ms returns 0x00
T4A00 001:959.253 JLINK_ReadReg(R0)
T4A00 001:959.259 - 0.006ms returns 0x00000001
T4A00 001:959.266 JLINK_HasError()
T4A00 001:959.274 JLINK_WriteReg(R0, 0x00000A00)
T4A00 001:959.281 - 0.006ms returns 0
T4A00 001:959.287 JLINK_WriteReg(R1, 0x00000200)
T4A00 001:959.293 - 0.005ms returns 0
T4A00 001:959.299 JLINK_WriteReg(R2, 0x000000FF)
T4A00 001:959.305 - 0.005ms returns 0
T4A00 001:959.311 JLINK_WriteReg(R3, 0x00000000)
T4A00 001:959.317 - 0.005ms returns 0
T4A00 001:959.324 JLINK_WriteReg(R4, 0x00000000)
T4A00 001:959.329 - 0.005ms returns 0
T4A00 001:959.335 JLINK_WriteReg(R5, 0x00000000)
T4A00 001:959.341 - 0.005ms returns 0
T4A00 001:959.350 JLINK_WriteReg(R6, 0x00000000)
T4A00 001:959.358 - 0.007ms returns 0
T4A00 001:959.364 JLINK_WriteReg(R7, 0x00000000)
T4A00 001:959.370 - 0.005ms returns 0
T4A00 001:959.376 JLINK_WriteReg(R8, 0x00000000)
T4A00 001:959.382 - 0.005ms returns 0
T4A00 001:959.388 JLINK_WriteReg(R9, 0x2000045C)
T4A00 001:959.394 - 0.005ms returns 0
T4A00 001:959.400 JLINK_WriteReg(R10, 0x00000000)
T4A00 001:959.420 - 0.020ms returns 0
T4A00 001:959.427 JLINK_WriteReg(R11, 0x00000000)
T4A00 001:959.433 - 0.005ms returns 0
T4A00 001:959.439 JLINK_WriteReg(R12, 0x00000000)
T4A00 001:959.445 - 0.005ms returns 0
T4A00 001:959.451 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 001:959.457 - 0.006ms returns 0
T4A00 001:959.464 JLINK_WriteReg(R14, 0x20000001)
T4A00 001:959.469 - 0.005ms returns 0
T4A00 001:959.476 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 001:959.481 - 0.005ms returns 0
T4A00 001:959.488 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 001:959.494 - 0.005ms returns 0
T4A00 001:959.500 JLINK_WriteReg(MSP, 0x20001000)
T4A00 001:959.506 - 0.005ms returns 0
T4A00 001:959.512 JLINK_WriteReg(PSP, 0x20001000)
T4A00 001:959.518 - 0.006ms returns 0
T4A00 001:959.525 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 001:959.531 - 0.006ms returns 0
T4A00 001:959.539 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 001:959.546 - 0.007ms returns 0x0000000D
T4A00 001:959.553 JLINK_Go()
T4A00 001:959.565   CPU_ReadMem(4 bytes @ 0x********)
T4A00 002:205.880 - 246.326ms
T4A00 002:205.938 JLINK_IsHalted()
T4A00 002:207.833 - 1.895ms returns FALSE
T4A00 002:207.864 JLINK_HasError()
T4A00 002:217.339 JLINK_IsHalted()
T4A00 002:241.599   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 002:243.504 - 26.163ms returns TRUE
T4A00 002:243.563 JLINK_ReadReg(R15 (PC))
T4A00 002:243.572 - 0.009ms returns 0x20000000
T4A00 002:243.577 JLINK_ClrBPEx(BPHandle = 0x0000000D)
T4A00 002:243.582 - 0.004ms returns 0x00
T4A00 002:243.586 JLINK_ReadReg(R0)
T4A00 002:243.590 - 0.003ms returns 0x00000000
T4A00 002:244.030 JLINK_HasError()
T4A00 002:244.040 JLINK_WriteReg(R0, 0x00000C00)
T4A00 002:244.045 - 0.005ms returns 0
T4A00 002:244.049 JLINK_WriteReg(R1, 0x00000200)
T4A00 002:244.053 - 0.003ms returns 0
T4A00 002:244.057 JLINK_WriteReg(R2, 0x000000FF)
T4A00 002:244.061 - 0.003ms returns 0
T4A00 002:244.065 JLINK_WriteReg(R3, 0x00000000)
T4A00 002:244.068 - 0.003ms returns 0
T4A00 002:244.073 JLINK_WriteReg(R4, 0x00000000)
T4A00 002:244.076 - 0.003ms returns 0
T4A00 002:244.080 JLINK_WriteReg(R5, 0x00000000)
T4A00 002:244.084 - 0.003ms returns 0
T4A00 002:244.088 JLINK_WriteReg(R6, 0x00000000)
T4A00 002:244.092 - 0.003ms returns 0
T4A00 002:244.096 JLINK_WriteReg(R7, 0x00000000)
T4A00 002:244.099 - 0.003ms returns 0
T4A00 002:244.104 JLINK_WriteReg(R8, 0x00000000)
T4A00 002:244.107 - 0.003ms returns 0
T4A00 002:244.111 JLINK_WriteReg(R9, 0x2000045C)
T4A00 002:244.115 - 0.003ms returns 0
T4A00 002:244.119 JLINK_WriteReg(R10, 0x00000000)
T4A00 002:244.122 - 0.003ms returns 0
T4A00 002:244.126 JLINK_WriteReg(R11, 0x00000000)
T4A00 002:244.130 - 0.003ms returns 0
T4A00 002:244.134 JLINK_WriteReg(R12, 0x00000000)
T4A00 002:244.138 - 0.003ms returns 0
T4A00 002:244.142 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 002:244.146 - 0.004ms returns 0
T4A00 002:244.150 JLINK_WriteReg(R14, 0x20000001)
T4A00 002:244.154 - 0.003ms returns 0
T4A00 002:244.158 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 002:244.162 - 0.003ms returns 0
T4A00 002:244.166 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 002:244.170 - 0.003ms returns 0
T4A00 002:244.174 JLINK_WriteReg(MSP, 0x20001000)
T4A00 002:244.177 - 0.003ms returns 0
T4A00 002:244.186 JLINK_WriteReg(PSP, 0x20001000)
T4A00 002:244.190 - 0.003ms returns 0
T4A00 002:244.194 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 002:244.197 - 0.003ms returns 0
T4A00 002:244.202 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 002:244.207 - 0.005ms returns 0x0000000E
T4A00 002:244.211 JLINK_Go()
T4A00 002:244.221   CPU_ReadMem(4 bytes @ 0x********)
T4A00 002:266.978 - 22.765ms
T4A00 002:267.033 JLINK_IsHalted()
T4A00 002:291.711   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 002:293.689 - 26.654ms returns TRUE
T4A00 002:293.766 JLINK_ReadReg(R15 (PC))
T4A00 002:293.776 - 0.009ms returns 0x20000000
T4A00 002:293.780 JLINK_ClrBPEx(BPHandle = 0x0000000E)
T4A00 002:293.785 - 0.004ms returns 0x00
T4A00 002:293.790 JLINK_ReadReg(R0)
T4A00 002:293.793 - 0.004ms returns 0x00000001
T4A00 002:293.799 JLINK_HasError()
T4A00 002:293.804 JLINK_WriteReg(R0, 0x00000C00)
T4A00 002:293.809 - 0.004ms returns 0
T4A00 002:293.813 JLINK_WriteReg(R1, 0x00000200)
T4A00 002:293.817 - 0.003ms returns 0
T4A00 002:293.821 JLINK_WriteReg(R2, 0x000000FF)
T4A00 002:293.824 - 0.003ms returns 0
T4A00 002:293.828 JLINK_WriteReg(R3, 0x00000000)
T4A00 002:293.832 - 0.003ms returns 0
T4A00 002:293.836 JLINK_WriteReg(R4, 0x00000000)
T4A00 002:293.840 - 0.003ms returns 0
T4A00 002:293.844 JLINK_WriteReg(R5, 0x00000000)
T4A00 002:293.848 - 0.003ms returns 0
T4A00 002:293.859 JLINK_WriteReg(R6, 0x00000000)
T4A00 002:293.863 - 0.003ms returns 0
T4A00 002:293.867 JLINK_WriteReg(R7, 0x00000000)
T4A00 002:293.871 - 0.003ms returns 0
T4A00 002:293.875 JLINK_WriteReg(R8, 0x00000000)
T4A00 002:293.878 - 0.003ms returns 0
T4A00 002:293.883 JLINK_WriteReg(R9, 0x2000045C)
T4A00 002:293.886 - 0.003ms returns 0
T4A00 002:293.890 JLINK_WriteReg(R10, 0x00000000)
T4A00 002:293.894 - 0.003ms returns 0
T4A00 002:293.898 JLINK_WriteReg(R11, 0x00000000)
T4A00 002:293.902 - 0.003ms returns 0
T4A00 002:293.906 JLINK_WriteReg(R12, 0x00000000)
T4A00 002:293.909 - 0.003ms returns 0
T4A00 002:293.914 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 002:293.918 - 0.004ms returns 0
T4A00 002:293.922 JLINK_WriteReg(R14, 0x20000001)
T4A00 002:293.926 - 0.003ms returns 0
T4A00 002:293.930 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 002:293.933 - 0.003ms returns 0
T4A00 002:293.937 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 002:293.941 - 0.003ms returns 0
T4A00 002:293.945 JLINK_WriteReg(MSP, 0x20001000)
T4A00 002:293.949 - 0.003ms returns 0
T4A00 002:293.953 JLINK_WriteReg(PSP, 0x20001000)
T4A00 002:293.957 - 0.003ms returns 0
T4A00 002:293.961 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 002:293.964 - 0.003ms returns 0
T4A00 002:293.969 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 002:293.974 - 0.004ms returns 0x0000000F
T4A00 002:293.978 JLINK_Go()
T4A00 002:293.988   CPU_ReadMem(4 bytes @ 0x********)
T4A00 002:317.363 - 23.383ms
T4A00 002:317.432 JLINK_IsHalted()
T4A00 002:319.705 - 2.270ms returns FALSE
T4A00 002:319.771 JLINK_HasError()
T4A00 002:324.612 JLINK_IsHalted()
T4A00 002:348.945   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 002:350.760 - 26.146ms returns TRUE
T4A00 002:350.824 JLINK_ReadReg(R15 (PC))
T4A00 002:350.835 - 0.011ms returns 0x20000000
T4A00 002:350.844 JLINK_ClrBPEx(BPHandle = 0x0000000F)
T4A00 002:350.850 - 0.006ms returns 0x00
T4A00 002:350.858 JLINK_ReadReg(R0)
T4A00 002:350.864 - 0.006ms returns 0x00000000
T4A00 002:351.446 JLINK_HasError()
T4A00 002:351.460 JLINK_WriteReg(R0, 0x00000E00)
T4A00 002:351.468 - 0.007ms returns 0
T4A00 002:351.475 JLINK_WriteReg(R1, 0x00000200)
T4A00 002:351.481 - 0.006ms returns 0
T4A00 002:351.487 JLINK_WriteReg(R2, 0x000000FF)
T4A00 002:351.493 - 0.005ms returns 0
T4A00 002:351.500 JLINK_WriteReg(R3, 0x00000000)
T4A00 002:351.506 - 0.005ms returns 0
T4A00 002:351.512 JLINK_WriteReg(R4, 0x00000000)
T4A00 002:351.518 - 0.005ms returns 0
T4A00 002:351.524 JLINK_WriteReg(R5, 0x00000000)
T4A00 002:351.530 - 0.005ms returns 0
T4A00 002:351.537 JLINK_WriteReg(R6, 0x00000000)
T4A00 002:351.542 - 0.005ms returns 0
T4A00 002:351.549 JLINK_WriteReg(R7, 0x00000000)
T4A00 002:351.555 - 0.005ms returns 0
T4A00 002:351.561 JLINK_WriteReg(R8, 0x00000000)
T4A00 002:351.567 - 0.006ms returns 0
T4A00 002:351.574 JLINK_WriteReg(R9, 0x2000045C)
T4A00 002:351.580 - 0.005ms returns 0
T4A00 002:351.586 JLINK_WriteReg(R10, 0x00000000)
T4A00 002:351.592 - 0.005ms returns 0
T4A00 002:351.598 JLINK_WriteReg(R11, 0x00000000)
T4A00 002:351.604 - 0.005ms returns 0
T4A00 002:351.613 JLINK_WriteReg(R12, 0x00000000)
T4A00 002:351.621 - 0.007ms returns 0
T4A00 002:351.628 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 002:351.634 - 0.006ms returns 0
T4A00 002:351.641 JLINK_WriteReg(R14, 0x20000001)
T4A00 002:351.646 - 0.005ms returns 0
T4A00 002:351.653 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 002:351.659 - 0.005ms returns 0
T4A00 002:351.666 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 002:351.672 - 0.006ms returns 0
T4A00 002:351.678 JLINK_WriteReg(MSP, 0x20001000)
T4A00 002:351.684 - 0.005ms returns 0
T4A00 002:351.691 JLINK_WriteReg(PSP, 0x20001000)
T4A00 002:351.696 - 0.005ms returns 0
T4A00 002:351.703 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 002:351.709 - 0.005ms returns 0
T4A00 002:351.716 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 002:351.723 - 0.007ms returns 0x00000010
T4A00 002:351.730 JLINK_Go()
T4A00 002:351.743   CPU_ReadMem(4 bytes @ 0x********)
T4A00 002:374.141 - 22.410ms
T4A00 002:374.193 JLINK_IsHalted()
T4A00 002:398.840   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 002:400.767 - 26.572ms returns TRUE
T4A00 002:400.848 JLINK_ReadReg(R15 (PC))
T4A00 002:400.866 - 0.017ms returns 0x20000000
T4A00 002:400.878 JLINK_ClrBPEx(BPHandle = 0x00000010)
T4A00 002:400.888 - 0.010ms returns 0x00
T4A00 002:400.900 JLINK_ReadReg(R0)
T4A00 002:400.910 - 0.009ms returns 0x00000001
T4A00 002:400.922 JLINK_HasError()
T4A00 002:400.938 JLINK_WriteReg(R0, 0x00000E00)
T4A00 002:400.948 - 0.010ms returns 0
T4A00 002:400.958 JLINK_WriteReg(R1, 0x00000200)
T4A00 002:400.967 - 0.008ms returns 0
T4A00 002:400.977 JLINK_WriteReg(R2, 0x000000FF)
T4A00 002:400.986 - 0.008ms returns 0
T4A00 002:400.996 JLINK_WriteReg(R3, 0x00000000)
T4A00 002:401.004 - 0.008ms returns 0
T4A00 002:401.014 JLINK_WriteReg(R4, 0x00000000)
T4A00 002:401.023 - 0.008ms returns 0
T4A00 002:401.033 JLINK_WriteReg(R5, 0x00000000)
T4A00 002:401.042 - 0.008ms returns 0
T4A00 002:401.052 JLINK_WriteReg(R6, 0x00000000)
T4A00 002:401.060 - 0.008ms returns 0
T4A00 002:401.070 JLINK_WriteReg(R7, 0x00000000)
T4A00 002:401.079 - 0.008ms returns 0
T4A00 002:401.089 JLINK_WriteReg(R8, 0x00000000)
T4A00 002:401.098 - 0.008ms returns 0
T4A00 002:401.108 JLINK_WriteReg(R9, 0x2000045C)
T4A00 002:401.117 - 0.008ms returns 0
T4A00 002:401.126 JLINK_WriteReg(R10, 0x00000000)
T4A00 002:401.135 - 0.008ms returns 0
T4A00 002:401.145 JLINK_WriteReg(R11, 0x00000000)
T4A00 002:401.154 - 0.008ms returns 0
T4A00 002:401.164 JLINK_WriteReg(R12, 0x00000000)
T4A00 002:401.172 - 0.008ms returns 0
T4A00 002:401.182 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 002:401.192 - 0.009ms returns 0
T4A00 002:401.202 JLINK_WriteReg(R14, 0x20000001)
T4A00 002:401.210 - 0.008ms returns 0
T4A00 002:401.220 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 002:401.229 - 0.008ms returns 0
T4A00 002:401.239 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 002:401.248 - 0.009ms returns 0
T4A00 002:401.258 JLINK_WriteReg(MSP, 0x20001000)
T4A00 002:401.267 - 0.008ms returns 0
T4A00 002:401.277 JLINK_WriteReg(PSP, 0x20001000)
T4A00 002:401.285 - 0.008ms returns 0
T4A00 002:401.295 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 002:401.304 - 0.008ms returns 0
T4A00 002:401.315 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 002:401.325 - 0.010ms returns 0x00000011
T4A00 002:401.335 JLINK_Go()
T4A00 002:401.352   CPU_ReadMem(4 bytes @ 0x********)
T4A00 002:423.830 - 22.494ms
T4A00 002:423.894 JLINK_IsHalted()
T4A00 002:426.116 - 2.222ms returns FALSE
T4A00 002:426.164 JLINK_HasError()
T4A00 002:431.751 JLINK_IsHalted()
T4A00 002:456.006   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 002:457.813 - 26.060ms returns TRUE
T4A00 002:457.863 JLINK_ReadReg(R15 (PC))
T4A00 002:457.870 - 0.007ms returns 0x20000000
T4A00 002:457.876 JLINK_ClrBPEx(BPHandle = 0x00000011)
T4A00 002:457.880 - 0.004ms returns 0x00
T4A00 002:457.888 JLINK_ReadReg(R0)
T4A00 002:457.892 - 0.004ms returns 0x00000000
T4A00 002:458.343 JLINK_HasError()
T4A00 002:458.356 JLINK_WriteReg(R0, 0x00001000)
T4A00 002:458.361 - 0.005ms returns 0
T4A00 002:458.367 JLINK_WriteReg(R1, 0x00000200)
T4A00 002:458.373 - 0.005ms returns 0
T4A00 002:458.377 JLINK_WriteReg(R2, 0x000000FF)
T4A00 002:458.380 - 0.003ms returns 0
T4A00 002:458.384 JLINK_WriteReg(R3, 0x00000000)
T4A00 002:458.388 - 0.003ms returns 0
T4A00 002:458.392 JLINK_WriteReg(R4, 0x00000000)
T4A00 002:458.396 - 0.003ms returns 0
T4A00 002:458.400 JLINK_WriteReg(R5, 0x00000000)
T4A00 002:458.404 - 0.003ms returns 0
T4A00 002:458.408 JLINK_WriteReg(R6, 0x00000000)
T4A00 002:458.411 - 0.003ms returns 0
T4A00 002:458.416 JLINK_WriteReg(R7, 0x00000000)
T4A00 002:458.419 - 0.003ms returns 0
T4A00 002:458.424 JLINK_WriteReg(R8, 0x00000000)
T4A00 002:458.427 - 0.003ms returns 0
T4A00 002:458.431 JLINK_WriteReg(R9, 0x2000045C)
T4A00 002:458.435 - 0.003ms returns 0
T4A00 002:458.439 JLINK_WriteReg(R10, 0x00000000)
T4A00 002:458.443 - 0.003ms returns 0
T4A00 002:458.447 JLINK_WriteReg(R11, 0x00000000)
T4A00 002:458.451 - 0.003ms returns 0
T4A00 002:458.455 JLINK_WriteReg(R12, 0x00000000)
T4A00 002:458.458 - 0.003ms returns 0
T4A00 002:458.463 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 002:458.467 - 0.004ms returns 0
T4A00 002:458.471 JLINK_WriteReg(R14, 0x20000001)
T4A00 002:458.475 - 0.003ms returns 0
T4A00 002:458.479 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 002:458.483 - 0.003ms returns 0
T4A00 002:458.487 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 002:458.491 - 0.003ms returns 0
T4A00 002:458.495 JLINK_WriteReg(MSP, 0x20001000)
T4A00 002:458.498 - 0.003ms returns 0
T4A00 002:458.503 JLINK_WriteReg(PSP, 0x20001000)
T4A00 002:458.506 - 0.003ms returns 0
T4A00 002:458.510 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 002:458.514 - 0.003ms returns 0
T4A00 002:458.519 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 002:458.523 - 0.004ms returns 0x00000012
T4A00 002:458.528 JLINK_Go()
T4A00 002:458.537   CPU_ReadMem(4 bytes @ 0x********)
T4A00 002:705.424 - 246.895ms
T4A00 002:705.508 JLINK_IsHalted()
T4A00 002:730.269   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 002:732.435 - 26.926ms returns TRUE
T4A00 002:732.494 JLINK_ReadReg(R15 (PC))
T4A00 002:732.503 - 0.009ms returns 0x20000000
T4A00 002:732.508 JLINK_ClrBPEx(BPHandle = 0x00000012)
T4A00 002:732.513 - 0.004ms returns 0x00
T4A00 002:732.517 JLINK_ReadReg(R0)
T4A00 002:732.521 - 0.003ms returns 0x00000001
T4A00 002:732.527 JLINK_HasError()
T4A00 002:732.532 JLINK_WriteReg(R0, 0x00001000)
T4A00 002:732.537 - 0.004ms returns 0
T4A00 002:732.546 JLINK_WriteReg(R1, 0x00000200)
T4A00 002:732.549 - 0.003ms returns 0
T4A00 002:732.553 JLINK_WriteReg(R2, 0x000000FF)
T4A00 002:732.557 - 0.003ms returns 0
T4A00 002:732.561 JLINK_WriteReg(R3, 0x00000000)
T4A00 002:732.565 - 0.003ms returns 0
T4A00 002:732.569 JLINK_WriteReg(R4, 0x00000000)
T4A00 002:732.572 - 0.003ms returns 0
T4A00 002:732.577 JLINK_WriteReg(R5, 0x00000000)
T4A00 002:732.580 - 0.003ms returns 0
T4A00 002:732.584 JLINK_WriteReg(R6, 0x00000000)
T4A00 002:732.588 - 0.003ms returns 0
T4A00 002:732.592 JLINK_WriteReg(R7, 0x00000000)
T4A00 002:732.596 - 0.003ms returns 0
T4A00 002:732.600 JLINK_WriteReg(R8, 0x00000000)
T4A00 002:732.604 - 0.003ms returns 0
T4A00 002:732.615 JLINK_WriteReg(R9, 0x2000045C)
T4A00 002:732.619 - 0.003ms returns 0
T4A00 002:732.623 JLINK_WriteReg(R10, 0x00000000)
T4A00 002:732.627 - 0.003ms returns 0
T4A00 002:732.631 JLINK_WriteReg(R11, 0x00000000)
T4A00 002:732.635 - 0.003ms returns 0
T4A00 002:732.639 JLINK_WriteReg(R12, 0x00000000)
T4A00 002:732.642 - 0.003ms returns 0
T4A00 002:732.647 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 002:732.651 - 0.004ms returns 0
T4A00 002:732.655 JLINK_WriteReg(R14, 0x20000001)
T4A00 002:732.659 - 0.003ms returns 0
T4A00 002:732.663 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 002:732.666 - 0.003ms returns 0
T4A00 002:732.670 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 002:732.674 - 0.003ms returns 0
T4A00 002:732.679 JLINK_WriteReg(MSP, 0x20001000)
T4A00 002:732.682 - 0.003ms returns 0
T4A00 002:732.687 JLINK_WriteReg(PSP, 0x20001000)
T4A00 002:732.690 - 0.003ms returns 0
T4A00 002:732.698 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 002:732.702 - 0.003ms returns 0
T4A00 002:732.707 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 002:732.712 - 0.005ms returns 0x00000013
T4A00 002:732.716 JLINK_Go()
T4A00 002:732.726   CPU_ReadMem(4 bytes @ 0x********)
T4A00 002:756.059 - 23.341ms
T4A00 002:756.134 JLINK_IsHalted()
T4A00 002:758.265 - 2.131ms returns FALSE
T4A00 002:758.298 JLINK_HasError()
T4A00 002:764.436 JLINK_IsHalted()
T4A00 002:789.366   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 002:791.123 - 26.686ms returns TRUE
T4A00 002:791.151 JLINK_ReadReg(R15 (PC))
T4A00 002:791.157 - 0.006ms returns 0x20000000
T4A00 002:791.168 JLINK_ClrBPEx(BPHandle = 0x00000013)
T4A00 002:791.172 - 0.004ms returns 0x00
T4A00 002:791.177 JLINK_ReadReg(R0)
T4A00 002:791.180 - 0.003ms returns 0x00000000
T4A00 002:791.675 JLINK_HasError()
T4A00 002:791.685 JLINK_WriteReg(R0, 0x00001200)
T4A00 002:791.690 - 0.005ms returns 0
T4A00 002:791.695 JLINK_WriteReg(R1, 0x00000200)
T4A00 002:791.698 - 0.003ms returns 0
T4A00 002:791.703 JLINK_WriteReg(R2, 0x000000FF)
T4A00 002:791.706 - 0.003ms returns 0
T4A00 002:791.710 JLINK_WriteReg(R3, 0x00000000)
T4A00 002:791.714 - 0.003ms returns 0
T4A00 002:791.718 JLINK_WriteReg(R4, 0x00000000)
T4A00 002:791.722 - 0.003ms returns 0
T4A00 002:791.726 JLINK_WriteReg(R5, 0x00000000)
T4A00 002:791.730 - 0.003ms returns 0
T4A00 002:791.734 JLINK_WriteReg(R6, 0x00000000)
T4A00 002:791.737 - 0.003ms returns 0
T4A00 002:791.742 JLINK_WriteReg(R7, 0x00000000)
T4A00 002:791.745 - 0.003ms returns 0
T4A00 002:791.749 JLINK_WriteReg(R8, 0x00000000)
T4A00 002:791.753 - 0.003ms returns 0
T4A00 002:791.757 JLINK_WriteReg(R9, 0x2000045C)
T4A00 002:791.761 - 0.003ms returns 0
T4A00 002:791.765 JLINK_WriteReg(R10, 0x00000000)
T4A00 002:791.769 - 0.004ms returns 0
T4A00 002:791.773 JLINK_WriteReg(R11, 0x00000000)
T4A00 002:791.777 - 0.003ms returns 0
T4A00 002:791.781 JLINK_WriteReg(R12, 0x00000000)
T4A00 002:791.784 - 0.003ms returns 0
T4A00 002:791.789 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 002:791.793 - 0.004ms returns 0
T4A00 002:791.797 JLINK_WriteReg(R14, 0x20000001)
T4A00 002:791.801 - 0.003ms returns 0
T4A00 002:791.805 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 002:791.808 - 0.003ms returns 0
T4A00 002:791.813 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 002:791.816 - 0.003ms returns 0
T4A00 002:791.820 JLINK_WriteReg(MSP, 0x20001000)
T4A00 002:791.824 - 0.003ms returns 0
T4A00 002:791.828 JLINK_WriteReg(PSP, 0x20001000)
T4A00 002:791.832 - 0.003ms returns 0
T4A00 002:791.836 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 002:791.840 - 0.003ms returns 0
T4A00 002:791.844 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 002:791.849 - 0.004ms returns 0x00000014
T4A00 002:791.853 JLINK_Go()
T4A00 002:791.863   CPU_ReadMem(4 bytes @ 0x********)
T4A00 002:814.611 - 22.757ms
T4A00 002:814.655 JLINK_IsHalted()
T4A00 002:838.938   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 002:840.754 - 26.097ms returns TRUE
T4A00 002:840.816 JLINK_ReadReg(R15 (PC))
T4A00 002:840.826 - 0.010ms returns 0x20000000
T4A00 002:840.837 JLINK_ClrBPEx(BPHandle = 0x00000014)
T4A00 002:840.842 - 0.005ms returns 0x00
T4A00 002:840.847 JLINK_ReadReg(R0)
T4A00 002:840.851 - 0.004ms returns 0x00000001
T4A00 002:840.857 JLINK_HasError()
T4A00 002:840.863 JLINK_WriteReg(R0, 0x00001200)
T4A00 002:840.867 - 0.004ms returns 0
T4A00 002:840.872 JLINK_WriteReg(R1, 0x00000200)
T4A00 002:840.876 - 0.003ms returns 0
T4A00 002:840.880 JLINK_WriteReg(R2, 0x000000FF)
T4A00 002:840.884 - 0.003ms returns 0
T4A00 002:840.888 JLINK_WriteReg(R3, 0x00000000)
T4A00 002:840.892 - 0.003ms returns 0
T4A00 002:840.897 JLINK_WriteReg(R4, 0x00000000)
T4A00 002:840.900 - 0.003ms returns 0
T4A00 002:840.905 JLINK_WriteReg(R5, 0x00000000)
T4A00 002:840.909 - 0.003ms returns 0
T4A00 002:840.913 JLINK_WriteReg(R6, 0x00000000)
T4A00 002:840.917 - 0.003ms returns 0
T4A00 002:840.922 JLINK_WriteReg(R7, 0x00000000)
T4A00 002:840.925 - 0.003ms returns 0
T4A00 002:840.932 JLINK_WriteReg(R8, 0x00000000)
T4A00 002:840.937 - 0.005ms returns 0
T4A00 002:840.942 JLINK_WriteReg(R9, 0x2000045C)
T4A00 002:840.945 - 0.003ms returns 0
T4A00 002:840.950 JLINK_WriteReg(R10, 0x00000000)
T4A00 002:840.954 - 0.003ms returns 0
T4A00 002:840.958 JLINK_WriteReg(R11, 0x00000000)
T4A00 002:840.962 - 0.003ms returns 0
T4A00 002:840.966 JLINK_WriteReg(R12, 0x00000000)
T4A00 002:840.970 - 0.003ms returns 0
T4A00 002:840.975 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 002:840.979 - 0.004ms returns 0
T4A00 002:840.983 JLINK_WriteReg(R14, 0x20000001)
T4A00 002:840.987 - 0.004ms returns 0
T4A00 002:840.992 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 002:840.996 - 0.004ms returns 0
T4A00 002:841.000 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 002:841.004 - 0.004ms returns 0
T4A00 002:841.008 JLINK_WriteReg(MSP, 0x20001000)
T4A00 002:841.012 - 0.003ms returns 0
T4A00 002:841.017 JLINK_WriteReg(PSP, 0x20001000)
T4A00 002:841.021 - 0.004ms returns 0
T4A00 002:841.025 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 002:841.029 - 0.003ms returns 0
T4A00 002:841.034 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 002:841.039 - 0.005ms returns 0x00000015
T4A00 002:841.043 JLINK_Go()
T4A00 002:841.055   CPU_ReadMem(4 bytes @ 0x********)
T4A00 002:863.459 - 22.415ms
T4A00 002:863.524 JLINK_IsHalted()
T4A00 002:865.769 - 2.244ms returns FALSE
T4A00 002:865.839 JLINK_HasError()
T4A00 002:868.742 JLINK_IsHalted()
T4A00 002:893.130   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 002:895.124 - 26.381ms returns TRUE
T4A00 002:895.235 JLINK_ReadReg(R15 (PC))
T4A00 002:895.250 - 0.015ms returns 0x20000000
T4A00 002:895.258 JLINK_ClrBPEx(BPHandle = 0x00000015)
T4A00 002:895.266 - 0.008ms returns 0x00
T4A00 002:895.274 JLINK_ReadReg(R0)
T4A00 002:895.282 - 0.007ms returns 0x00000000
T4A00 002:897.236 JLINK_HasError()
T4A00 002:897.258 JLINK_WriteReg(R0, 0x00001400)
T4A00 002:897.268 - 0.010ms returns 0
T4A00 002:897.277 JLINK_WriteReg(R1, 0x00000200)
T4A00 002:897.284 - 0.007ms returns 0
T4A00 002:897.292 JLINK_WriteReg(R2, 0x000000FF)
T4A00 002:897.299 - 0.007ms returns 0
T4A00 002:897.307 JLINK_WriteReg(R3, 0x00000000)
T4A00 002:897.314 - 0.007ms returns 0
T4A00 002:897.370 JLINK_WriteReg(R4, 0x00000000)
T4A00 002:897.382 - 0.012ms returns 0
T4A00 002:897.388 JLINK_WriteReg(R5, 0x00000000)
T4A00 002:897.392 - 0.004ms returns 0
T4A00 002:897.397 JLINK_WriteReg(R6, 0x00000000)
T4A00 002:897.402 - 0.004ms returns 0
T4A00 002:897.406 JLINK_WriteReg(R7, 0x00000000)
T4A00 002:897.411 - 0.004ms returns 0
T4A00 002:897.416 JLINK_WriteReg(R8, 0x00000000)
T4A00 002:897.420 - 0.004ms returns 0
T4A00 002:897.425 JLINK_WriteReg(R9, 0x2000045C)
T4A00 002:897.429 - 0.004ms returns 0
T4A00 002:897.434 JLINK_WriteReg(R10, 0x00000000)
T4A00 002:897.439 - 0.004ms returns 0
T4A00 002:897.444 JLINK_WriteReg(R11, 0x00000000)
T4A00 002:897.448 - 0.004ms returns 0
T4A00 002:897.453 JLINK_WriteReg(R12, 0x00000000)
T4A00 002:897.457 - 0.004ms returns 0
T4A00 002:897.462 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 002:897.467 - 0.005ms returns 0
T4A00 002:897.472 JLINK_WriteReg(R14, 0x20000001)
T4A00 002:897.477 - 0.004ms returns 0
T4A00 002:897.482 JLINK_WriteReg(R15 (PC), 0x20000020)
T4A00 002:897.486 - 0.004ms returns 0
T4A00 002:897.491 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 002:897.496 - 0.004ms returns 0
T4A00 002:897.500 JLINK_WriteReg(MSP, 0x20001000)
T4A00 002:897.505 - 0.004ms returns 0
T4A00 002:897.510 JLINK_WriteReg(PSP, 0x20001000)
T4A00 002:897.514 - 0.004ms returns 0
T4A00 002:897.519 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 002:897.523 - 0.004ms returns 0
T4A00 002:897.529 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 002:897.535 - 0.006ms returns 0x00000016
T4A00 002:897.540 JLINK_Go()
T4A00 002:897.550   CPU_ReadMem(4 bytes @ 0x********)
T4A00 002:920.013 - 22.471ms
T4A00 002:920.063 JLINK_IsHalted()
T4A00 002:944.818   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 002:946.815 - 26.750ms returns TRUE
T4A00 002:946.920 JLINK_ReadReg(R15 (PC))
T4A00 002:946.940 - 0.020ms returns 0x20000000
T4A00 002:946.957 JLINK_ClrBPEx(BPHandle = 0x00000016)
T4A00 002:946.967 - 0.010ms returns 0x00
T4A00 002:946.980 JLINK_ReadReg(R0)
T4A00 002:946.989 - 0.009ms returns 0x00000001
T4A00 002:947.003 JLINK_HasError()
T4A00 002:947.018 JLINK_WriteReg(R0, 0x00001400)
T4A00 002:947.028 - 0.010ms returns 0
T4A00 002:947.040 JLINK_WriteReg(R1, 0x00000200)
T4A00 002:947.049 - 0.009ms returns 0
T4A00 002:947.060 JLINK_WriteReg(R2, 0x000000FF)
T4A00 002:947.069 - 0.008ms returns 0
T4A00 002:947.080 JLINK_WriteReg(R3, 0x00000000)
T4A00 002:947.089 - 0.008ms returns 0
T4A00 002:947.100 JLINK_WriteReg(R4, 0x00000000)
T4A00 002:947.109 - 0.008ms returns 0
T4A00 002:947.120 JLINK_WriteReg(R5, 0x00000000)
T4A00 002:947.129 - 0.008ms returns 0
T4A00 002:947.140 JLINK_WriteReg(R6, 0x00000000)
T4A00 002:947.149 - 0.008ms returns 0
T4A00 002:947.160 JLINK_WriteReg(R7, 0x00000000)
T4A00 002:947.169 - 0.008ms returns 0
T4A00 002:947.180 JLINK_WriteReg(R8, 0x00000000)
T4A00 002:947.189 - 0.008ms returns 0
T4A00 002:947.200 JLINK_WriteReg(R9, 0x2000045C)
T4A00 002:947.209 - 0.009ms returns 0
T4A00 002:947.220 JLINK_WriteReg(R10, 0x00000000)
T4A00 002:947.229 - 0.009ms returns 0
T4A00 002:947.240 JLINK_WriteReg(R11, 0x00000000)
T4A00 002:947.249 - 0.009ms returns 0
T4A00 002:947.263 JLINK_WriteReg(R12, 0x00000000)
T4A00 002:947.272 - 0.009ms returns 0
T4A00 002:947.284 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 002:947.294 - 0.010ms returns 0
T4A00 002:947.306 JLINK_WriteReg(R14, 0x20000001)
T4A00 002:947.315 - 0.009ms returns 0
T4A00 002:947.327 JLINK_WriteReg(R15 (PC), 0x200000B8)
T4A00 002:947.337 - 0.009ms returns 0
T4A00 002:947.348 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 002:947.357 - 0.009ms returns 0
T4A00 002:947.368 JLINK_WriteReg(MSP, 0x20001000)
T4A00 002:947.377 - 0.009ms returns 0
T4A00 002:947.387 JLINK_WriteReg(PSP, 0x20001000)
T4A00 002:947.396 - 0.009ms returns 0
T4A00 002:947.406 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 002:947.416 - 0.009ms returns 0
T4A00 002:947.427 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 002:947.438 - 0.011ms returns 0x00000017
T4A00 002:947.452 JLINK_Go()
T4A00 002:947.472   CPU_ReadMem(4 bytes @ 0x********)
T4A00 003:194.880 - 247.426ms
T4A00 003:194.971 JLINK_IsHalted()
T4A00 003:197.308 - 2.335ms returns FALSE
T4A00 003:197.393 JLINK_HasError()
T4A00 003:205.236 JLINK_IsHalted()
T4A00 003:230.174   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 003:232.619 - 27.381ms returns TRUE
T4A00 003:232.678 JLINK_ReadReg(R15 (PC))
T4A00 003:232.686 - 0.009ms returns 0x20000000
T4A00 003:232.692 JLINK_ClrBPEx(BPHandle = 0x00000017)
T4A00 003:232.696 - 0.004ms returns 0x00
T4A00 003:232.701 JLINK_ReadReg(R0)
T4A00 003:232.704 - 0.003ms returns 0x00000000
T4A00 003:233.071 JLINK_HasError()
T4A00 003:233.081 JLINK_WriteReg(R0, 0x00000001)
T4A00 003:233.086 - 0.004ms returns 0
T4A00 003:233.090 JLINK_WriteReg(R1, 0x00000200)
T4A00 003:233.094 - 0.003ms returns 0
T4A00 003:233.098 JLINK_WriteReg(R2, 0x000000FF)
T4A00 003:233.102 - 0.003ms returns 0
T4A00 003:233.106 JLINK_WriteReg(R3, 0x00000000)
T4A00 003:233.109 - 0.003ms returns 0
T4A00 003:233.113 JLINK_WriteReg(R4, 0x00000000)
T4A00 003:233.117 - 0.003ms returns 0
T4A00 003:233.121 JLINK_WriteReg(R5, 0x00000000)
T4A00 003:233.125 - 0.003ms returns 0
T4A00 003:233.129 JLINK_WriteReg(R6, 0x00000000)
T4A00 003:233.132 - 0.003ms returns 0
T4A00 003:233.136 JLINK_WriteReg(R7, 0x00000000)
T4A00 003:233.140 - 0.003ms returns 0
T4A00 003:233.144 JLINK_WriteReg(R8, 0x00000000)
T4A00 003:233.148 - 0.003ms returns 0
T4A00 003:233.152 JLINK_WriteReg(R9, 0x2000045C)
T4A00 003:233.156 - 0.003ms returns 0
T4A00 003:233.160 JLINK_WriteReg(R10, 0x00000000)
T4A00 003:233.163 - 0.003ms returns 0
T4A00 003:233.168 JLINK_WriteReg(R11, 0x00000000)
T4A00 003:233.171 - 0.003ms returns 0
T4A00 003:233.175 JLINK_WriteReg(R12, 0x00000000)
T4A00 003:233.179 - 0.003ms returns 0
T4A00 003:233.183 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 003:233.188 - 0.004ms returns 0
T4A00 003:233.194 JLINK_WriteReg(R14, 0x20000001)
T4A00 003:233.198 - 0.003ms returns 0
T4A00 003:233.202 JLINK_WriteReg(R15 (PC), 0x200003C8)
T4A00 003:233.206 - 0.003ms returns 0
T4A00 003:233.210 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 003:233.214 - 0.003ms returns 0
T4A00 003:233.218 JLINK_WriteReg(MSP, 0x20001000)
T4A00 003:233.222 - 0.003ms returns 0
T4A00 003:233.226 JLINK_WriteReg(PSP, 0x20001000)
T4A00 003:233.229 - 0.003ms returns 0
T4A00 003:233.233 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 003:233.237 - 0.003ms returns 0
T4A00 003:233.242 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 003:233.246 - 0.004ms returns 0x00000018
T4A00 003:233.251 JLINK_Go()
T4A00 003:233.260   CPU_ReadMem(4 bytes @ 0x********)
T4A00 003:257.491 - 24.239ms
T4A00 003:257.562 JLINK_IsHalted()
T4A00 003:282.855   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 003:284.794 - 27.230ms returns TRUE
T4A00 003:284.863 JLINK_ReadReg(R15 (PC))
T4A00 003:284.873 - 0.010ms returns 0x20000000
T4A00 003:284.885 JLINK_ClrBPEx(BPHandle = 0x00000018)
T4A00 003:284.889 - 0.004ms returns 0x00
T4A00 003:284.895 JLINK_ReadReg(R0)
T4A00 003:284.899 - 0.004ms returns 0x00000000
T4A00 003:343.138 JLINK_WriteMem(0x20000000, 0x460 Bytes, ...)
T4A00 003:343.156   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T4A00 003:343.181   CPU_WriteMem(1120 bytes @ 0x20000000)
T4A00 003:402.285 - 59.146ms returns 0x460
T4A00 003:403.120 JLINK_HasError()
T4A00 003:403.134 JLINK_WriteReg(R0, 0x00000000)
T4A00 003:403.142 - 0.008ms returns 0
T4A00 003:403.147 JLINK_WriteReg(R1, 0x00B71B00)
T4A00 003:403.151 - 0.003ms returns 0
T4A00 003:403.155 JLINK_WriteReg(R2, 0x00000002)
T4A00 003:403.159 - 0.003ms returns 0
T4A00 003:403.163 JLINK_WriteReg(R3, 0x00000000)
T4A00 003:403.166 - 0.003ms returns 0
T4A00 003:403.170 JLINK_WriteReg(R4, 0x00000000)
T4A00 003:403.174 - 0.003ms returns 0
T4A00 003:403.178 JLINK_WriteReg(R5, 0x00000000)
T4A00 003:403.182 - 0.003ms returns 0
T4A00 003:403.186 JLINK_WriteReg(R6, 0x00000000)
T4A00 003:403.190 - 0.003ms returns 0
T4A00 003:403.194 JLINK_WriteReg(R7, 0x00000000)
T4A00 003:403.197 - 0.003ms returns 0
T4A00 003:403.202 JLINK_WriteReg(R8, 0x00000000)
T4A00 003:403.205 - 0.003ms returns 0
T4A00 003:403.209 JLINK_WriteReg(R9, 0x2000045C)
T4A00 003:403.213 - 0.003ms returns 0
T4A00 003:403.217 JLINK_WriteReg(R10, 0x00000000)
T4A00 003:403.221 - 0.003ms returns 0
T4A00 003:403.225 JLINK_WriteReg(R11, 0x00000000)
T4A00 003:403.229 - 0.003ms returns 0
T4A00 003:403.233 JLINK_WriteReg(R12, 0x00000000)
T4A00 003:403.236 - 0.003ms returns 0
T4A00 003:403.240 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 003:403.244 - 0.004ms returns 0
T4A00 003:403.249 JLINK_WriteReg(R14, 0x20000001)
T4A00 003:403.252 - 0.003ms returns 0
T4A00 003:403.257 JLINK_WriteReg(R15 (PC), 0x20000130)
T4A00 003:403.260 - 0.004ms returns 0
T4A00 003:403.265 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 003:403.268 - 0.003ms returns 0
T4A00 003:403.272 JLINK_WriteReg(MSP, 0x20001000)
T4A00 003:403.276 - 0.003ms returns 0
T4A00 003:403.280 JLINK_WriteReg(PSP, 0x20001000)
T4A00 003:403.284 - 0.003ms returns 0
T4A00 003:403.288 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 003:403.292 - 0.003ms returns 0
T4A00 003:403.296 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 003:403.307   CPU_ReadMem(4 bytes @ 0x20000000)
T4A00 003:405.070   CPU_WriteMem(4 bytes @ 0x20000000)
T4A00 003:407.095   CPU_ReadMem(4 bytes @ 0x20000000)
T4A00 003:409.016   CPU_WriteMem(4 bytes @ 0x20000000)
T4A00 003:410.919   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 003:412.656 - 9.358ms returns 0x00000019
T4A00 003:412.700 JLINK_Go()
T4A00 003:412.714   CPU_WriteMem(2 bytes @ 0x20000000)
T4A00 003:414.687   CPU_ReadMem(4 bytes @ 0x********)
T4A00 003:437.158 - 24.457ms
T4A00 003:437.217 JLINK_IsHalted()
T4A00 003:461.543   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 003:463.359 - 26.140ms returns TRUE
T4A00 003:463.416 JLINK_ReadReg(R15 (PC))
T4A00 003:463.424 - 0.008ms returns 0x20000000
T4A00 003:463.430 JLINK_ClrBPEx(BPHandle = 0x00000019)
T4A00 003:463.477 - 0.047ms returns 0x00
T4A00 003:463.483 JLINK_ReadReg(R0)
T4A00 003:463.487 - 0.004ms returns 0x00000000
T4A00 003:463.843 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 003:463.851   Data:  08 15 00 20 D5 00 00 00 EF 00 00 00 F1 00 00 00 ...
T4A00 003:463.865   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 003:715.420 - 251.575ms returns 0x200
T4A00 003:715.490 JLINK_HasError()
T4A00 003:715.514 JLINK_WriteReg(R0, 0x00000000)
T4A00 003:715.523 - 0.009ms returns 0
T4A00 003:715.527 JLINK_WriteReg(R1, 0x00000200)
T4A00 003:715.531 - 0.003ms returns 0
T4A00 003:715.535 JLINK_WriteReg(R2, 0x20000460)
T4A00 003:715.539 - 0.003ms returns 0
T4A00 003:715.543 JLINK_WriteReg(R3, 0x00000000)
T4A00 003:715.547 - 0.003ms returns 0
T4A00 003:715.551 JLINK_WriteReg(R4, 0x00000000)
T4A00 003:715.554 - 0.003ms returns 0
T4A00 003:715.568 JLINK_WriteReg(R5, 0x00000000)
T4A00 003:715.581 - 0.013ms returns 0
T4A00 003:715.593 JLINK_WriteReg(R6, 0x00000000)
T4A00 003:715.597 - 0.004ms returns 0
T4A00 003:715.601 JLINK_WriteReg(R7, 0x00000000)
T4A00 003:715.605 - 0.004ms returns 0
T4A00 003:715.610 JLINK_WriteReg(R8, 0x00000000)
T4A00 003:715.613 - 0.003ms returns 0
T4A00 003:715.617 JLINK_WriteReg(R9, 0x2000045C)
T4A00 003:715.621 - 0.003ms returns 0
T4A00 003:715.625 JLINK_WriteReg(R10, 0x00000000)
T4A00 003:715.629 - 0.003ms returns 0
T4A00 003:715.633 JLINK_WriteReg(R11, 0x00000000)
T4A00 003:715.636 - 0.003ms returns 0
T4A00 003:715.640 JLINK_WriteReg(R12, 0x00000000)
T4A00 003:715.644 - 0.003ms returns 0
T4A00 003:715.648 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 003:715.653 - 0.004ms returns 0
T4A00 003:715.657 JLINK_WriteReg(R14, 0x20000001)
T4A00 003:715.661 - 0.003ms returns 0
T4A00 003:715.665 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 003:715.669 - 0.004ms returns 0
T4A00 003:715.673 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 003:715.677 - 0.003ms returns 0
T4A00 003:715.681 JLINK_WriteReg(MSP, 0x20001000)
T4A00 003:715.685 - 0.003ms returns 0
T4A00 003:715.689 JLINK_WriteReg(PSP, 0x20001000)
T4A00 003:715.692 - 0.003ms returns 0
T4A00 003:715.696 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 003:715.700 - 0.003ms returns 0
T4A00 003:715.705 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 003:715.712 - 0.007ms returns 0x0000001A
T4A00 003:715.716 JLINK_Go()
T4A00 003:715.729   CPU_ReadMem(4 bytes @ 0x********)
T4A00 003:739.396 - 23.678ms
T4A00 003:739.518 JLINK_IsHalted()
T4A00 003:742.031 - 2.512ms returns FALSE
T4A00 003:742.084 JLINK_HasError()
T4A00 003:759.184 JLINK_IsHalted()
T4A00 003:761.645 - 2.459ms returns FALSE
T4A00 003:761.695 JLINK_HasError()
T4A00 003:763.298 JLINK_IsHalted()
T4A00 003:765.518 - 2.220ms returns FALSE
T4A00 003:765.581 JLINK_HasError()
T4A00 003:767.298 JLINK_IsHalted()
T4A00 003:769.432 - 2.132ms returns FALSE
T4A00 003:769.508 JLINK_HasError()
T4A00 003:771.439 JLINK_IsHalted()
T4A00 003:773.774 - 2.334ms returns FALSE
T4A00 003:773.829 JLINK_HasError()
T4A00 003:775.290 JLINK_IsHalted()
T4A00 003:777.700 - 2.410ms returns FALSE
T4A00 003:777.734 JLINK_HasError()
T4A00 003:778.979 JLINK_IsHalted()
T4A00 003:781.474 - 2.494ms returns FALSE
T4A00 003:781.548 JLINK_HasError()
T4A00 003:783.546 JLINK_IsHalted()
T4A00 003:785.731 - 2.184ms returns FALSE
T4A00 003:785.766 JLINK_HasError()
T4A00 003:787.249 JLINK_IsHalted()
T4A00 003:789.642 - 2.392ms returns FALSE
T4A00 003:789.710 JLINK_HasError()
T4A00 003:791.602 JLINK_IsHalted()
T4A00 003:794.218 - 2.615ms returns FALSE
T4A00 003:794.255 JLINK_HasError()
T4A00 003:796.112 JLINK_IsHalted()
T4A00 003:798.310 - 2.198ms returns FALSE
T4A00 003:798.360 JLINK_HasError()
T4A00 003:799.594 JLINK_IsHalted()
T4A00 003:801.842 - 2.247ms returns FALSE
T4A00 003:801.879 JLINK_HasError()
T4A00 003:803.603 JLINK_IsHalted()
T4A00 003:805.956 - 2.352ms returns FALSE
T4A00 003:806.016 JLINK_HasError()
T4A00 003:807.898 JLINK_IsHalted()
T4A00 003:810.125 - 2.227ms returns FALSE
T4A00 003:810.146 JLINK_HasError()
T4A00 003:811.768 JLINK_IsHalted()
T4A00 003:813.947 - 2.178ms returns FALSE
T4A00 003:813.976 JLINK_HasError()
T4A00 003:815.624 JLINK_IsHalted()
T4A00 003:817.817 - 2.193ms returns FALSE
T4A00 003:817.854 JLINK_HasError()
T4A00 003:819.134 JLINK_IsHalted()
T4A00 003:821.242 - 2.108ms returns FALSE
T4A00 003:821.271 JLINK_HasError()
T4A00 003:822.740 JLINK_IsHalted()
T4A00 003:847.277   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 003:849.102 - 26.362ms returns TRUE
T4A00 003:849.144 JLINK_ReadReg(R15 (PC))
T4A00 003:849.158 - 0.013ms returns 0x20000000
T4A00 003:849.169 JLINK_ClrBPEx(BPHandle = 0x0000001A)
T4A00 003:849.178 - 0.009ms returns 0x00
T4A00 003:849.205 JLINK_ReadReg(R0)
T4A00 003:849.214 - 0.009ms returns 0x00000000
T4A00 003:850.094 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 003:850.113   Data:  FF F7 62 FF 64 14 00 00 84 14 00 00 80 B5 00 F0 ...
T4A00 003:850.136   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 003:877.820 - 27.727ms returns 0x200
T4A00 003:877.874 JLINK_HasError()
T4A00 003:877.890 JLINK_WriteReg(R0, 0x00000200)
T4A00 003:877.900 - 0.010ms returns 0
T4A00 003:877.904 JLINK_WriteReg(R1, 0x00000200)
T4A00 003:877.908 - 0.004ms returns 0
T4A00 003:877.913 JLINK_WriteReg(R2, 0x20000460)
T4A00 003:877.916 - 0.003ms returns 0
T4A00 003:877.921 JLINK_WriteReg(R3, 0x00000000)
T4A00 003:877.924 - 0.003ms returns 0
T4A00 003:877.929 JLINK_WriteReg(R4, 0x00000000)
T4A00 003:877.932 - 0.003ms returns 0
T4A00 003:877.936 JLINK_WriteReg(R5, 0x00000000)
T4A00 003:877.940 - 0.003ms returns 0
T4A00 003:877.944 JLINK_WriteReg(R6, 0x00000000)
T4A00 003:877.948 - 0.003ms returns 0
T4A00 003:877.952 JLINK_WriteReg(R7, 0x00000000)
T4A00 003:877.956 - 0.003ms returns 0
T4A00 003:877.960 JLINK_WriteReg(R8, 0x00000000)
T4A00 003:877.964 - 0.003ms returns 0
T4A00 003:877.968 JLINK_WriteReg(R9, 0x2000045C)
T4A00 003:877.971 - 0.003ms returns 0
T4A00 003:877.975 JLINK_WriteReg(R10, 0x00000000)
T4A00 003:877.979 - 0.003ms returns 0
T4A00 003:877.983 JLINK_WriteReg(R11, 0x00000000)
T4A00 003:877.987 - 0.003ms returns 0
T4A00 003:877.991 JLINK_WriteReg(R12, 0x00000000)
T4A00 003:877.994 - 0.003ms returns 0
T4A00 003:877.999 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 003:878.003 - 0.004ms returns 0
T4A00 003:878.007 JLINK_WriteReg(R14, 0x20000001)
T4A00 003:878.011 - 0.003ms returns 0
T4A00 003:878.015 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 003:878.019 - 0.003ms returns 0
T4A00 003:878.023 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 003:878.027 - 0.003ms returns 0
T4A00 003:878.031 JLINK_WriteReg(MSP, 0x20001000)
T4A00 003:878.035 - 0.003ms returns 0
T4A00 003:878.039 JLINK_WriteReg(PSP, 0x20001000)
T4A00 003:878.042 - 0.003ms returns 0
T4A00 003:878.047 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 003:878.050 - 0.003ms returns 0
T4A00 003:878.055 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 003:878.060 - 0.006ms returns 0x0000001B
T4A00 003:878.065 JLINK_Go()
T4A00 003:878.077   CPU_ReadMem(4 bytes @ 0x********)
T4A00 003:900.485 - 22.418ms
T4A00 003:900.524 JLINK_IsHalted()
T4A00 003:902.708 - 2.183ms returns FALSE
T4A00 003:902.726 JLINK_HasError()
T4A00 003:909.371 JLINK_IsHalted()
T4A00 003:911.358 - 1.986ms returns FALSE
T4A00 003:911.393 JLINK_HasError()
T4A00 003:913.366 JLINK_IsHalted()
T4A00 003:915.278 - 1.911ms returns FALSE
T4A00 003:915.297 JLINK_HasError()
T4A00 003:916.528 JLINK_IsHalted()
T4A00 003:918.488 - 1.958ms returns FALSE
T4A00 003:918.527 JLINK_HasError()
T4A00 003:919.714 JLINK_IsHalted()
T4A00 003:921.762 - 2.048ms returns FALSE
T4A00 003:921.792 JLINK_HasError()
T4A00 003:923.283 JLINK_IsHalted()
T4A00 003:925.233 - 1.950ms returns FALSE
T4A00 003:925.286 JLINK_HasError()
T4A00 003:926.533 JLINK_IsHalted()
T4A00 003:928.496 - 1.963ms returns FALSE
T4A00 003:928.521 JLINK_HasError()
T4A00 003:930.494 JLINK_IsHalted()
T4A00 003:932.391 - 1.897ms returns FALSE
T4A00 003:932.412 JLINK_HasError()
T4A00 003:934.384 JLINK_IsHalted()
T4A00 003:936.388 - 2.004ms returns FALSE
T4A00 003:936.427 JLINK_HasError()
T4A00 003:938.389 JLINK_IsHalted()
T4A00 003:940.316 - 1.926ms returns FALSE
T4A00 003:940.342 JLINK_HasError()
T4A00 003:941.392 JLINK_IsHalted()
T4A00 003:943.282 - 1.890ms returns FALSE
T4A00 003:943.313 JLINK_HasError()
T4A00 003:945.267 JLINK_IsHalted()
T4A00 003:947.186 - 1.918ms returns FALSE
T4A00 003:947.234 JLINK_HasError()
T4A00 003:948.401 JLINK_IsHalted()
T4A00 003:950.347 - 1.945ms returns FALSE
T4A00 003:950.418 JLINK_HasError()
T4A00 003:952.399 JLINK_IsHalted()
T4A00 003:954.272 - 1.873ms returns FALSE
T4A00 003:954.301 JLINK_HasError()
T4A00 003:955.396 JLINK_IsHalted()
T4A00 004:203.616   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 004:206.175 - 250.777ms returns TRUE
T4A00 004:206.247 JLINK_ReadReg(R15 (PC))
T4A00 004:206.257 - 0.009ms returns 0x20000000
T4A00 004:206.268 JLINK_ClrBPEx(BPHandle = 0x0000001B)
T4A00 004:206.273 - 0.004ms returns 0x00
T4A00 004:206.278 JLINK_ReadReg(R0)
T4A00 004:206.282 - 0.004ms returns 0x00000000
T4A00 004:208.307 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 004:208.317   Data:  A5 A5 00 00 80 88 70 47 00 48 70 47 08 00 00 20 ...
T4A00 004:208.331   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 004:236.086 - 27.778ms returns 0x200
T4A00 004:236.153 JLINK_HasError()
T4A00 004:236.166 JLINK_WriteReg(R0, 0x00000400)
T4A00 004:236.176 - 0.010ms returns 0
T4A00 004:236.182 JLINK_WriteReg(R1, 0x00000200)
T4A00 004:236.187 - 0.005ms returns 0
T4A00 004:236.193 JLINK_WriteReg(R2, 0x20000460)
T4A00 004:236.198 - 0.006ms returns 0
T4A00 004:236.209 JLINK_WriteReg(R3, 0x00000000)
T4A00 004:236.213 - 0.005ms returns 0
T4A00 004:236.219 JLINK_WriteReg(R4, 0x00000000)
T4A00 004:236.224 - 0.004ms returns 0
T4A00 004:236.229 JLINK_WriteReg(R5, 0x00000000)
T4A00 004:236.234 - 0.004ms returns 0
T4A00 004:236.240 JLINK_WriteReg(R6, 0x00000000)
T4A00 004:236.245 - 0.004ms returns 0
T4A00 004:236.250 JLINK_WriteReg(R7, 0x00000000)
T4A00 004:236.255 - 0.004ms returns 0
T4A00 004:236.261 JLINK_WriteReg(R8, 0x00000000)
T4A00 004:236.266 - 0.005ms returns 0
T4A00 004:236.273 JLINK_WriteReg(R9, 0x2000045C)
T4A00 004:236.277 - 0.004ms returns 0
T4A00 004:236.283 JLINK_WriteReg(R10, 0x00000000)
T4A00 004:236.289 - 0.005ms returns 0
T4A00 004:236.294 JLINK_WriteReg(R11, 0x00000000)
T4A00 004:236.299 - 0.004ms returns 0
T4A00 004:236.305 JLINK_WriteReg(R12, 0x00000000)
T4A00 004:236.309 - 0.005ms returns 0
T4A00 004:236.315 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 004:236.321 - 0.006ms returns 0
T4A00 004:236.327 JLINK_WriteReg(R14, 0x20000001)
T4A00 004:236.332 - 0.005ms returns 0
T4A00 004:236.338 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 004:236.343 - 0.005ms returns 0
T4A00 004:236.349 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 004:236.353 - 0.004ms returns 0
T4A00 004:236.359 JLINK_WriteReg(MSP, 0x20001000)
T4A00 004:236.364 - 0.004ms returns 0
T4A00 004:236.369 JLINK_WriteReg(PSP, 0x20001000)
T4A00 004:236.374 - 0.004ms returns 0
T4A00 004:236.380 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 004:236.385 - 0.004ms returns 0
T4A00 004:236.391 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 004:236.397 - 0.007ms returns 0x0000001C
T4A00 004:236.403 JLINK_Go()
T4A00 004:236.417   CPU_ReadMem(4 bytes @ 0x********)
T4A00 004:259.856 - 23.451ms
T4A00 004:259.932 JLINK_IsHalted()
T4A00 004:262.435 - 2.502ms returns FALSE
T4A00 004:262.504 JLINK_HasError()
T4A00 004:266.666 JLINK_IsHalted()
T4A00 004:268.791 - 2.123ms returns FALSE
T4A00 004:268.815 JLINK_HasError()
T4A00 004:270.734 JLINK_IsHalted()
T4A00 004:272.642 - 1.907ms returns FALSE
T4A00 004:272.663 JLINK_HasError()
T4A00 004:273.881 JLINK_IsHalted()
T4A00 004:275.893 - 2.006ms returns FALSE
T4A00 004:275.929 JLINK_HasError()
T4A00 004:277.675 JLINK_IsHalted()
T4A00 004:279.608 - 1.931ms returns FALSE
T4A00 004:279.677 JLINK_HasError()
T4A00 004:280.965 JLINK_IsHalted()
T4A00 004:283.149 - 2.184ms returns FALSE
T4A00 004:283.171 JLINK_HasError()
T4A00 004:286.707 JLINK_IsHalted()
T4A00 004:288.704 - 1.995ms returns FALSE
T4A00 004:288.744 JLINK_HasError()
T4A00 004:290.667 JLINK_IsHalted()
T4A00 004:292.583 - 1.915ms returns FALSE
T4A00 004:292.627 JLINK_HasError()
T4A00 004:293.712 JLINK_IsHalted()
T4A00 004:295.686 - 1.973ms returns FALSE
T4A00 004:295.729 JLINK_HasError()
T4A00 004:297.745 JLINK_IsHalted()
T4A00 004:299.773 - 2.026ms returns FALSE
T4A00 004:299.866 JLINK_HasError()
T4A00 004:301.073 JLINK_IsHalted()
T4A00 004:302.947 - 1.873ms returns FALSE
T4A00 004:302.978 JLINK_HasError()
T4A00 004:304.724 JLINK_IsHalted()
T4A00 004:306.691 - 1.966ms returns FALSE
T4A00 004:306.737 JLINK_HasError()
T4A00 004:308.713 JLINK_IsHalted()
T4A00 004:310.624 - 1.910ms returns FALSE
T4A00 004:310.646 JLINK_HasError()
T4A00 004:312.592 JLINK_IsHalted()
T4A00 004:314.661 - 2.069ms returns FALSE
T4A00 004:314.693 JLINK_HasError()
T4A00 004:316.702 JLINK_IsHalted()
T4A00 004:318.645 - 1.942ms returns FALSE
T4A00 004:318.685 JLINK_HasError()
T4A00 004:319.736 JLINK_IsHalted()
T4A00 004:321.651 - 1.914ms returns FALSE
T4A00 004:321.681 JLINK_HasError()
T4A00 004:323.616 JLINK_IsHalted()
T4A00 004:325.561 - 1.944ms returns FALSE
T4A00 004:325.595 JLINK_HasError()
T4A00 004:326.714 JLINK_IsHalted()
T4A00 004:328.649 - 1.935ms returns FALSE
T4A00 004:328.683 JLINK_HasError()
T4A00 004:330.253 JLINK_IsHalted()
T4A00 004:332.243 - 1.990ms returns FALSE
T4A00 004:332.274 JLINK_HasError()
T4A00 004:334.995 JLINK_IsHalted()
T4A00 004:337.006 - 2.009ms returns FALSE
T4A00 004:337.053 JLINK_HasError()
T4A00 004:338.997 JLINK_IsHalted()
T4A00 004:341.000 - 2.000ms returns FALSE
T4A00 004:341.024 JLINK_HasError()
T4A00 004:342.963 JLINK_IsHalted()
T4A00 004:367.037   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 004:368.857 - 25.893ms returns TRUE
T4A00 004:368.903 JLINK_ReadReg(R15 (PC))
T4A00 004:368.914 - 0.011ms returns 0x20000000
T4A00 004:368.928 JLINK_ClrBPEx(BPHandle = 0x0000001C)
T4A00 004:368.936 - 0.007ms returns 0x00
T4A00 004:368.945 JLINK_ReadReg(R0)
T4A00 004:368.951 - 0.006ms returns 0x00000000
T4A00 004:369.609 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 004:369.632   Data:  00 21 16 91 40 20 15 90 04 A8 14 90 03 91 17 91 ...
T4A00 004:369.650   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 004:397.281 - 27.671ms returns 0x200
T4A00 004:397.363 JLINK_HasError()
T4A00 004:397.380 JLINK_WriteReg(R0, 0x00000600)
T4A00 004:397.392 - 0.011ms returns 0
T4A00 004:397.401 JLINK_WriteReg(R1, 0x00000200)
T4A00 004:397.409 - 0.007ms returns 0
T4A00 004:397.417 JLINK_WriteReg(R2, 0x20000460)
T4A00 004:397.424 - 0.007ms returns 0
T4A00 004:397.433 JLINK_WriteReg(R3, 0x00000000)
T4A00 004:397.440 - 0.007ms returns 0
T4A00 004:397.448 JLINK_WriteReg(R4, 0x00000000)
T4A00 004:397.455 - 0.007ms returns 0
T4A00 004:397.464 JLINK_WriteReg(R5, 0x00000000)
T4A00 004:397.471 - 0.007ms returns 0
T4A00 004:397.479 JLINK_WriteReg(R6, 0x00000000)
T4A00 004:397.486 - 0.007ms returns 0
T4A00 004:397.495 JLINK_WriteReg(R7, 0x00000000)
T4A00 004:397.502 - 0.007ms returns 0
T4A00 004:397.510 JLINK_WriteReg(R8, 0x00000000)
T4A00 004:397.517 - 0.007ms returns 0
T4A00 004:397.526 JLINK_WriteReg(R9, 0x2000045C)
T4A00 004:397.533 - 0.007ms returns 0
T4A00 004:397.541 JLINK_WriteReg(R10, 0x00000000)
T4A00 004:397.549 - 0.007ms returns 0
T4A00 004:397.557 JLINK_WriteReg(R11, 0x00000000)
T4A00 004:397.564 - 0.007ms returns 0
T4A00 004:397.572 JLINK_WriteReg(R12, 0x00000000)
T4A00 004:397.579 - 0.007ms returns 0
T4A00 004:397.588 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 004:397.596 - 0.008ms returns 0
T4A00 004:397.604 JLINK_WriteReg(R14, 0x20000001)
T4A00 004:397.612 - 0.007ms returns 0
T4A00 004:397.620 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 004:397.627 - 0.007ms returns 0
T4A00 004:397.636 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 004:397.643 - 0.007ms returns 0
T4A00 004:397.651 JLINK_WriteReg(MSP, 0x20001000)
T4A00 004:397.659 - 0.007ms returns 0
T4A00 004:397.667 JLINK_WriteReg(PSP, 0x20001000)
T4A00 004:397.674 - 0.007ms returns 0
T4A00 004:397.682 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 004:397.689 - 0.007ms returns 0
T4A00 004:397.704 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 004:397.713 - 0.009ms returns 0x0000001D
T4A00 004:397.721 JLINK_Go()
T4A00 004:397.737   CPU_ReadMem(4 bytes @ 0x********)
T4A00 004:420.097 - 22.374ms
T4A00 004:420.141 JLINK_IsHalted()
T4A00 004:422.338 - 2.197ms returns FALSE
T4A00 004:422.371 JLINK_HasError()
T4A00 004:428.671 JLINK_IsHalted()
T4A00 004:671.479   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 004:673.502 - 244.830ms returns TRUE
T4A00 004:673.539 JLINK_ReadReg(R15 (PC))
T4A00 004:673.548 - 0.008ms returns 0x20000000
T4A00 004:673.553 JLINK_ClrBPEx(BPHandle = 0x0000001D)
T4A00 004:673.557 - 0.004ms returns 0x00
T4A00 004:673.567 JLINK_ReadReg(R0)
T4A00 004:673.571 - 0.004ms returns 0x00000000
T4A00 004:675.475 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 004:675.485   Data:  FF E7 FF E7 FF E7 FF E7 FF E7 FF E7 FF E7 FF E7 ...
T4A00 004:675.498   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 004:703.272 - 27.794ms returns 0x200
T4A00 004:703.367 JLINK_HasError()
T4A00 004:703.382 JLINK_WriteReg(R0, 0x00000800)
T4A00 004:703.396 - 0.014ms returns 0
T4A00 004:703.408 JLINK_WriteReg(R1, 0x00000200)
T4A00 004:703.417 - 0.009ms returns 0
T4A00 004:703.427 JLINK_WriteReg(R2, 0x20000460)
T4A00 004:703.437 - 0.009ms returns 0
T4A00 004:703.449 JLINK_WriteReg(R3, 0x00000000)
T4A00 004:703.458 - 0.009ms returns 0
T4A00 004:703.476 JLINK_WriteReg(R4, 0x00000000)
T4A00 004:703.485 - 0.009ms returns 0
T4A00 004:703.495 JLINK_WriteReg(R5, 0x00000000)
T4A00 004:703.504 - 0.009ms returns 0
T4A00 004:703.514 JLINK_WriteReg(R6, 0x00000000)
T4A00 004:703.523 - 0.008ms returns 0
T4A00 004:703.533 JLINK_WriteReg(R7, 0x00000000)
T4A00 004:703.542 - 0.009ms returns 0
T4A00 004:703.552 JLINK_WriteReg(R8, 0x00000000)
T4A00 004:703.561 - 0.008ms returns 0
T4A00 004:703.571 JLINK_WriteReg(R9, 0x2000045C)
T4A00 004:703.580 - 0.008ms returns 0
T4A00 004:703.589 JLINK_WriteReg(R10, 0x00000000)
T4A00 004:703.598 - 0.009ms returns 0
T4A00 004:703.609 JLINK_WriteReg(R11, 0x00000000)
T4A00 004:703.617 - 0.008ms returns 0
T4A00 004:703.627 JLINK_WriteReg(R12, 0x00000000)
T4A00 004:703.636 - 0.008ms returns 0
T4A00 004:703.646 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 004:703.656 - 0.010ms returns 0
T4A00 004:703.666 JLINK_WriteReg(R14, 0x20000001)
T4A00 004:703.675 - 0.008ms returns 0
T4A00 004:703.685 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 004:703.694 - 0.008ms returns 0
T4A00 004:703.705 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 004:703.713 - 0.009ms returns 0
T4A00 004:703.723 JLINK_WriteReg(MSP, 0x20001000)
T4A00 004:703.733 - 0.009ms returns 0
T4A00 004:703.743 JLINK_WriteReg(PSP, 0x20001000)
T4A00 004:703.751 - 0.008ms returns 0
T4A00 004:703.761 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 004:703.770 - 0.008ms returns 0
T4A00 004:703.782 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 004:703.793 - 0.012ms returns 0x0000001E
T4A00 004:703.804 JLINK_Go()
T4A00 004:703.826   CPU_ReadMem(4 bytes @ 0x********)
T4A00 004:726.853 - 23.047ms
T4A00 004:726.920 JLINK_IsHalted()
T4A00 004:729.199 - 2.278ms returns FALSE
T4A00 004:729.262 JLINK_HasError()
T4A00 004:734.079 JLINK_IsHalted()
T4A00 004:736.163 - 2.083ms returns FALSE
T4A00 004:736.230 JLINK_HasError()
T4A00 004:737.521 JLINK_IsHalted()
T4A00 004:739.610 - 2.088ms returns FALSE
T4A00 004:739.669 JLINK_HasError()
T4A00 004:741.102 JLINK_IsHalted()
T4A00 004:743.339 - 2.236ms returns FALSE
T4A00 004:743.379 JLINK_HasError()
T4A00 004:745.092 JLINK_IsHalted()
T4A00 004:747.399 - 2.306ms returns FALSE
T4A00 004:747.439 JLINK_HasError()
T4A00 004:749.257 JLINK_IsHalted()
T4A00 004:751.493 - 2.235ms returns FALSE
T4A00 004:751.530 JLINK_HasError()
T4A00 004:753.330 JLINK_IsHalted()
T4A00 004:755.585 - 2.255ms returns FALSE
T4A00 004:755.624 JLINK_HasError()
T4A00 004:757.330 JLINK_IsHalted()
T4A00 004:759.515 - 2.185ms returns FALSE
T4A00 004:759.552 JLINK_HasError()
T4A00 004:761.429 JLINK_IsHalted()
T4A00 004:763.978 - 2.549ms returns FALSE
T4A00 004:764.017 JLINK_HasError()
T4A00 004:765.353 JLINK_IsHalted()
T4A00 004:767.471 - 2.118ms returns FALSE
T4A00 004:767.511 JLINK_HasError()
T4A00 004:768.681 JLINK_IsHalted()
T4A00 004:770.689 - 2.008ms returns FALSE
T4A00 004:770.716 JLINK_HasError()
T4A00 004:772.449 JLINK_IsHalted()
T4A00 004:774.374 - 1.923ms returns FALSE
T4A00 004:774.415 JLINK_HasError()
T4A00 004:775.761 JLINK_IsHalted()
T4A00 004:777.705 - 1.942ms returns FALSE
T4A00 004:777.733 JLINK_HasError()
T4A00 004:779.760 JLINK_IsHalted()
T4A00 004:781.812 - 2.051ms returns FALSE
T4A00 004:781.850 JLINK_HasError()
T4A00 004:782.975 JLINK_IsHalted()
T4A00 004:784.867 - 1.892ms returns FALSE
T4A00 004:784.895 JLINK_HasError()
T4A00 004:786.863 JLINK_IsHalted()
T4A00 004:788.811 - 1.947ms returns FALSE
T4A00 004:788.845 JLINK_HasError()
T4A00 004:790.081 JLINK_IsHalted()
T4A00 004:791.958 - 1.877ms returns FALSE
T4A00 004:791.981 JLINK_HasError()
T4A00 004:793.080 JLINK_IsHalted()
T4A00 004:794.937 - 1.856ms returns FALSE
T4A00 004:794.969 JLINK_HasError()
T4A00 004:796.766 JLINK_IsHalted()
T4A00 004:799.041 - 2.273ms returns FALSE
T4A00 004:799.130 JLINK_HasError()
T4A00 004:800.765 JLINK_IsHalted()
T4A00 004:802.732 - 1.967ms returns FALSE
T4A00 004:802.757 JLINK_HasError()
T4A00 004:804.785 JLINK_IsHalted()
T4A00 004:806.729 - 1.944ms returns FALSE
T4A00 004:806.759 JLINK_HasError()
T4A00 004:808.773 JLINK_IsHalted()
T4A00 004:833.038   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 004:834.920 - 26.146ms returns TRUE
T4A00 004:834.961 JLINK_ReadReg(R15 (PC))
T4A00 004:834.973 - 0.012ms returns 0x20000000
T4A00 004:834.983 JLINK_ClrBPEx(BPHandle = 0x0000001E)
T4A00 004:834.991 - 0.008ms returns 0x00
T4A00 004:835.000 JLINK_ReadReg(R0)
T4A00 004:835.007 - 0.007ms returns 0x00000000
T4A00 004:835.744 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 004:835.759   Data:  00 20 00 2C 56 D0 71 69 00 2C 53 D0 01 22 12 03 ...
T4A00 004:835.776   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 004:863.484 - 27.738ms returns 0x200
T4A00 004:863.581 JLINK_HasError()
T4A00 004:863.603 JLINK_WriteReg(R0, 0x00000A00)
T4A00 004:863.622 - 0.019ms returns 0
T4A00 004:863.633 JLINK_WriteReg(R1, 0x00000200)
T4A00 004:863.643 - 0.009ms returns 0
T4A00 004:863.653 JLINK_WriteReg(R2, 0x20000460)
T4A00 004:863.663 - 0.009ms returns 0
T4A00 004:863.673 JLINK_WriteReg(R3, 0x00000000)
T4A00 004:863.682 - 0.009ms returns 0
T4A00 004:863.693 JLINK_WriteReg(R4, 0x00000000)
T4A00 004:863.702 - 0.009ms returns 0
T4A00 004:863.713 JLINK_WriteReg(R5, 0x00000000)
T4A00 004:863.722 - 0.009ms returns 0
T4A00 004:863.732 JLINK_WriteReg(R6, 0x00000000)
T4A00 004:863.741 - 0.009ms returns 0
T4A00 004:863.752 JLINK_WriteReg(R7, 0x00000000)
T4A00 004:863.761 - 0.009ms returns 0
T4A00 004:863.772 JLINK_WriteReg(R8, 0x00000000)
T4A00 004:863.781 - 0.009ms returns 0
T4A00 004:863.791 JLINK_WriteReg(R9, 0x2000045C)
T4A00 004:863.801 - 0.009ms returns 0
T4A00 004:863.811 JLINK_WriteReg(R10, 0x00000000)
T4A00 004:863.820 - 0.009ms returns 0
T4A00 004:863.831 JLINK_WriteReg(R11, 0x00000000)
T4A00 004:863.840 - 0.009ms returns 0
T4A00 004:863.850 JLINK_WriteReg(R12, 0x00000000)
T4A00 004:863.859 - 0.009ms returns 0
T4A00 004:863.870 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 004:863.881 - 0.010ms returns 0
T4A00 004:863.891 JLINK_WriteReg(R14, 0x20000001)
T4A00 004:863.901 - 0.009ms returns 0
T4A00 004:863.914 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 004:863.927 - 0.013ms returns 0
T4A00 004:863.940 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 004:863.952 - 0.012ms returns 0
T4A00 004:863.965 JLINK_WriteReg(MSP, 0x20001000)
T4A00 004:863.975 - 0.009ms returns 0
T4A00 004:863.986 JLINK_WriteReg(PSP, 0x20001000)
T4A00 004:863.995 - 0.009ms returns 0
T4A00 004:864.005 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 004:864.014 - 0.009ms returns 0
T4A00 004:864.027 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 004:864.038 - 0.012ms returns 0x0000001F
T4A00 004:864.049 JLINK_Go()
T4A00 004:864.071   CPU_ReadMem(4 bytes @ 0x********)
T4A00 004:887.015 - 22.963ms
T4A00 004:887.111 JLINK_IsHalted()
T4A00 004:889.631 - 2.519ms returns FALSE
T4A00 004:889.673 JLINK_HasError()
T4A00 004:898.314 JLINK_IsHalted()
T4A00 005:139.008   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 005:141.397 - 243.082ms returns TRUE
T4A00 005:141.447 JLINK_ReadReg(R15 (PC))
T4A00 005:141.457 - 0.010ms returns 0x20000000
T4A00 005:141.463 JLINK_ClrBPEx(BPHandle = 0x0000001F)
T4A00 005:141.469 - 0.005ms returns 0x00
T4A00 005:141.477 JLINK_ReadReg(R0)
T4A00 005:141.483 - 0.006ms returns 0x00000000
T4A00 005:142.904 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 005:142.917   Data:  C8 40 01 B0 F0 BD C0 46 DC 1B 10 00 80 8D 5B 00 ...
T4A00 005:142.932   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 005:170.943 - 28.038ms returns 0x200
T4A00 005:171.026 JLINK_HasError()
T4A00 005:171.049 JLINK_WriteReg(R0, 0x00000C00)
T4A00 005:171.058 - 0.009ms returns 0
T4A00 005:171.064 JLINK_WriteReg(R1, 0x00000200)
T4A00 005:171.069 - 0.005ms returns 0
T4A00 005:171.079 JLINK_WriteReg(R2, 0x20000460)
T4A00 005:171.085 - 0.006ms returns 0
T4A00 005:171.091 JLINK_WriteReg(R3, 0x00000000)
T4A00 005:171.096 - 0.005ms returns 0
T4A00 005:171.102 JLINK_WriteReg(R4, 0x00000000)
T4A00 005:171.107 - 0.004ms returns 0
T4A00 005:171.113 JLINK_WriteReg(R5, 0x00000000)
T4A00 005:171.118 - 0.005ms returns 0
T4A00 005:171.124 JLINK_WriteReg(R6, 0x00000000)
T4A00 005:171.129 - 0.005ms returns 0
T4A00 005:171.137 JLINK_WriteReg(R7, 0x00000000)
T4A00 005:171.143 - 0.005ms returns 0
T4A00 005:171.149 JLINK_WriteReg(R8, 0x00000000)
T4A00 005:171.155 - 0.006ms returns 0
T4A00 005:171.160 JLINK_WriteReg(R9, 0x2000045C)
T4A00 005:171.163 - 0.003ms returns 0
T4A00 005:171.168 JLINK_WriteReg(R10, 0x00000000)
T4A00 005:171.172 - 0.003ms returns 0
T4A00 005:171.176 JLINK_WriteReg(R11, 0x00000000)
T4A00 005:171.180 - 0.004ms returns 0
T4A00 005:171.184 JLINK_WriteReg(R12, 0x00000000)
T4A00 005:171.188 - 0.003ms returns 0
T4A00 005:171.193 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 005:171.197 - 0.005ms returns 0
T4A00 005:171.202 JLINK_WriteReg(R14, 0x20000001)
T4A00 005:171.207 - 0.005ms returns 0
T4A00 005:171.215 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 005:171.221 - 0.006ms returns 0
T4A00 005:171.227 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 005:171.234 - 0.006ms returns 0
T4A00 005:171.240 JLINK_WriteReg(MSP, 0x20001000)
T4A00 005:171.245 - 0.005ms returns 0
T4A00 005:171.250 JLINK_WriteReg(PSP, 0x20001000)
T4A00 005:171.257 - 0.006ms returns 0
T4A00 005:171.264 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 005:171.269 - 0.005ms returns 0
T4A00 005:171.278 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 005:171.285 - 0.007ms returns 0x00000020
T4A00 005:171.293 JLINK_Go()
T4A00 005:171.309   CPU_ReadMem(4 bytes @ 0x********)
T4A00 005:195.125 - 23.831ms
T4A00 005:195.190 JLINK_IsHalted()
T4A00 005:197.608 - 2.417ms returns FALSE
T4A00 005:197.692 JLINK_HasError()
T4A00 005:201.691 JLINK_IsHalted()
T4A00 005:204.067 - 2.374ms returns FALSE
T4A00 005:204.121 JLINK_HasError()
T4A00 005:205.812 JLINK_IsHalted()
T4A00 005:208.487 - 2.673ms returns FALSE
T4A00 005:208.550 JLINK_HasError()
T4A00 005:210.585 JLINK_IsHalted()
T4A00 005:213.511 - 2.925ms returns FALSE
T4A00 005:213.574 JLINK_HasError()
T4A00 005:214.678 JLINK_IsHalted()
T4A00 005:217.381 - 2.702ms returns FALSE
T4A00 005:217.455 JLINK_HasError()
T4A00 005:218.902 JLINK_IsHalted()
T4A00 005:221.685 - 2.782ms returns FALSE
T4A00 005:221.743 JLINK_HasError()
T4A00 005:223.006 JLINK_IsHalted()
T4A00 005:225.953 - 2.946ms returns FALSE
T4A00 005:226.018 JLINK_HasError()
T4A00 005:228.019 JLINK_IsHalted()
T4A00 005:231.151 - 3.131ms returns FALSE
T4A00 005:231.221 JLINK_HasError()
T4A00 005:232.827 JLINK_IsHalted()
T4A00 005:235.014 - 2.185ms returns FALSE
T4A00 005:235.097 JLINK_HasError()
T4A00 005:236.998 JLINK_IsHalted()
T4A00 005:239.182 - 2.182ms returns FALSE
T4A00 005:239.237 JLINK_HasError()
T4A00 005:241.005 JLINK_IsHalted()
T4A00 005:243.325 - 2.318ms returns FALSE
T4A00 005:243.419 JLINK_HasError()
T4A00 005:245.021 JLINK_IsHalted()
T4A00 005:247.619 - 2.598ms returns FALSE
T4A00 005:248.407 JLINK_HasError()
T4A00 005:249.776 JLINK_IsHalted()
T4A00 005:252.232 - 2.455ms returns FALSE
T4A00 005:252.295 JLINK_HasError()
T4A00 005:254.073 JLINK_IsHalted()
T4A00 005:257.254 - 3.181ms returns FALSE
T4A00 005:257.334 JLINK_HasError()
T4A00 005:258.512 JLINK_IsHalted()
T4A00 005:261.604 - 3.091ms returns FALSE
T4A00 005:261.685 JLINK_HasError()
T4A00 005:263.977 JLINK_IsHalted()
T4A00 005:266.643 - 2.666ms returns FALSE
T4A00 005:266.681 JLINK_HasError()
T4A00 005:267.958 JLINK_IsHalted()
T4A00 005:270.431 - 2.472ms returns FALSE
T4A00 005:270.472 JLINK_HasError()
T4A00 005:272.328 JLINK_IsHalted()
T4A00 005:275.010 - 2.681ms returns FALSE
T4A00 005:275.089 JLINK_HasError()
T4A00 005:276.442 JLINK_IsHalted()
T4A00 005:302.323   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 005:304.872 - 28.430ms returns TRUE
T4A00 005:304.942 JLINK_ReadReg(R15 (PC))
T4A00 005:304.951 - 0.009ms returns 0x20000000
T4A00 005:304.956 JLINK_ClrBPEx(BPHandle = 0x00000020)
T4A00 005:304.961 - 0.004ms returns 0x00
T4A00 005:304.966 JLINK_ReadReg(R0)
T4A00 005:304.969 - 0.003ms returns 0x00000000
T4A00 005:305.479 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 005:305.487   Data:  1B 68 83 42 03 D0 8B 42 F9 D1 08 68 10 60 70 47 ...
T4A00 005:305.501   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 005:334.205 - 28.724ms returns 0x200
T4A00 005:334.278 JLINK_HasError()
T4A00 005:334.289 JLINK_WriteReg(R0, 0x00000E00)
T4A00 005:334.300 - 0.010ms returns 0
T4A00 005:334.305 JLINK_WriteReg(R1, 0x00000200)
T4A00 005:334.310 - 0.004ms returns 0
T4A00 005:334.315 JLINK_WriteReg(R2, 0x20000460)
T4A00 005:334.320 - 0.004ms returns 0
T4A00 005:334.325 JLINK_WriteReg(R3, 0x00000000)
T4A00 005:334.330 - 0.004ms returns 0
T4A00 005:334.335 JLINK_WriteReg(R4, 0x00000000)
T4A00 005:334.340 - 0.004ms returns 0
T4A00 005:334.353 JLINK_WriteReg(R5, 0x00000000)
T4A00 005:334.358 - 0.005ms returns 0
T4A00 005:334.364 JLINK_WriteReg(R6, 0x00000000)
T4A00 005:334.369 - 0.004ms returns 0
T4A00 005:334.374 JLINK_WriteReg(R7, 0x00000000)
T4A00 005:334.379 - 0.004ms returns 0
T4A00 005:334.385 JLINK_WriteReg(R8, 0x00000000)
T4A00 005:334.389 - 0.004ms returns 0
T4A00 005:334.395 JLINK_WriteReg(R9, 0x2000045C)
T4A00 005:334.399 - 0.004ms returns 0
T4A00 005:334.405 JLINK_WriteReg(R10, 0x00000000)
T4A00 005:334.409 - 0.004ms returns 0
T4A00 005:334.415 JLINK_WriteReg(R11, 0x00000000)
T4A00 005:334.419 - 0.004ms returns 0
T4A00 005:334.425 JLINK_WriteReg(R12, 0x00000000)
T4A00 005:334.429 - 0.004ms returns 0
T4A00 005:334.435 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 005:334.440 - 0.005ms returns 0
T4A00 005:334.445 JLINK_WriteReg(R14, 0x20000001)
T4A00 005:334.450 - 0.004ms returns 0
T4A00 005:334.456 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 005:334.461 - 0.005ms returns 0
T4A00 005:334.472 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 005:334.477 - 0.005ms returns 0
T4A00 005:334.482 JLINK_WriteReg(MSP, 0x20001000)
T4A00 005:334.487 - 0.004ms returns 0
T4A00 005:334.492 JLINK_WriteReg(PSP, 0x20001000)
T4A00 005:334.497 - 0.004ms returns 0
T4A00 005:334.502 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 005:334.507 - 0.004ms returns 0
T4A00 005:334.513 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 005:334.520 - 0.007ms returns 0x00000021
T4A00 005:334.525 JLINK_Go()
T4A00 005:334.538   CPU_ReadMem(4 bytes @ 0x********)
T4A00 005:358.526 - 23.999ms
T4A00 005:358.577 JLINK_IsHalted()
T4A00 005:360.721 - 2.143ms returns FALSE
T4A00 005:360.745 JLINK_HasError()
T4A00 005:364.338 JLINK_IsHalted()
T4A00 005:367.347 - 3.007ms returns FALSE
T4A00 005:367.427 JLINK_HasError()
T4A00 005:368.652 JLINK_IsHalted()
T4A00 005:371.601 - 2.948ms returns FALSE
T4A00 005:371.680 JLINK_HasError()
T4A00 005:373.621 JLINK_IsHalted()
T4A00 005:621.557   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 005:623.829 - 250.207ms returns TRUE
T4A00 005:623.910 JLINK_ReadReg(R15 (PC))
T4A00 005:623.925 - 0.016ms returns 0x20000000
T4A00 005:623.933 JLINK_ClrBPEx(BPHandle = 0x00000021)
T4A00 005:623.939 - 0.006ms returns 0x00
T4A00 005:623.945 JLINK_ReadReg(R0)
T4A00 005:623.950 - 0.005ms returns 0x00000000
T4A00 005:624.544 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 005:624.557   Data:  1B D5 2B 21 16 E0 04 98 C0 07 F1 D1 00 2F EF D0 ...
T4A00 005:624.572   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 005:653.165 - 28.619ms returns 0x200
T4A00 005:653.299 JLINK_HasError()
T4A00 005:653.313 JLINK_WriteReg(R0, 0x00001000)
T4A00 005:653.322 - 0.008ms returns 0
T4A00 005:653.327 JLINK_WriteReg(R1, 0x00000200)
T4A00 005:653.330 - 0.003ms returns 0
T4A00 005:653.335 JLINK_WriteReg(R2, 0x20000460)
T4A00 005:653.338 - 0.003ms returns 0
T4A00 005:653.343 JLINK_WriteReg(R3, 0x00000000)
T4A00 005:653.346 - 0.003ms returns 0
T4A00 005:653.351 JLINK_WriteReg(R4, 0x00000000)
T4A00 005:653.354 - 0.003ms returns 0
T4A00 005:653.359 JLINK_WriteReg(R5, 0x00000000)
T4A00 005:653.362 - 0.003ms returns 0
T4A00 005:653.375 JLINK_WriteReg(R6, 0x00000000)
T4A00 005:653.380 - 0.004ms returns 0
T4A00 005:653.384 JLINK_WriteReg(R7, 0x00000000)
T4A00 005:653.388 - 0.003ms returns 0
T4A00 005:653.392 JLINK_WriteReg(R8, 0x00000000)
T4A00 005:653.396 - 0.003ms returns 0
T4A00 005:653.400 JLINK_WriteReg(R9, 0x2000045C)
T4A00 005:653.404 - 0.003ms returns 0
T4A00 005:653.408 JLINK_WriteReg(R10, 0x00000000)
T4A00 005:653.412 - 0.003ms returns 0
T4A00 005:653.417 JLINK_WriteReg(R11, 0x00000000)
T4A00 005:653.422 - 0.004ms returns 0
T4A00 005:653.426 JLINK_WriteReg(R12, 0x00000000)
T4A00 005:653.431 - 0.004ms returns 0
T4A00 005:653.436 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 005:653.442 - 0.005ms returns 0
T4A00 005:653.447 JLINK_WriteReg(R14, 0x20000001)
T4A00 005:653.451 - 0.004ms returns 0
T4A00 005:653.457 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 005:653.461 - 0.004ms returns 0
T4A00 005:653.466 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 005:653.470 - 0.004ms returns 0
T4A00 005:653.475 JLINK_WriteReg(MSP, 0x20001000)
T4A00 005:653.479 - 0.004ms returns 0
T4A00 005:653.484 JLINK_WriteReg(PSP, 0x20001000)
T4A00 005:653.489 - 0.004ms returns 0
T4A00 005:653.494 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 005:653.498 - 0.004ms returns 0
T4A00 005:653.504 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 005:653.511 - 0.007ms returns 0x00000022
T4A00 005:653.516 JLINK_Go()
T4A00 005:653.533   CPU_ReadMem(4 bytes @ 0x********)
T4A00 005:677.965 - 24.448ms
T4A00 005:678.044 JLINK_IsHalted()
T4A00 005:680.970 - 2.925ms returns FALSE
T4A00 005:681.053 JLINK_HasError()
T4A00 005:701.482 JLINK_IsHalted()
T4A00 005:704.326 - 2.843ms returns FALSE
T4A00 005:704.392 JLINK_HasError()
T4A00 005:706.411 JLINK_IsHalted()
T4A00 005:709.196 - 2.784ms returns FALSE
T4A00 005:709.267 JLINK_HasError()
T4A00 005:711.017 JLINK_IsHalted()
T4A00 005:713.605 - 2.587ms returns FALSE
T4A00 005:713.669 JLINK_HasError()
T4A00 005:714.872 JLINK_IsHalted()
T4A00 005:717.472 - 2.600ms returns FALSE
T4A00 005:717.541 JLINK_HasError()
T4A00 005:720.933 JLINK_IsHalted()
T4A00 005:723.704 - 2.769ms returns FALSE
T4A00 005:723.791 JLINK_HasError()
T4A00 005:725.719 JLINK_IsHalted()
T4A00 005:728.787 - 3.067ms returns FALSE
T4A00 005:728.876 JLINK_HasError()
T4A00 005:730.164 JLINK_IsHalted()
T4A00 005:732.649 - 2.486ms returns FALSE
T4A00 005:732.692 JLINK_HasError()
T4A00 005:734.446 JLINK_IsHalted()
T4A00 005:736.933 - 2.485ms returns FALSE
T4A00 005:737.020 JLINK_HasError()
T4A00 005:738.204 JLINK_IsHalted()
T4A00 005:741.347 - 3.142ms returns FALSE
T4A00 005:741.431 JLINK_HasError()
T4A00 005:742.596 JLINK_IsHalted()
T4A00 005:745.592 - 2.993ms returns FALSE
T4A00 005:745.728 JLINK_HasError()
T4A00 005:747.551 JLINK_IsHalted()
T4A00 005:749.947 - 2.394ms returns FALSE
T4A00 005:749.998 JLINK_HasError()
T4A00 005:751.625 JLINK_IsHalted()
T4A00 005:753.797 - 2.173ms returns FALSE
T4A00 005:753.824 JLINK_HasError()
T4A00 005:755.777 JLINK_IsHalted()
T4A00 005:759.228 - 3.449ms returns FALSE
T4A00 005:759.319 JLINK_HasError()
T4A00 005:761.351 JLINK_IsHalted()
T4A00 005:786.111   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 005:788.424 - 27.072ms returns TRUE
T4A00 005:788.465 JLINK_ReadReg(R15 (PC))
T4A00 005:788.476 - 0.011ms returns 0x20000000
T4A00 005:788.484 JLINK_ClrBPEx(BPHandle = 0x00000022)
T4A00 005:788.491 - 0.007ms returns 0x00
T4A00 005:788.498 JLINK_ReadReg(R0)
T4A00 005:788.505 - 0.006ms returns 0x00000000
T4A00 005:789.176 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 005:789.190   Data:  A5 A5 00 00 F0 B5 83 B0 16 46 0D 46 C7 68 00 21 ...
T4A00 005:789.206   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 005:817.387 - 28.209ms returns 0x200
T4A00 005:817.501 JLINK_HasError()
T4A00 005:817.513 JLINK_WriteReg(R0, 0x00001200)
T4A00 005:817.525 - 0.011ms returns 0
T4A00 005:817.533 JLINK_WriteReg(R1, 0x00000200)
T4A00 005:817.539 - 0.006ms returns 0
T4A00 005:817.545 JLINK_WriteReg(R2, 0x20000460)
T4A00 005:817.552 - 0.006ms returns 0
T4A00 005:817.562 JLINK_WriteReg(R3, 0x00000000)
T4A00 005:817.570 - 0.008ms returns 0
T4A00 005:817.590 JLINK_WriteReg(R4, 0x00000000)
T4A00 005:817.598 - 0.007ms returns 0
T4A00 005:817.608 JLINK_WriteReg(R5, 0x00000000)
T4A00 005:817.615 - 0.007ms returns 0
T4A00 005:817.622 JLINK_WriteReg(R6, 0x00000000)
T4A00 005:817.628 - 0.005ms returns 0
T4A00 005:817.635 JLINK_WriteReg(R7, 0x00000000)
T4A00 005:817.641 - 0.005ms returns 0
T4A00 005:817.648 JLINK_WriteReg(R8, 0x00000000)
T4A00 005:817.653 - 0.006ms returns 0
T4A00 005:817.661 JLINK_WriteReg(R9, 0x2000045C)
T4A00 005:817.666 - 0.006ms returns 0
T4A00 005:817.673 JLINK_WriteReg(R10, 0x00000000)
T4A00 005:817.682 - 0.008ms returns 0
T4A00 005:817.689 JLINK_WriteReg(R11, 0x00000000)
T4A00 005:817.695 - 0.005ms returns 0
T4A00 005:817.702 JLINK_WriteReg(R12, 0x00000000)
T4A00 005:817.710 - 0.008ms returns 0
T4A00 005:817.719 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 005:817.728 - 0.009ms returns 0
T4A00 005:817.736 JLINK_WriteReg(R14, 0x20000001)
T4A00 005:817.744 - 0.007ms returns 0
T4A00 005:817.753 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 005:817.761 - 0.008ms returns 0
T4A00 005:817.770 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 005:817.778 - 0.008ms returns 0
T4A00 005:817.787 JLINK_WriteReg(MSP, 0x20001000)
T4A00 005:817.795 - 0.007ms returns 0
T4A00 005:817.803 JLINK_WriteReg(PSP, 0x20001000)
T4A00 005:817.811 - 0.007ms returns 0
T4A00 005:817.820 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 005:817.827 - 0.007ms returns 0
T4A00 005:817.836 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 005:817.846 - 0.010ms returns 0x00000023
T4A00 005:817.856 JLINK_Go()
T4A00 005:817.873   CPU_ReadMem(4 bytes @ 0x********)
T4A00 005:841.822 - 23.965ms
T4A00 005:841.875 JLINK_IsHalted()
T4A00 005:844.101 - 2.226ms returns FALSE
T4A00 005:844.129 JLINK_HasError()
T4A00 005:847.147 JLINK_IsHalted()
T4A00 006:093.307   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 006:095.635 - 248.487ms returns TRUE
T4A00 006:095.705 JLINK_ReadReg(R15 (PC))
T4A00 006:095.716 - 0.011ms returns 0x20000000
T4A00 006:095.721 JLINK_ClrBPEx(BPHandle = 0x00000023)
T4A00 006:095.725 - 0.004ms returns 0x00
T4A00 006:095.730 JLINK_ReadReg(R0)
T4A00 006:095.734 - 0.004ms returns 0x00000000
T4A00 006:096.371 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 006:096.381   Data:  00 96 09 49 09 4B 20 46 2A 46 FF F7 51 FD FF F7 ...
T4A00 006:096.394   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 006:124.799 - 28.427ms returns 0x200
T4A00 006:124.868 JLINK_HasError()
T4A00 006:124.876 JLINK_WriteReg(R0, 0x00001400)
T4A00 006:124.886 - 0.009ms returns 0
T4A00 006:124.901 JLINK_WriteReg(R1, 0x00000090)
T4A00 006:124.923 - 0.022ms returns 0
T4A00 006:124.930 JLINK_WriteReg(R2, 0x20000460)
T4A00 006:124.935 - 0.005ms returns 0
T4A00 006:124.948 JLINK_WriteReg(R3, 0x00000000)
T4A00 006:124.953 - 0.005ms returns 0
T4A00 006:124.959 JLINK_WriteReg(R4, 0x00000000)
T4A00 006:124.964 - 0.005ms returns 0
T4A00 006:124.969 JLINK_WriteReg(R5, 0x00000000)
T4A00 006:124.978 - 0.008ms returns 0
T4A00 006:124.987 JLINK_WriteReg(R6, 0x00000000)
T4A00 006:124.992 - 0.005ms returns 0
T4A00 006:124.998 JLINK_WriteReg(R7, 0x00000000)
T4A00 006:125.003 - 0.005ms returns 0
T4A00 006:125.009 JLINK_WriteReg(R8, 0x00000000)
T4A00 006:125.014 - 0.005ms returns 0
T4A00 006:125.021 JLINK_WriteReg(R9, 0x2000045C)
T4A00 006:125.025 - 0.005ms returns 0
T4A00 006:125.031 JLINK_WriteReg(R10, 0x00000000)
T4A00 006:125.036 - 0.005ms returns 0
T4A00 006:125.043 JLINK_WriteReg(R11, 0x00000000)
T4A00 006:125.048 - 0.005ms returns 0
T4A00 006:125.053 JLINK_WriteReg(R12, 0x00000000)
T4A00 006:125.059 - 0.006ms returns 0
T4A00 006:125.067 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 006:125.073 - 0.006ms returns 0
T4A00 006:125.078 JLINK_WriteReg(R14, 0x20000001)
T4A00 006:125.083 - 0.005ms returns 0
T4A00 006:125.089 JLINK_WriteReg(R15 (PC), 0x20000340)
T4A00 006:125.094 - 0.005ms returns 0
T4A00 006:125.100 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 006:125.105 - 0.005ms returns 0
T4A00 006:125.111 JLINK_WriteReg(MSP, 0x20001000)
T4A00 006:125.116 - 0.005ms returns 0
T4A00 006:125.121 JLINK_WriteReg(PSP, 0x20001000)
T4A00 006:125.126 - 0.004ms returns 0
T4A00 006:125.132 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 006:125.138 - 0.006ms returns 0
T4A00 006:125.145 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 006:125.153 - 0.008ms returns 0x00000024
T4A00 006:125.159 JLINK_Go()
T4A00 006:125.173   CPU_ReadMem(4 bytes @ 0x********)
T4A00 006:149.090 - 23.930ms
T4A00 006:149.147 JLINK_IsHalted()
T4A00 006:151.165 - 2.018ms returns FALSE
T4A00 006:151.195 JLINK_HasError()
T4A00 006:159.301 JLINK_IsHalted()
T4A00 006:161.712 - 2.410ms returns FALSE
T4A00 006:161.776 JLINK_HasError()
T4A00 006:163.618 JLINK_IsHalted()
T4A00 006:165.951 - 2.334ms returns FALSE
T4A00 006:165.983 JLINK_HasError()
T4A00 006:168.576 JLINK_IsHalted()
T4A00 006:171.036 - 2.460ms returns FALSE
T4A00 006:171.103 JLINK_HasError()
T4A00 006:172.301 JLINK_IsHalted()
T4A00 006:197.565   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 006:199.907 - 27.606ms returns TRUE
T4A00 006:200.004 JLINK_ReadReg(R15 (PC))
T4A00 006:200.019 - 0.015ms returns 0x20000000
T4A00 006:200.030 JLINK_ClrBPEx(BPHandle = 0x00000024)
T4A00 006:200.039 - 0.009ms returns 0x00
T4A00 006:200.050 JLINK_ReadReg(R0)
T4A00 006:200.059 - 0.008ms returns 0x00000000
T4A00 006:200.070 JLINK_HasError()
T4A00 006:200.101 JLINK_WriteReg(R0, 0x00000002)
T4A00 006:200.117 - 0.015ms returns 0
T4A00 006:200.123 JLINK_WriteReg(R1, 0x00000090)
T4A00 006:200.127 - 0.003ms returns 0
T4A00 006:200.131 JLINK_WriteReg(R2, 0x20000460)
T4A00 006:200.135 - 0.003ms returns 0
T4A00 006:200.139 JLINK_WriteReg(R3, 0x00000000)
T4A00 006:200.143 - 0.003ms returns 0
T4A00 006:201.632 JLINK_WriteReg(R4, 0x00000000)
T4A00 006:201.645 - 0.013ms returns 0
T4A00 006:201.651 JLINK_WriteReg(R5, 0x00000000)
T4A00 006:201.656 - 0.004ms returns 0
T4A00 006:201.661 JLINK_WriteReg(R6, 0x00000000)
T4A00 006:201.666 - 0.004ms returns 0
T4A00 006:201.671 JLINK_WriteReg(R7, 0x00000000)
T4A00 006:201.677 - 0.005ms returns 0
T4A00 006:201.682 JLINK_WriteReg(R8, 0x00000000)
T4A00 006:201.686 - 0.003ms returns 0
T4A00 006:201.690 JLINK_WriteReg(R9, 0x2000045C)
T4A00 006:201.693 - 0.003ms returns 0
T4A00 006:201.697 JLINK_WriteReg(R10, 0x00000000)
T4A00 006:201.701 - 0.003ms returns 0
T4A00 006:201.705 JLINK_WriteReg(R11, 0x00000000)
T4A00 006:201.709 - 0.003ms returns 0
T4A00 006:201.713 JLINK_WriteReg(R12, 0x00000000)
T4A00 006:201.717 - 0.003ms returns 0
T4A00 006:201.721 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 006:201.725 - 0.004ms returns 0
T4A00 006:201.729 JLINK_WriteReg(R14, 0x20000001)
T4A00 006:201.733 - 0.003ms returns 0
T4A00 006:201.737 JLINK_WriteReg(R15 (PC), 0x200003C8)
T4A00 006:201.741 - 0.003ms returns 0
T4A00 006:201.745 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 006:201.749 - 0.004ms returns 0
T4A00 006:201.753 JLINK_WriteReg(MSP, 0x20001000)
T4A00 006:201.757 - 0.003ms returns 0
T4A00 006:201.761 JLINK_WriteReg(PSP, 0x20001000)
T4A00 006:201.768 - 0.006ms returns 0
T4A00 006:201.772 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 006:201.776 - 0.003ms returns 0
T4A00 006:201.781 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 006:201.786 - 0.005ms returns 0x00000025
T4A00 006:201.791 JLINK_Go()
T4A00 006:201.800   CPU_ReadMem(4 bytes @ 0x********)
T4A00 006:227.290 - 25.498ms
T4A00 006:227.391 JLINK_IsHalted()
T4A00 006:252.905   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 006:255.945 - 28.553ms returns TRUE
T4A00 006:256.123 JLINK_ReadReg(R15 (PC))
T4A00 006:256.153 - 0.029ms returns 0x20000000
T4A00 006:256.175 JLINK_ClrBPEx(BPHandle = 0x00000025)
T4A00 006:256.187 - 0.012ms returns 0x00
T4A00 006:256.204 JLINK_ReadReg(R0)
T4A00 006:256.215 - 0.011ms returns 0x00000000
T4A00 006:330.373 JLINK_WriteMem(0x20000000, 0x460 Bytes, ...)
T4A00 006:330.387   Data:  00 BE 0A E0 0D 78 2D 06 68 40 08 24 40 00 00 D3 ...
T4A00 006:330.406   CPU_WriteMem(1120 bytes @ 0x20000000)
T4A00 006:591.349 - 260.974ms returns 0x460
T4A00 006:591.516 JLINK_HasError()
T4A00 006:591.537 JLINK_WriteReg(R0, 0x00000000)
T4A00 006:591.550 - 0.013ms returns 0
T4A00 006:591.558 JLINK_WriteReg(R1, 0x00B71B00)
T4A00 006:591.565 - 0.007ms returns 0
T4A00 006:591.573 JLINK_WriteReg(R2, 0x00000003)
T4A00 006:591.580 - 0.006ms returns 0
T4A00 006:591.587 JLINK_WriteReg(R3, 0x00000000)
T4A00 006:591.595 - 0.007ms returns 0
T4A00 006:591.603 JLINK_WriteReg(R4, 0x00000000)
T4A00 006:591.610 - 0.007ms returns 0
T4A00 006:591.619 JLINK_WriteReg(R5, 0x00000000)
T4A00 006:591.627 - 0.007ms returns 0
T4A00 006:591.634 JLINK_WriteReg(R6, 0x00000000)
T4A00 006:591.641 - 0.006ms returns 0
T4A00 006:591.648 JLINK_WriteReg(R7, 0x00000000)
T4A00 006:591.655 - 0.006ms returns 0
T4A00 006:591.663 JLINK_WriteReg(R8, 0x00000000)
T4A00 006:591.671 - 0.007ms returns 0
T4A00 006:591.678 JLINK_WriteReg(R9, 0x2000045C)
T4A00 006:591.685 - 0.007ms returns 0
T4A00 006:591.693 JLINK_WriteReg(R10, 0x00000000)
T4A00 006:591.699 - 0.005ms returns 0
T4A00 006:591.705 JLINK_WriteReg(R11, 0x00000000)
T4A00 006:591.711 - 0.005ms returns 0
T4A00 006:591.718 JLINK_WriteReg(R12, 0x00000000)
T4A00 006:591.723 - 0.005ms returns 0
T4A00 006:591.730 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 006:591.737 - 0.007ms returns 0
T4A00 006:591.743 JLINK_WriteReg(R14, 0x20000001)
T4A00 006:591.749 - 0.005ms returns 0
T4A00 006:591.756 JLINK_WriteReg(R15 (PC), 0x20000130)
T4A00 006:591.761 - 0.006ms returns 0
T4A00 006:591.768 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 006:591.774 - 0.005ms returns 0
T4A00 006:591.780 JLINK_WriteReg(MSP, 0x20001000)
T4A00 006:591.786 - 0.005ms returns 0
T4A00 006:591.792 JLINK_WriteReg(PSP, 0x20001000)
T4A00 006:591.798 - 0.005ms returns 0
T4A00 006:591.804 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 006:591.810 - 0.005ms returns 0
T4A00 006:591.817 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 006:591.831   CPU_ReadMem(4 bytes @ 0x20000000)
T4A00 006:594.360   CPU_WriteMem(4 bytes @ 0x20000000)
T4A00 006:597.207   CPU_ReadMem(4 bytes @ 0x20000000)
T4A00 006:599.711   CPU_WriteMem(4 bytes @ 0x20000000)
T4A00 006:601.948   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 006:604.571 - 12.753ms returns 0x00000026
T4A00 006:604.631 JLINK_Go()
T4A00 006:604.642   CPU_WriteMem(2 bytes @ 0x20000000)
T4A00 006:607.217   CPU_ReadMem(4 bytes @ 0x********)
T4A00 006:632.084 - 27.452ms
T4A00 006:632.156 JLINK_IsHalted()
T4A00 006:657.531   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 006:659.971 - 27.813ms returns TRUE
T4A00 006:660.080 JLINK_ReadReg(R15 (PC))
T4A00 006:660.092 - 0.012ms returns 0x20000000
T4A00 006:660.098 JLINK_ClrBPEx(BPHandle = 0x00000026)
T4A00 006:660.103 - 0.005ms returns 0x00
T4A00 006:660.109 JLINK_ReadReg(R0)
T4A00 006:660.113 - 0.004ms returns 0x00000000
T4A00 006:660.119 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 006:660.125   Data:  08 15 00 20 D5 00 00 00 EF 00 00 00 F1 00 00 00 ...
T4A00 006:660.139   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 006:688.438 - 28.317ms returns 0x200
T4A00 006:688.530 JLINK_HasError()
T4A00 006:688.549 JLINK_WriteReg(R0, 0x00000000)
T4A00 006:688.569 - 0.020ms returns 0
T4A00 006:688.575 JLINK_WriteReg(R1, 0x00000200)
T4A00 006:688.579 - 0.004ms returns 0
T4A00 006:688.583 JLINK_WriteReg(R2, 0x20000460)
T4A00 006:688.587 - 0.003ms returns 0
T4A00 006:688.591 JLINK_WriteReg(R3, 0x00000000)
T4A00 006:688.595 - 0.003ms returns 0
T4A00 006:688.599 JLINK_WriteReg(R4, 0x00000000)
T4A00 006:688.603 - 0.003ms returns 0
T4A00 006:688.608 JLINK_WriteReg(R5, 0x00000000)
T4A00 006:688.612 - 0.003ms returns 0
T4A00 006:688.623 JLINK_WriteReg(R6, 0x00000000)
T4A00 006:688.626 - 0.003ms returns 0
T4A00 006:688.631 JLINK_WriteReg(R7, 0x00000000)
T4A00 006:688.635 - 0.003ms returns 0
T4A00 006:688.639 JLINK_WriteReg(R8, 0x00000000)
T4A00 006:688.643 - 0.003ms returns 0
T4A00 006:688.647 JLINK_WriteReg(R9, 0x2000045C)
T4A00 006:688.651 - 0.003ms returns 0
T4A00 006:688.655 JLINK_WriteReg(R10, 0x00000000)
T4A00 006:688.659 - 0.003ms returns 0
T4A00 006:688.663 JLINK_WriteReg(R11, 0x00000000)
T4A00 006:688.667 - 0.003ms returns 0
T4A00 006:688.672 JLINK_WriteReg(R12, 0x00000000)
T4A00 006:688.676 - 0.003ms returns 0
T4A00 006:688.680 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 006:688.685 - 0.004ms returns 0
T4A00 006:688.689 JLINK_WriteReg(R14, 0x20000001)
T4A00 006:688.693 - 0.003ms returns 0
T4A00 006:688.697 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 006:688.701 - 0.004ms returns 0
T4A00 006:688.705 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 006:688.709 - 0.003ms returns 0
T4A00 006:688.713 JLINK_WriteReg(MSP, 0x20001000)
T4A00 006:688.716 - 0.003ms returns 0
T4A00 006:688.720 JLINK_WriteReg(PSP, 0x20001000)
T4A00 006:688.724 - 0.003ms returns 0
T4A00 006:688.728 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 006:688.732 - 0.003ms returns 0
T4A00 006:688.737 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 006:688.742 - 0.006ms returns 0x00000027
T4A00 006:688.747 JLINK_Go()
T4A00 006:688.759   CPU_ReadMem(4 bytes @ 0x********)
T4A00 006:712.269 - 23.520ms
T4A00 006:712.361 JLINK_IsHalted()
T4A00 006:715.137 - 2.775ms returns FALSE
T4A00 006:715.189 JLINK_HasError()
T4A00 006:728.225 JLINK_IsHalted()
T4A00 006:753.780   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 006:756.265 - 28.039ms returns TRUE
T4A00 006:756.294 JLINK_ReadReg(R15 (PC))
T4A00 006:756.302 - 0.008ms returns 0x20000000
T4A00 006:756.309 JLINK_ClrBPEx(BPHandle = 0x00000027)
T4A00 006:756.313 - 0.004ms returns 0x00
T4A00 006:756.317 JLINK_ReadReg(R0)
T4A00 006:756.321 - 0.004ms returns 0x00000200
T4A00 006:757.901 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 006:757.913   Data:  FF F7 62 FF 64 14 00 00 84 14 00 00 80 B5 00 F0 ...
T4A00 006:757.925   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 006:786.341 - 28.439ms returns 0x200
T4A00 006:786.401 JLINK_HasError()
T4A00 006:786.409 JLINK_WriteReg(R0, 0x00000200)
T4A00 006:786.417 - 0.009ms returns 0
T4A00 006:786.422 JLINK_WriteReg(R1, 0x00000200)
T4A00 006:786.425 - 0.003ms returns 0
T4A00 006:786.430 JLINK_WriteReg(R2, 0x20000460)
T4A00 006:786.433 - 0.003ms returns 0
T4A00 006:786.437 JLINK_WriteReg(R3, 0x00000000)
T4A00 006:786.441 - 0.003ms returns 0
T4A00 006:786.445 JLINK_WriteReg(R4, 0x00000000)
T4A00 006:786.449 - 0.003ms returns 0
T4A00 006:786.454 JLINK_WriteReg(R5, 0x00000000)
T4A00 006:786.460 - 0.005ms returns 0
T4A00 006:786.465 JLINK_WriteReg(R6, 0x00000000)
T4A00 006:786.470 - 0.005ms returns 0
T4A00 006:786.491 JLINK_WriteReg(R7, 0x00000000)
T4A00 006:786.496 - 0.004ms returns 0
T4A00 006:786.501 JLINK_WriteReg(R8, 0x00000000)
T4A00 006:786.505 - 0.004ms returns 0
T4A00 006:786.509 JLINK_WriteReg(R9, 0x2000045C)
T4A00 006:786.513 - 0.003ms returns 0
T4A00 006:786.517 JLINK_WriteReg(R10, 0x00000000)
T4A00 006:786.521 - 0.003ms returns 0
T4A00 006:786.525 JLINK_WriteReg(R11, 0x00000000)
T4A00 006:786.529 - 0.003ms returns 0
T4A00 006:786.533 JLINK_WriteReg(R12, 0x00000000)
T4A00 006:786.537 - 0.004ms returns 0
T4A00 006:786.549 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 006:786.557 - 0.008ms returns 0
T4A00 006:786.563 JLINK_WriteReg(R14, 0x20000001)
T4A00 006:786.567 - 0.003ms returns 0
T4A00 006:786.571 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 006:786.575 - 0.003ms returns 0
T4A00 006:786.579 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 006:786.583 - 0.003ms returns 0
T4A00 006:786.587 JLINK_WriteReg(MSP, 0x20001000)
T4A00 006:786.591 - 0.003ms returns 0
T4A00 006:786.595 JLINK_WriteReg(PSP, 0x20001000)
T4A00 006:786.598 - 0.003ms returns 0
T4A00 006:786.603 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 006:786.606 - 0.004ms returns 0
T4A00 006:786.612 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 006:786.617 - 0.006ms returns 0x00000028
T4A00 006:786.621 JLINK_Go()
T4A00 006:786.633   CPU_ReadMem(4 bytes @ 0x********)
T4A00 006:811.650 - 25.027ms
T4A00 006:811.749 JLINK_IsHalted()
T4A00 006:814.417 - 2.667ms returns FALSE
T4A00 006:814.504 JLINK_HasError()
T4A00 006:818.987 JLINK_IsHalted()
T4A00 007:063.903   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 007:066.302 - 247.315ms returns TRUE
T4A00 007:066.345 JLINK_ReadReg(R15 (PC))
T4A00 007:066.355 - 0.009ms returns 0x20000000
T4A00 007:066.361 JLINK_ClrBPEx(BPHandle = 0x00000028)
T4A00 007:066.368 - 0.006ms returns 0x00
T4A00 007:066.374 JLINK_ReadReg(R0)
T4A00 007:066.379 - 0.005ms returns 0x00000400
T4A00 007:067.459 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 007:067.469   Data:  A5 A5 00 00 80 88 70 47 00 48 70 47 08 00 00 20 ...
T4A00 007:067.481   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 007:095.475 - 28.014ms returns 0x200
T4A00 007:095.529 JLINK_HasError()
T4A00 007:095.543 JLINK_WriteReg(R0, 0x00000400)
T4A00 007:095.553 - 0.009ms returns 0
T4A00 007:095.557 JLINK_WriteReg(R1, 0x00000200)
T4A00 007:095.561 - 0.004ms returns 0
T4A00 007:095.565 JLINK_WriteReg(R2, 0x20000460)
T4A00 007:095.569 - 0.004ms returns 0
T4A00 007:095.574 JLINK_WriteReg(R3, 0x00000000)
T4A00 007:095.577 - 0.004ms returns 0
T4A00 007:095.582 JLINK_WriteReg(R4, 0x00000000)
T4A00 007:095.586 - 0.003ms returns 0
T4A00 007:095.590 JLINK_WriteReg(R5, 0x00000000)
T4A00 007:095.594 - 0.003ms returns 0
T4A00 007:095.598 JLINK_WriteReg(R6, 0x00000000)
T4A00 007:095.602 - 0.003ms returns 0
T4A00 007:095.606 JLINK_WriteReg(R7, 0x00000000)
T4A00 007:095.610 - 0.003ms returns 0
T4A00 007:095.614 JLINK_WriteReg(R8, 0x00000000)
T4A00 007:095.618 - 0.003ms returns 0
T4A00 007:095.623 JLINK_WriteReg(R9, 0x2000045C)
T4A00 007:095.627 - 0.003ms returns 0
T4A00 007:095.631 JLINK_WriteReg(R10, 0x00000000)
T4A00 007:095.635 - 0.004ms returns 0
T4A00 007:095.640 JLINK_WriteReg(R11, 0x00000000)
T4A00 007:095.643 - 0.003ms returns 0
T4A00 007:095.648 JLINK_WriteReg(R12, 0x00000000)
T4A00 007:095.652 - 0.003ms returns 0
T4A00 007:095.656 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 007:095.661 - 0.004ms returns 0
T4A00 007:095.665 JLINK_WriteReg(R14, 0x20000001)
T4A00 007:095.669 - 0.003ms returns 0
T4A00 007:095.673 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 007:095.677 - 0.004ms returns 0
T4A00 007:095.681 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 007:095.686 - 0.004ms returns 0
T4A00 007:095.690 JLINK_WriteReg(MSP, 0x20001000)
T4A00 007:095.694 - 0.004ms returns 0
T4A00 007:095.699 JLINK_WriteReg(PSP, 0x20001000)
T4A00 007:095.702 - 0.003ms returns 0
T4A00 007:095.707 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 007:095.711 - 0.003ms returns 0
T4A00 007:095.716 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 007:095.721 - 0.006ms returns 0x00000029
T4A00 007:095.726 JLINK_Go()
T4A00 007:095.739   CPU_ReadMem(4 bytes @ 0x********)
T4A00 007:119.761 - 24.033ms
T4A00 007:119.801 JLINK_IsHalted()
T4A00 007:122.388 - 2.587ms returns FALSE
T4A00 007:122.413 JLINK_HasError()
T4A00 007:134.875 JLINK_IsHalted()
T4A00 007:160.034   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 007:162.567 - 27.692ms returns TRUE
T4A00 007:162.609 JLINK_ReadReg(R15 (PC))
T4A00 007:162.617 - 0.008ms returns 0x20000000
T4A00 007:162.622 JLINK_ClrBPEx(BPHandle = 0x00000029)
T4A00 007:162.627 - 0.004ms returns 0x00
T4A00 007:162.635 JLINK_ReadReg(R0)
T4A00 007:162.639 - 0.004ms returns 0x00000600
T4A00 007:163.367 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 007:163.378   Data:  00 21 16 91 40 20 15 90 04 A8 14 90 03 91 17 91 ...
T4A00 007:163.390   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 007:191.443 - 28.075ms returns 0x200
T4A00 007:191.496 JLINK_HasError()
T4A00 007:191.504 JLINK_WriteReg(R0, 0x00000600)
T4A00 007:191.512 - 0.008ms returns 0
T4A00 007:191.516 JLINK_WriteReg(R1, 0x00000200)
T4A00 007:191.520 - 0.004ms returns 0
T4A00 007:191.524 JLINK_WriteReg(R2, 0x20000460)
T4A00 007:191.528 - 0.003ms returns 0
T4A00 007:191.532 JLINK_WriteReg(R3, 0x00000000)
T4A00 007:191.536 - 0.003ms returns 0
T4A00 007:191.540 JLINK_WriteReg(R4, 0x00000000)
T4A00 007:191.543 - 0.003ms returns 0
T4A00 007:191.547 JLINK_WriteReg(R5, 0x00000000)
T4A00 007:191.551 - 0.003ms returns 0
T4A00 007:191.555 JLINK_WriteReg(R6, 0x00000000)
T4A00 007:191.558 - 0.003ms returns 0
T4A00 007:191.563 JLINK_WriteReg(R7, 0x00000000)
T4A00 007:191.566 - 0.003ms returns 0
T4A00 007:191.570 JLINK_WriteReg(R8, 0x00000000)
T4A00 007:191.574 - 0.003ms returns 0
T4A00 007:191.578 JLINK_WriteReg(R9, 0x2000045C)
T4A00 007:191.582 - 0.003ms returns 0
T4A00 007:191.586 JLINK_WriteReg(R10, 0x00000000)
T4A00 007:191.590 - 0.003ms returns 0
T4A00 007:191.594 JLINK_WriteReg(R11, 0x00000000)
T4A00 007:191.597 - 0.003ms returns 0
T4A00 007:191.601 JLINK_WriteReg(R12, 0x00000000)
T4A00 007:191.605 - 0.003ms returns 0
T4A00 007:191.609 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 007:191.614 - 0.005ms returns 0
T4A00 007:191.623 JLINK_WriteReg(R14, 0x20000001)
T4A00 007:191.627 - 0.003ms returns 0
T4A00 007:191.631 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 007:191.635 - 0.004ms returns 0
T4A00 007:191.639 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 007:191.643 - 0.003ms returns 0
T4A00 007:191.647 JLINK_WriteReg(MSP, 0x20001000)
T4A00 007:191.650 - 0.003ms returns 0
T4A00 007:191.654 JLINK_WriteReg(PSP, 0x20001000)
T4A00 007:191.658 - 0.003ms returns 0
T4A00 007:191.662 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 007:191.666 - 0.003ms returns 0
T4A00 007:191.670 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 007:191.677 - 0.005ms returns 0x0000002A
T4A00 007:191.681 JLINK_Go()
T4A00 007:191.692   CPU_ReadMem(4 bytes @ 0x********)
T4A00 007:215.272 - 23.590ms
T4A00 007:215.340 JLINK_IsHalted()
T4A00 007:217.453 - 2.111ms returns FALSE
T4A00 007:217.515 JLINK_HasError()
T4A00 007:218.845 JLINK_IsHalted()
T4A00 007:244.147   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 007:246.221 - 27.375ms returns TRUE
T4A00 007:246.288 JLINK_ReadReg(R15 (PC))
T4A00 007:246.302 - 0.014ms returns 0x20000000
T4A00 007:246.313 JLINK_ClrBPEx(BPHandle = 0x0000002A)
T4A00 007:246.321 - 0.008ms returns 0x00
T4A00 007:246.329 JLINK_ReadReg(R0)
T4A00 007:246.337 - 0.007ms returns 0x00000800
T4A00 007:247.030 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 007:247.045   Data:  FF E7 FF E7 FF E7 FF E7 FF E7 FF E7 FF E7 FF E7 ...
T4A00 007:247.064   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 007:275.386 - 28.355ms returns 0x200
T4A00 007:275.489 JLINK_HasError()
T4A00 007:275.507 JLINK_WriteReg(R0, 0x00000800)
T4A00 007:275.524 - 0.017ms returns 0
T4A00 007:275.534 JLINK_WriteReg(R1, 0x00000200)
T4A00 007:275.543 - 0.009ms returns 0
T4A00 007:275.553 JLINK_WriteReg(R2, 0x20000460)
T4A00 007:275.562 - 0.009ms returns 0
T4A00 007:275.581 JLINK_WriteReg(R3, 0x00000000)
T4A00 007:275.591 - 0.009ms returns 0
T4A00 007:275.601 JLINK_WriteReg(R4, 0x00000000)
T4A00 007:275.609 - 0.008ms returns 0
T4A00 007:275.620 JLINK_WriteReg(R5, 0x00000000)
T4A00 007:275.629 - 0.009ms returns 0
T4A00 007:275.639 JLINK_WriteReg(R6, 0x00000000)
T4A00 007:275.648 - 0.008ms returns 0
T4A00 007:275.657 JLINK_WriteReg(R7, 0x00000000)
T4A00 007:275.666 - 0.008ms returns 0
T4A00 007:275.677 JLINK_WriteReg(R8, 0x00000000)
T4A00 007:275.685 - 0.009ms returns 0
T4A00 007:275.696 JLINK_WriteReg(R9, 0x2000045C)
T4A00 007:275.704 - 0.008ms returns 0
T4A00 007:275.721 JLINK_WriteReg(R10, 0x00000000)
T4A00 007:275.729 - 0.009ms returns 0
T4A00 007:275.739 JLINK_WriteReg(R11, 0x00000000)
T4A00 007:275.748 - 0.009ms returns 0
T4A00 007:275.758 JLINK_WriteReg(R12, 0x00000000)
T4A00 007:275.767 - 0.008ms returns 0
T4A00 007:275.777 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 007:275.788 - 0.011ms returns 0
T4A00 007:275.798 JLINK_WriteReg(R14, 0x20000001)
T4A00 007:275.807 - 0.008ms returns 0
T4A00 007:275.817 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 007:275.826 - 0.009ms returns 0
T4A00 007:275.836 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 007:275.845 - 0.008ms returns 0
T4A00 007:275.854 JLINK_WriteReg(MSP, 0x20001000)
T4A00 007:275.863 - 0.008ms returns 0
T4A00 007:275.873 JLINK_WriteReg(PSP, 0x20001000)
T4A00 007:275.881 - 0.008ms returns 0
T4A00 007:275.892 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 007:275.906 - 0.014ms returns 0
T4A00 007:275.917 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 007:275.929 - 0.012ms returns 0x0000002B
T4A00 007:275.938 JLINK_Go()
T4A00 007:275.959   CPU_ReadMem(4 bytes @ 0x********)
T4A00 007:299.840 - 23.900ms
T4A00 007:299.873 JLINK_IsHalted()
T4A00 007:301.816 - 1.942ms returns FALSE
T4A00 007:301.837 JLINK_HasError()
T4A00 007:305.376 JLINK_IsHalted()
T4A00 007:551.303   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 007:553.793 - 248.416ms returns TRUE
T4A00 007:553.885 JLINK_ReadReg(R15 (PC))
T4A00 007:553.893 - 0.008ms returns 0x20000000
T4A00 007:553.898 JLINK_ClrBPEx(BPHandle = 0x0000002B)
T4A00 007:553.903 - 0.004ms returns 0x00
T4A00 007:553.908 JLINK_ReadReg(R0)
T4A00 007:553.912 - 0.004ms returns 0x00000A00
T4A00 007:554.363 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 007:554.372   Data:  00 20 00 2C 56 D0 71 69 00 2C 53 D0 01 22 12 03 ...
T4A00 007:554.383   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 007:582.585 - 28.219ms returns 0x200
T4A00 007:582.688 JLINK_HasError()
T4A00 007:582.728 JLINK_WriteReg(R0, 0x00000A00)
T4A00 007:582.748 - 0.019ms returns 0
T4A00 007:582.763 JLINK_WriteReg(R1, 0x00000200)
T4A00 007:582.774 - 0.011ms returns 0
T4A00 007:582.787 JLINK_WriteReg(R2, 0x20000460)
T4A00 007:582.799 - 0.012ms returns 0
T4A00 007:582.815 JLINK_WriteReg(R3, 0x00000000)
T4A00 007:582.832 - 0.016ms returns 0
T4A00 007:582.847 JLINK_WriteReg(R4, 0x00000000)
T4A00 007:582.861 - 0.014ms returns 0
T4A00 007:582.889 JLINK_WriteReg(R5, 0x00000000)
T4A00 007:582.905 - 0.015ms returns 0
T4A00 007:582.921 JLINK_WriteReg(R6, 0x00000000)
T4A00 007:582.936 - 0.014ms returns 0
T4A00 007:582.953 JLINK_WriteReg(R7, 0x00000000)
T4A00 007:582.967 - 0.014ms returns 0
T4A00 007:582.983 JLINK_WriteReg(R8, 0x00000000)
T4A00 007:582.997 - 0.014ms returns 0
T4A00 007:583.014 JLINK_WriteReg(R9, 0x2000045C)
T4A00 007:583.029 - 0.014ms returns 0
T4A00 007:583.045 JLINK_WriteReg(R10, 0x00000000)
T4A00 007:583.060 - 0.015ms returns 0
T4A00 007:583.076 JLINK_WriteReg(R11, 0x00000000)
T4A00 007:583.091 - 0.015ms returns 0
T4A00 007:583.109 JLINK_WriteReg(R12, 0x00000000)
T4A00 007:583.124 - 0.015ms returns 0
T4A00 007:583.146 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 007:583.163 - 0.016ms returns 0
T4A00 007:583.180 JLINK_WriteReg(R14, 0x20000001)
T4A00 007:583.196 - 0.015ms returns 0
T4A00 007:583.213 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 007:583.229 - 0.015ms returns 0
T4A00 007:583.245 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 007:583.261 - 0.014ms returns 0
T4A00 007:583.277 JLINK_WriteReg(MSP, 0x20001000)
T4A00 007:583.289 - 0.011ms returns 0
T4A00 007:583.301 JLINK_WriteReg(PSP, 0x20001000)
T4A00 007:583.312 - 0.011ms returns 0
T4A00 007:583.325 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 007:583.335 - 0.011ms returns 0
T4A00 007:583.349 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 007:583.364 - 0.015ms returns 0x0000002C
T4A00 007:583.377 JLINK_Go()
T4A00 007:583.402   CPU_ReadMem(4 bytes @ 0x********)
T4A00 007:607.134 - 23.756ms
T4A00 007:607.199 JLINK_IsHalted()
T4A00 007:609.578 - 2.378ms returns FALSE
T4A00 007:609.617 JLINK_HasError()
T4A00 007:621.601 JLINK_IsHalted()
T4A00 007:646.841   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 007:649.444 - 27.842ms returns TRUE
T4A00 007:649.513 JLINK_ReadReg(R15 (PC))
T4A00 007:649.523 - 0.010ms returns 0x20000000
T4A00 007:649.529 JLINK_ClrBPEx(BPHandle = 0x0000002C)
T4A00 007:649.535 - 0.005ms returns 0x00
T4A00 007:649.541 JLINK_ReadReg(R0)
T4A00 007:649.545 - 0.004ms returns 0x00000C00
T4A00 007:650.027 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 007:650.035   Data:  C8 40 01 B0 F0 BD C0 46 DC 1B 10 00 80 8D 5B 00 ...
T4A00 007:650.061   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 007:678.111 - 28.083ms returns 0x200
T4A00 007:678.301 JLINK_HasError()
T4A00 007:678.316 JLINK_WriteReg(R0, 0x00000C00)
T4A00 007:678.325 - 0.009ms returns 0
T4A00 007:678.329 JLINK_WriteReg(R1, 0x00000200)
T4A00 007:678.333 - 0.003ms returns 0
T4A00 007:678.337 JLINK_WriteReg(R2, 0x20000460)
T4A00 007:678.341 - 0.003ms returns 0
T4A00 007:678.345 JLINK_WriteReg(R3, 0x00000000)
T4A00 007:678.349 - 0.003ms returns 0
T4A00 007:678.353 JLINK_WriteReg(R4, 0x00000000)
T4A00 007:678.357 - 0.003ms returns 0
T4A00 007:678.369 JLINK_WriteReg(R5, 0x00000000)
T4A00 007:678.373 - 0.003ms returns 0
T4A00 007:678.377 JLINK_WriteReg(R6, 0x00000000)
T4A00 007:678.381 - 0.003ms returns 0
T4A00 007:678.385 JLINK_WriteReg(R7, 0x00000000)
T4A00 007:678.389 - 0.003ms returns 0
T4A00 007:678.393 JLINK_WriteReg(R8, 0x00000000)
T4A00 007:678.397 - 0.003ms returns 0
T4A00 007:678.401 JLINK_WriteReg(R9, 0x2000045C)
T4A00 007:678.405 - 0.003ms returns 0
T4A00 007:678.409 JLINK_WriteReg(R10, 0x00000000)
T4A00 007:678.413 - 0.003ms returns 0
T4A00 007:678.417 JLINK_WriteReg(R11, 0x00000000)
T4A00 007:678.421 - 0.003ms returns 0
T4A00 007:678.425 JLINK_WriteReg(R12, 0x00000000)
T4A00 007:678.428 - 0.003ms returns 0
T4A00 007:678.432 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 007:678.437 - 0.004ms returns 0
T4A00 007:678.441 JLINK_WriteReg(R14, 0x20000001)
T4A00 007:678.444 - 0.003ms returns 0
T4A00 007:678.449 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 007:678.453 - 0.003ms returns 0
T4A00 007:678.457 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 007:678.461 - 0.003ms returns 0
T4A00 007:678.465 JLINK_WriteReg(MSP, 0x20001000)
T4A00 007:678.468 - 0.003ms returns 0
T4A00 007:678.472 JLINK_WriteReg(PSP, 0x20001000)
T4A00 007:678.476 - 0.003ms returns 0
T4A00 007:678.480 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 007:678.483 - 0.003ms returns 0
T4A00 007:678.489 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 007:678.494 - 0.005ms returns 0x0000002D
T4A00 007:678.498 JLINK_Go()
T4A00 007:678.511   CPU_ReadMem(4 bytes @ 0x********)
T4A00 007:702.517 - 24.018ms
T4A00 007:702.572 JLINK_IsHalted()
T4A00 007:704.755 - 2.182ms returns FALSE
T4A00 007:704.789 JLINK_HasError()
T4A00 007:709.394 JLINK_IsHalted()
T4A00 007:735.827   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 007:738.226 - 28.831ms returns TRUE
T4A00 007:738.270 JLINK_ReadReg(R15 (PC))
T4A00 007:738.279 - 0.009ms returns 0x20000000
T4A00 007:738.285 JLINK_ClrBPEx(BPHandle = 0x0000002D)
T4A00 007:738.289 - 0.004ms returns 0x00
T4A00 007:738.294 JLINK_ReadReg(R0)
T4A00 007:738.297 - 0.004ms returns 0x00000E00
T4A00 007:739.337 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 007:739.348   Data:  1B 68 83 42 03 D0 8B 42 F9 D1 08 68 10 60 70 47 ...
T4A00 007:739.360   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 007:767.450 - 28.112ms returns 0x200
T4A00 007:767.549 JLINK_HasError()
T4A00 007:767.562 JLINK_WriteReg(R0, 0x00000E00)
T4A00 007:767.576 - 0.013ms returns 0
T4A00 007:767.583 JLINK_WriteReg(R1, 0x00000200)
T4A00 007:767.589 - 0.006ms returns 0
T4A00 007:767.597 JLINK_WriteReg(R2, 0x20000460)
T4A00 007:767.603 - 0.006ms returns 0
T4A00 007:767.621 JLINK_WriteReg(R3, 0x00000000)
T4A00 007:767.627 - 0.006ms returns 0
T4A00 007:767.634 JLINK_WriteReg(R4, 0x00000000)
T4A00 007:767.641 - 0.006ms returns 0
T4A00 007:767.648 JLINK_WriteReg(R5, 0x00000000)
T4A00 007:767.654 - 0.006ms returns 0
T4A00 007:767.665 JLINK_WriteReg(R6, 0x00000000)
T4A00 007:767.673 - 0.008ms returns 0
T4A00 007:767.681 JLINK_WriteReg(R7, 0x00000000)
T4A00 007:767.687 - 0.006ms returns 0
T4A00 007:767.694 JLINK_WriteReg(R8, 0x00000000)
T4A00 007:767.701 - 0.006ms returns 0
T4A00 007:767.708 JLINK_WriteReg(R9, 0x2000045C)
T4A00 007:767.713 - 0.006ms returns 0
T4A00 007:767.721 JLINK_WriteReg(R10, 0x00000000)
T4A00 007:767.727 - 0.006ms returns 0
T4A00 007:767.734 JLINK_WriteReg(R11, 0x00000000)
T4A00 007:767.741 - 0.006ms returns 0
T4A00 007:767.748 JLINK_WriteReg(R12, 0x00000000)
T4A00 007:767.754 - 0.006ms returns 0
T4A00 007:767.761 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 007:767.768 - 0.007ms returns 0
T4A00 007:767.775 JLINK_WriteReg(R14, 0x20000001)
T4A00 007:767.781 - 0.006ms returns 0
T4A00 007:767.789 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 007:767.795 - 0.006ms returns 0
T4A00 007:767.802 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 007:767.809 - 0.006ms returns 0
T4A00 007:767.816 JLINK_WriteReg(MSP, 0x20001000)
T4A00 007:767.822 - 0.006ms returns 0
T4A00 007:767.829 JLINK_WriteReg(PSP, 0x20001000)
T4A00 007:767.835 - 0.006ms returns 0
T4A00 007:767.842 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 007:767.849 - 0.006ms returns 0
T4A00 007:767.857 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 007:767.865 - 0.010ms returns 0x0000002E
T4A00 007:767.872 JLINK_Go()
T4A00 007:767.886   CPU_ReadMem(4 bytes @ 0x********)
T4A00 008:017.021 - 249.148ms
T4A00 008:017.084 JLINK_IsHalted()
T4A00 008:019.224 - 2.139ms returns FALSE
T4A00 008:019.251 JLINK_HasError()
T4A00 008:026.669 JLINK_IsHalted()
T4A00 008:052.415   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 008:055.024 - 28.355ms returns TRUE
T4A00 008:055.111 JLINK_ReadReg(R15 (PC))
T4A00 008:055.122 - 0.011ms returns 0x20000000
T4A00 008:055.128 JLINK_ClrBPEx(BPHandle = 0x0000002E)
T4A00 008:055.133 - 0.005ms returns 0x00
T4A00 008:055.139 JLINK_ReadReg(R0)
T4A00 008:055.143 - 0.004ms returns 0x00001000
T4A00 008:055.675 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 008:055.685   Data:  1B D5 2B 21 16 E0 04 98 C0 07 F1 D1 00 2F EF D0 ...
T4A00 008:055.701   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 008:083.504 - 27.827ms returns 0x200
T4A00 008:083.550 JLINK_HasError()
T4A00 008:083.564 JLINK_WriteReg(R0, 0x00001000)
T4A00 008:083.573 - 0.009ms returns 0
T4A00 008:083.577 JLINK_WriteReg(R1, 0x00000200)
T4A00 008:083.581 - 0.003ms returns 0
T4A00 008:083.585 JLINK_WriteReg(R2, 0x20000460)
T4A00 008:083.589 - 0.003ms returns 0
T4A00 008:083.593 JLINK_WriteReg(R3, 0x00000000)
T4A00 008:083.597 - 0.003ms returns 0
T4A00 008:083.601 JLINK_WriteReg(R4, 0x00000000)
T4A00 008:083.605 - 0.003ms returns 0
T4A00 008:083.609 JLINK_WriteReg(R5, 0x00000000)
T4A00 008:083.613 - 0.003ms returns 0
T4A00 008:083.617 JLINK_WriteReg(R6, 0x00000000)
T4A00 008:083.621 - 0.003ms returns 0
T4A00 008:083.625 JLINK_WriteReg(R7, 0x00000000)
T4A00 008:083.629 - 0.004ms returns 0
T4A00 008:083.633 JLINK_WriteReg(R8, 0x00000000)
T4A00 008:083.637 - 0.003ms returns 0
T4A00 008:083.641 JLINK_WriteReg(R9, 0x2000045C)
T4A00 008:083.645 - 0.003ms returns 0
T4A00 008:083.649 JLINK_WriteReg(R10, 0x00000000)
T4A00 008:083.653 - 0.003ms returns 0
T4A00 008:083.657 JLINK_WriteReg(R11, 0x00000000)
T4A00 008:083.661 - 0.004ms returns 0
T4A00 008:083.666 JLINK_WriteReg(R12, 0x00000000)
T4A00 008:083.670 - 0.003ms returns 0
T4A00 008:083.675 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 008:083.680 - 0.005ms returns 0
T4A00 008:083.685 JLINK_WriteReg(R14, 0x20000001)
T4A00 008:083.689 - 0.003ms returns 0
T4A00 008:083.693 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 008:083.697 - 0.004ms returns 0
T4A00 008:083.701 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 008:083.705 - 0.003ms returns 0
T4A00 008:083.709 JLINK_WriteReg(MSP, 0x20001000)
T4A00 008:083.713 - 0.003ms returns 0
T4A00 008:083.717 JLINK_WriteReg(PSP, 0x20001000)
T4A00 008:083.721 - 0.003ms returns 0
T4A00 008:083.725 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 008:083.729 - 0.003ms returns 0
T4A00 008:083.737 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 008:083.742 - 0.005ms returns 0x0000002F
T4A00 008:083.746 JLINK_Go()
T4A00 008:083.758   CPU_ReadMem(4 bytes @ 0x********)
T4A00 008:107.097 - 23.349ms
T4A00 008:107.174 JLINK_IsHalted()
T4A00 008:109.593 - 2.417ms returns FALSE
T4A00 008:109.685 JLINK_HasError()
T4A00 008:115.898 JLINK_IsHalted()
T4A00 008:141.223   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 008:143.566 - 27.668ms returns TRUE
T4A00 008:143.625 JLINK_ReadReg(R15 (PC))
T4A00 008:143.640 - 0.015ms returns 0x20000000
T4A00 008:143.645 JLINK_ClrBPEx(BPHandle = 0x0000002F)
T4A00 008:143.649 - 0.004ms returns 0x00
T4A00 008:143.654 JLINK_ReadReg(R0)
T4A00 008:143.658 - 0.003ms returns 0x00001200
T4A00 008:144.117 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 008:144.133   Data:  A5 A5 00 00 F0 B5 83 B0 16 46 0D 46 C7 68 00 21 ...
T4A00 008:144.145   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 008:172.090 - 27.972ms returns 0x200
T4A00 008:172.185 JLINK_HasError()
T4A00 008:172.199 JLINK_WriteReg(R0, 0x00001200)
T4A00 008:172.213 - 0.013ms returns 0
T4A00 008:172.220 JLINK_WriteReg(R1, 0x00000200)
T4A00 008:172.225 - 0.006ms returns 0
T4A00 008:172.232 JLINK_WriteReg(R2, 0x20000460)
T4A00 008:172.237 - 0.005ms returns 0
T4A00 008:172.245 JLINK_WriteReg(R3, 0x00000000)
T4A00 008:172.250 - 0.005ms returns 0
T4A00 008:172.257 JLINK_WriteReg(R4, 0x00000000)
T4A00 008:172.263 - 0.006ms returns 0
T4A00 008:172.269 JLINK_WriteReg(R5, 0x00000000)
T4A00 008:172.275 - 0.005ms returns 0
T4A00 008:172.281 JLINK_WriteReg(R6, 0x00000000)
T4A00 008:172.287 - 0.005ms returns 0
T4A00 008:172.293 JLINK_WriteReg(R7, 0x00000000)
T4A00 008:172.300 - 0.006ms returns 0
T4A00 008:172.405 JLINK_WriteReg(R8, 0x00000000)
T4A00 008:172.416 - 0.010ms returns 0
T4A00 008:172.422 JLINK_WriteReg(R9, 0x2000045C)
T4A00 008:172.426 - 0.004ms returns 0
T4A00 008:172.432 JLINK_WriteReg(R10, 0x00000000)
T4A00 008:172.437 - 0.004ms returns 0
T4A00 008:172.441 JLINK_WriteReg(R11, 0x00000000)
T4A00 008:172.446 - 0.004ms returns 0
T4A00 008:172.452 JLINK_WriteReg(R12, 0x00000000)
T4A00 008:172.457 - 0.004ms returns 0
T4A00 008:172.461 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 008:172.468 - 0.006ms returns 0
T4A00 008:172.473 JLINK_WriteReg(R14, 0x20000001)
T4A00 008:172.477 - 0.004ms returns 0
T4A00 008:172.483 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 008:172.488 - 0.005ms returns 0
T4A00 008:172.494 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 008:172.501 - 0.006ms returns 0
T4A00 008:172.507 JLINK_WriteReg(MSP, 0x20001000)
T4A00 008:172.512 - 0.004ms returns 0
T4A00 008:172.517 JLINK_WriteReg(PSP, 0x20001000)
T4A00 008:172.521 - 0.004ms returns 0
T4A00 008:172.526 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 008:172.531 - 0.004ms returns 0
T4A00 008:172.537 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 008:172.545 - 0.007ms returns 0x00000030
T4A00 008:172.549 JLINK_Go()
T4A00 008:172.564   CPU_ReadMem(4 bytes @ 0x********)
T4A00 008:196.497 - 23.946ms
T4A00 008:196.587 JLINK_IsHalted()
T4A00 008:199.187 - 2.599ms returns FALSE
T4A00 008:199.258 JLINK_HasError()
T4A00 008:200.990 JLINK_IsHalted()
T4A00 008:225.833   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 008:228.585 - 27.589ms returns TRUE
T4A00 008:228.675 JLINK_ReadReg(R15 (PC))
T4A00 008:228.686 - 0.011ms returns 0x20000000
T4A00 008:228.693 JLINK_ClrBPEx(BPHandle = 0x00000030)
T4A00 008:228.698 - 0.005ms returns 0x00
T4A00 008:228.703 JLINK_ReadReg(R0)
T4A00 008:228.707 - 0.004ms returns 0x00001400
T4A00 008:229.193 JLINK_WriteMem(0x20000460, 0x200 Bytes, ...)
T4A00 008:229.201   Data:  00 96 09 49 09 4B 20 46 2A 46 FF F7 51 FD FF F7 ...
T4A00 008:229.215   CPU_WriteMem(512 bytes @ 0x20000460)
T4A00 008:481.250 - 252.055ms returns 0x200
T4A00 008:481.322 JLINK_HasError()
T4A00 008:481.333 JLINK_WriteReg(R0, 0x00001400)
T4A00 008:481.342 - 0.009ms returns 0
T4A00 008:481.346 JLINK_WriteReg(R1, 0x00000090)
T4A00 008:481.350 - 0.003ms returns 0
T4A00 008:481.355 JLINK_WriteReg(R2, 0x20000460)
T4A00 008:481.362 - 0.007ms returns 0
T4A00 008:481.367 JLINK_WriteReg(R3, 0x00000000)
T4A00 008:481.371 - 0.003ms returns 0
T4A00 008:481.375 JLINK_WriteReg(R4, 0x00000000)
T4A00 008:481.379 - 0.003ms returns 0
T4A00 008:481.383 JLINK_WriteReg(R5, 0x00000000)
T4A00 008:481.387 - 0.003ms returns 0
T4A00 008:481.399 JLINK_WriteReg(R6, 0x00000000)
T4A00 008:481.403 - 0.003ms returns 0
T4A00 008:481.407 JLINK_WriteReg(R7, 0x00000000)
T4A00 008:481.411 - 0.003ms returns 0
T4A00 008:481.416 JLINK_WriteReg(R8, 0x00000000)
T4A00 008:481.419 - 0.003ms returns 0
T4A00 008:481.424 JLINK_WriteReg(R9, 0x2000045C)
T4A00 008:481.427 - 0.003ms returns 0
T4A00 008:481.432 JLINK_WriteReg(R10, 0x00000000)
T4A00 008:481.435 - 0.003ms returns 0
T4A00 008:481.440 JLINK_WriteReg(R11, 0x00000000)
T4A00 008:481.443 - 0.003ms returns 0
T4A00 008:481.450 JLINK_WriteReg(R12, 0x00000000)
T4A00 008:481.453 - 0.004ms returns 0
T4A00 008:481.458 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 008:481.462 - 0.004ms returns 0
T4A00 008:481.466 JLINK_WriteReg(R14, 0x20000001)
T4A00 008:481.470 - 0.003ms returns 0
T4A00 008:481.475 JLINK_WriteReg(R15 (PC), 0x20000410)
T4A00 008:481.478 - 0.004ms returns 0
T4A00 008:481.483 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 008:481.487 - 0.003ms returns 0
T4A00 008:481.491 JLINK_WriteReg(MSP, 0x20001000)
T4A00 008:481.495 - 0.003ms returns 0
T4A00 008:481.499 JLINK_WriteReg(PSP, 0x20001000)
T4A00 008:481.503 - 0.003ms returns 0
T4A00 008:481.507 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 008:481.511 - 0.003ms returns 0
T4A00 008:481.517 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 008:481.522 - 0.006ms returns 0x00000031
T4A00 008:481.526 JLINK_Go()
T4A00 008:481.539   CPU_ReadMem(4 bytes @ 0x********)
T4A00 008:504.995 - 23.467ms
T4A00 008:505.037 JLINK_IsHalted()
T4A00 008:529.635   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 008:531.919 - 26.880ms returns TRUE
T4A00 008:531.994 JLINK_ReadReg(R15 (PC))
T4A00 008:532.005 - 0.011ms returns 0x20000000
T4A00 008:532.018 JLINK_ClrBPEx(BPHandle = 0x00000031)
T4A00 008:532.024 - 0.006ms returns 0x00
T4A00 008:532.031 JLINK_ReadReg(R0)
T4A00 008:532.037 - 0.006ms returns 0x00001490
T4A00 008:532.642 JLINK_HasError()
T4A00 008:532.655 JLINK_WriteReg(R0, 0x00000003)
T4A00 008:532.662 - 0.007ms returns 0
T4A00 008:532.670 JLINK_WriteReg(R1, 0x00000090)
T4A00 008:532.675 - 0.006ms returns 0
T4A00 008:532.682 JLINK_WriteReg(R2, 0x20000460)
T4A00 008:532.688 - 0.005ms returns 0
T4A00 008:532.694 JLINK_WriteReg(R3, 0x00000000)
T4A00 008:532.699 - 0.005ms returns 0
T4A00 008:532.706 JLINK_WriteReg(R4, 0x00000000)
T4A00 008:532.712 - 0.005ms returns 0
T4A00 008:532.718 JLINK_WriteReg(R5, 0x00000000)
T4A00 008:532.723 - 0.005ms returns 0
T4A00 008:532.730 JLINK_WriteReg(R6, 0x00000000)
T4A00 008:532.735 - 0.005ms returns 0
T4A00 008:532.742 JLINK_WriteReg(R7, 0x00000000)
T4A00 008:532.747 - 0.005ms returns 0
T4A00 008:532.754 JLINK_WriteReg(R8, 0x00000000)
T4A00 008:532.759 - 0.005ms returns 0
T4A00 008:532.766 JLINK_WriteReg(R9, 0x2000045C)
T4A00 008:532.771 - 0.005ms returns 0
T4A00 008:532.778 JLINK_WriteReg(R10, 0x00000000)
T4A00 008:532.783 - 0.005ms returns 0
T4A00 008:532.790 JLINK_WriteReg(R11, 0x00000000)
T4A00 008:532.795 - 0.005ms returns 0
T4A00 008:532.802 JLINK_WriteReg(R12, 0x00000000)
T4A00 008:532.807 - 0.005ms returns 0
T4A00 008:532.814 JLINK_WriteReg(R13 (SP), 0x20001000)
T4A00 008:532.820 - 0.006ms returns 0
T4A00 008:532.826 JLINK_WriteReg(R14, 0x20000001)
T4A00 008:532.832 - 0.005ms returns 0
T4A00 008:532.838 JLINK_WriteReg(R15 (PC), 0x200003C8)
T4A00 008:532.844 - 0.005ms returns 0
T4A00 008:532.850 JLINK_WriteReg(XPSR, 0x01000000)
T4A00 008:532.856 - 0.005ms returns 0
T4A00 008:532.862 JLINK_WriteReg(MSP, 0x20001000)
T4A00 008:532.868 - 0.005ms returns 0
T4A00 008:532.874 JLINK_WriteReg(PSP, 0x20001000)
T4A00 008:532.880 - 0.005ms returns 0
T4A00 008:532.886 JLINK_WriteReg(CFBP, 0x00000000)
T4A00 008:532.892 - 0.005ms returns 0
T4A00 008:532.899 JLINK_SetBPEx(Addr = 0x20000000, Type = 0xFFFFFFF2)
T4A00 008:532.910 - 0.011ms returns 0x00000032
T4A00 008:532.917 JLINK_Go()
T4A00 008:532.929   CPU_ReadMem(4 bytes @ 0x********)
T4A00 008:555.942 - 23.024ms
T4A00 008:555.990 JLINK_IsHalted()
T4A00 008:580.724   CPU_ReadMem(2 bytes @ 0x20000000)
T4A00 008:582.538 - 26.547ms returns TRUE
T4A00 008:582.782 JLINK_ReadReg(R15 (PC))
T4A00 008:582.798 - 0.015ms returns 0x20000000
T4A00 008:582.803 JLINK_ClrBPEx(BPHandle = 0x00000032)
T4A00 008:582.808 - 0.004ms returns 0x00
T4A00 008:582.813 JLINK_ReadReg(R0)
T4A00 008:582.817 - 0.004ms returns 0x00000000
T4A00 008:637.818 JLINK_WriteMemEx(0x20000000, 0x00000002 Bytes, Flags = 0x02000000)
T4A00 008:637.838   Data:  FE E7
T4A00 008:637.859   CPU_WriteMem(2 bytes @ 0x20000000)
T4A00 008:639.744 - 1.925ms returns 0x2
T4A00 008:639.774 JLINK_HasError()
T4A00 008:639.781 JLINK_HasError()
T4A00 008:639.786 JLINK_SetResetType(JLINKARM_CM3_RESET_TYPE_NORMAL)
T4A00 008:639.790 - 0.004ms returns JLINKARM_CM3_RESET_TYPE_NORMAL
T4A00 008:639.795 JLINK_Reset()
T4A00 008:641.218   CPU_ReadMem(4 bytes @ 0x20000460)
T4A00 008:642.956   CPU_WriteMem(4 bytes @ 0x20000460)
T4A00 008:646.298   Memory map 'before startup completion point' is active
T4A00 008:646.315   CPU_WriteMem(4 bytes @ 0xE000EDF0)
T4A00 008:648.134   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T4A00 008:651.594   Reset: Halt core after reset via DEMCR.VC_CORERESET.
T4A00 008:655.124   Reset: Reset device via AIRCR.SYSRESETREQ.
T4A00 008:655.140   CPU_WriteMem(4 bytes @ 0xE000ED0C)
T4A00 008:712.298   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T4A00 008:714.124   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T4A00 008:715.967   CPU_WriteMem(4 bytes @ 0xE000EDFC)
T4A00 008:725.312   CPU_ReadMem(4 bytes @ 0xE000EDF0)
T4A00 008:751.841   CPU_WriteMem(4 bytes @ 0x********)
T4A00 008:753.686   CPU_ReadMem(4 bytes @ 0xE000EDFC)
T4A00 008:755.432   CPU_ReadMem(4 bytes @ 0x********)
T4A00 008:757.206   CPU_WriteMem(4 bytes @ 0x********)
T4A00 008:759.108 - 119.312ms
T4A00 008:759.143 JLINK_Go()
T4A00 008:759.161   CPU_ReadMem(4 bytes @ 0x********)
T4A00 008:761.070   CPU_WriteMem(4 bytes @ 0x********)
T4A00 008:762.908   CPU_WriteMem(4 bytes @ 0xE0002008)
T4A00 008:762.926   CPU_WriteMem(4 bytes @ 0xE000200C)
T4A00 008:762.934   CPU_WriteMem(4 bytes @ 0xE0002010)
T4A00 008:762.943   CPU_WriteMem(4 bytes @ 0xE0002014)
T4A00 008:769.014   CPU_WriteMem(4 bytes @ 0xE0001004)
T4A00 008:776.299   Memory map 'after startup completion point' is active
T4A00 008:776.314 - 17.170ms
T4A00 008:782.146 JLINK_Close()
T4A00 009:001.394   CPU is running
T4A00 009:001.426   CPU_WriteMem(4 bytes @ 0xE0002008)
T4A00 009:003.396   CPU is running
T4A00 009:003.411   CPU_WriteMem(4 bytes @ 0xE000200C)
T4A00 009:005.370   CPU is running
T4A00 009:005.384   CPU_WriteMem(4 bytes @ 0xE0002010)
T4A00 009:007.542   CPU is running
T4A00 009:007.557   CPU_WriteMem(4 bytes @ 0xE0002014)
T4A00 009:013.230 - 231.083ms
T4A00 009:013.254   
T4A00 009:013.262   Closed
